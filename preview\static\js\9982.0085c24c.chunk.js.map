{"version": 3, "file": "static/js/9982.0085c24c.chunk.js", "mappings": ";+HAAA,IAAIA,EAAYC,OAAOC,OACnB,SAAkBC,GACd,MAAwB,kBAAVA,GAAsBA,IAAUA,CACjD,EAUL,SAASC,EAAeC,EAAWC,GAC/B,GAAID,EAAUE,SAAWD,EAAWC,OAChC,OAAO,EAEX,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUE,OAAQC,IAClC,GAdSC,EAcIJ,EAAUG,GAdPE,EAcWJ,EAAWE,KAbtCC,IAAUC,GAGVV,EAAUS,IAAUT,EAAUU,IAW1B,OAAO,EAfnB,IAAiBD,EAAOC,EAkBpB,OAAO,CACV,CAyBD,IAvBA,SAAoBC,EAAUC,GAE1B,IAAIC,OADY,IAAZD,IAAsBA,EAAUR,GAEpC,IACIU,EADAC,EAAW,GAEXC,GAAa,EAejB,OAdA,WAEI,IADA,IAAIC,EAAU,GACLC,EAAK,EAAGA,EAAKC,UAAUZ,OAAQW,IACpCD,EAAQC,GAAMC,UAAUD,GAE5B,OAAIF,GAAcH,IAAaO,MAAQR,EAAQK,EAASF,KAGxDD,EAAaH,EAASU,MAAMD,KAAMH,GAClCD,GAAa,EACbH,EAAWO,KACXL,EAAWE,GALAH,CAOd,CAEJ,4JCrCD,SAASQ,EAAuBC,GAC9B,MAAO,yBAA2BA,EAAO,4CAA8CA,EAAhF,iFACR,CAGD,IAAIC,EACuB,oBAAXC,QAAyBA,OAAOC,YAAc,eAS1DC,EAAe,WACjB,OAAOC,KAAKC,SAASC,SAAS,IAAIC,UAAU,GAAGC,MAAM,IAAIC,KAAK,IAC/D,EAEGC,EAAc,CAChBC,KAAM,eAAiBR,IACvBS,QAAS,kBAAoBT,IAC7BU,qBAAsB,WACpB,MAAO,+BAAiCV,GACzC,GAOH,SAASW,EAAcC,GACrB,GAAmB,kBAARA,GAA4B,OAARA,EAAc,OAAO,EAGpD,IAFA,IAAIC,EAAQD,EAE4B,OAAjCE,OAAOC,eAAeF,IAC3BA,EAAQC,OAAOC,eAAeF,GAGhC,OAAOC,OAAOC,eAAeH,KAASC,CACvC,CAuFD,SAASG,EAAYC,EAASC,EAAgBC,GAC5C,IAAIC,EAEJ,GAA8B,oBAAnBF,GAAqD,oBAAbC,GAA+C,oBAAbA,GAAmD,oBAAjB3B,UAAU,GAC/H,MAAM,IAAI6B,MAA8C1B,EAAuB,IAQjF,GAL8B,oBAAnBuB,GAAqD,qBAAbC,IACjDA,EAAWD,EACXA,OAAiBI,GAGK,qBAAbH,EAA0B,CACnC,GAAwB,oBAAbA,EACT,MAAM,IAAIE,MAA8C1B,EAAuB,IAGjF,OAAOwB,EAASH,EAATG,CAAsBF,EAASC,EACvC,CAED,GAAuB,oBAAZD,EACT,MAAM,IAAII,MAA8C1B,EAAuB,IAGjF,IAAI4B,EAAiBN,EACjBO,EAAeN,EACfO,EAAmB,GACnBC,EAAgBD,EAChBE,GAAgB,EASpB,SAASC,IACHF,IAAkBD,IACpBC,EAAgBD,EAAiBI,QAEpC,CAQD,SAASC,IACP,GAAIH,EACF,MAAM,IAAIN,MAA8C1B,EAAuB,IAGjF,OAAO6B,CACR,CA0BD,SAASO,EAAUC,GACjB,GAAwB,oBAAbA,EACT,MAAM,IAAIX,MAA8C1B,EAAuB,IAGjF,GAAIgC,EACF,MAAM,IAAIN,MAA8C1B,EAAuB,IAGjF,IAAIsC,GAAe,EAGnB,OAFAL,IACAF,EAAcQ,KAAKF,GACZ,WACL,GAAKC,EAAL,CAIA,GAAIN,EACF,MAAM,IAAIN,MAA8C1B,EAAuB,IAGjFsC,GAAe,EACfL,IACA,IAAIO,EAAQT,EAAcU,QAAQJ,GAClCN,EAAcW,OAAOF,EAAO,GAC5BV,EAAmB,IAVlB,CAWF,CACF,CA4BD,SAASa,EAASC,GAChB,IAAK5B,EAAc4B,GACjB,MAAM,IAAIlB,MAA8C1B,EAAuB,IAGjF,GAA2B,qBAAhB4C,EAAOC,KAChB,MAAM,IAAInB,MAA8C1B,EAAuB,IAGjF,GAAIgC,EACF,MAAM,IAAIN,MAA8C1B,EAAuB,IAGjF,IACEgC,GAAgB,EAChBH,EAAeD,EAAeC,EAAce,EAG7C,CALD,QAIEZ,GAAgB,CACjB,CAID,IAFA,IAAIc,EAAYhB,EAAmBC,EAE1B7C,EAAI,EAAGA,EAAI4D,EAAU7D,OAAQC,IAAK,EAEzCmD,EADeS,EAAU5D,KAE1B,CAED,OAAO0D,CACR,CAaD,SAASG,EAAeC,GACtB,GAA2B,oBAAhBA,EACT,MAAM,IAAItB,MAA8C1B,EAAuB,KAGjF4B,EAAiBoB,EAKjBL,EAAS,CACPE,KAAMjC,EAAYE,SAErB,CASD,SAASV,IACP,IAAI6C,EAEAC,EAAiBd,EACrB,OAAOa,EAAO,CASZb,UAAW,SAAmBe,GAC5B,GAAwB,kBAAbA,GAAsC,OAAbA,EAClC,MAAM,IAAIzB,MAA8C1B,EAAuB,KAGjF,SAASoD,IACHD,EAASE,MACXF,EAASE,KAAKlB,IAEjB,CAID,OAFAiB,IAEO,CACLE,YAFgBJ,EAAeE,GAIlC,IACKlD,GAAgB,WACtB,OAAOJ,IACR,EAAEmD,CACJ,CAQD,OAHAN,EAAS,CACPE,KAAMjC,EAAYC,QAEbY,EAAQ,CACbkB,SAAUA,EACVP,UAAWA,EACXD,SAAUA,EACVY,eAAgBA,IACT7C,GAAgBE,EAAYqB,CACtC,CAiMD,SAAS8B,EAAkBC,EAAeb,GACxC,OAAO,WACL,OAAOA,EAASa,EAAczD,MAAMD,KAAMD,WAC3C,CACF,CAwBD,SAAS4D,EAAmBC,EAAgBf,GAC1C,GAA8B,oBAAnBe,EACT,OAAOH,EAAkBG,EAAgBf,GAG3C,GAA8B,kBAAnBe,GAAkD,OAAnBA,EACxC,MAAM,IAAIhC,MAA8C1B,EAAuB,KAGjF,IAAI2D,EAAsB,CAAC,EAE3B,IAAK,IAAIC,KAAOF,EAAgB,CAC9B,IAAIF,EAAgBE,EAAeE,GAEN,oBAAlBJ,IACTG,EAAoBC,GAAOL,EAAkBC,EAAeb,GAE/D,CAED,OAAOgB,CACR,CAYD,SAASE,IACP,IAAK,IAAIC,EAAOjE,UAAUZ,OAAQ8E,EAAQ,IAAIC,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAChFF,EAAME,GAAQpE,UAAUoE,GAG1B,OAAqB,IAAjBF,EAAM9E,OACD,SAAUiF,GACf,OAAOA,CACR,EAGkB,IAAjBH,EAAM9E,OACD8E,EAAM,GAGRA,EAAMI,QAAO,SAAUC,EAAGC,GAC/B,OAAO,WACL,OAAOD,EAAEC,EAAEtE,WAAM,EAAQF,WAC1B,CACF,GACF,CC9oBM,IAAIyE,EAAiCC,EAAAA,cAAoB,MCIhE,IAAIC,EAJJ,SAA0BC,GACxBA,GACD,EAQUC,EAAW,WACpB,OAAOF,CACR,ECuDD,IAAIG,EAAgB,CAClBC,OAAQ,WAAoB,EAC5BC,IAAK,WACH,MAAO,EACR,GAEI,SAASC,EAAmBC,EAAOC,GACxC,IAAI1B,EACAR,EAAY6B,EAWhB,SAASM,IACHC,EAAaC,eACfD,EAAaC,eAEhB,CAMD,SAASC,IACF9B,IACHA,EAAc0B,EAAYA,EAAUK,aAAaJ,GAAuBF,EAAM3C,UAAU6C,GACxFnC,EAhGN,WACE,IAAI0B,EAAQE,IACRvF,EAAQ,KACRmG,EAAO,KACX,MAAO,CACLC,MAAO,WACLpG,EAAQ,KACRmG,EAAO,IACR,EACDV,OAAQ,WACNJ,GAAM,WAGJ,IAFA,IAAInC,EAAWlD,EAERkD,GACLA,EAASoC,WACTpC,EAAWA,EAASgB,IAEvB,GACF,EACDwB,IAAK,WAIH,IAHA,IAAI/B,EAAY,GACZT,EAAWlD,EAERkD,GACLS,EAAUP,KAAKF,GACfA,EAAWA,EAASgB,KAGtB,OAAOP,CACR,EACDV,UAAW,SAAmBqC,GAC5B,IAAInC,GAAe,EACfD,EAAWiD,EAAO,CACpBb,SAAUA,EACVpB,KAAM,KACNmC,KAAMF,GASR,OANIjD,EAASmD,KACXnD,EAASmD,KAAKnC,KAAOhB,EAErBlD,EAAQkD,EAGH,WACAC,GAA0B,OAAVnD,IACrBmD,GAAe,EAEXD,EAASgB,KACXhB,EAASgB,KAAKmC,KAAOnD,EAASmD,KAE9BF,EAAOjD,EAASmD,KAGdnD,EAASmD,KACXnD,EAASmD,KAAKnC,KAAOhB,EAASgB,KAE9BlE,EAAQkD,EAASgB,KAEpB,CACF,EAEJ,CAkCiBoC,GAEf,CAWD,IAAIP,EAAe,CACjBG,aApCF,SAAsBhD,GAEpB,OADA+C,IACOtC,EAAUV,UAAUC,EAC5B,EAkCCqD,iBAhCF,WACE5C,EAAU8B,QACX,EA+BCK,oBAAqBA,EACrB3C,aAxBF,WACE,OAAOqD,QAAQrC,EAChB,EAuBC8B,aAAcA,EACdQ,eAfF,WACMtC,IACFA,IACAA,OAAc3B,EACdmB,EAAUyC,QACVzC,EAAY6B,EAEf,EASCkB,aAAc,WACZ,OAAO/C,CACR,GAEH,OAAOoC,CACR,CCpHM,IAAIY,EAA8C,qBAAXC,QAAqD,qBAApBA,OAAOC,UAAqE,qBAAlCD,OAAOC,SAASC,cAAgCC,EAAAA,gBAAkBC,EAAAA,UC2C3L,MA9CA,SAAkBlD,GAChB,IAAI8B,EAAQ9B,EAAK8B,MACbqB,EAAUnD,EAAKmD,QACfC,EAAWpD,EAAKoD,SAChBC,GAAeC,EAAAA,EAAAA,UAAQ,WACzB,IAAIrB,EAAeJ,EAAmBC,GACtC,MAAO,CACLA,MAAOA,EACPG,aAAcA,EAEjB,GAAE,CAACH,IACAyB,GAAgBD,EAAAA,EAAAA,UAAQ,WAC1B,OAAOxB,EAAM5C,UACd,GAAE,CAAC4C,IACJe,GAA0B,WACxB,IAAIZ,EAAeoB,EAAapB,aAQhC,OAPAA,EAAaC,cAAgBD,EAAaQ,iBAC1CR,EAAaE,eAEToB,IAAkBzB,EAAM5C,YAC1B+C,EAAaQ,mBAGR,WACLR,EAAaU,iBACbV,EAAaC,cAAgB,IAC9B,CACF,GAAE,CAACmB,EAAcE,IAClB,IAAIC,EAAUL,GAAW9B,EACzB,OAAoBC,EAAAA,cAAoBkC,EAAQC,SAAU,CACxD7H,MAAOyH,GACND,EACJ,4CCpCGM,EAAY,CAAC,iBAAkB,aAAc,kBAAmB,2BAA4B,WAAY,UAAW,aAAc,WACjIC,EAAa,CAAC,0BAQdC,EAAc,GACdC,EAAwB,CAAC,KAAM,MAUnC,SAASC,EAAyBC,EAAOpE,GACvC,IAAIqE,EAAcD,EAAM,GACxB,MAAO,CAACpE,EAAOsE,QAASD,EAAc,EACvC,CAED,SAASE,EAAkCC,EAAYC,EAAYC,GACjExB,GAA0B,WACxB,OAAOsB,EAAWrH,WAAM,EAAQsH,EACjC,GAAEC,EACJ,CAED,SAASC,EAAoBC,EAAkBC,EAAgBC,EAAmBC,EAAcC,EAAkBC,EAA2BnC,GAE3I8B,EAAiBM,QAAUH,EAC3BF,EAAeK,QAAUF,EACzBF,EAAkBI,SAAU,EAExBD,EAA0BC,UAC5BD,EAA0BC,QAAU,KACpCpC,IAEH,CAED,SAASqC,EAAiBC,EAA0BjD,EAAOG,EAAc+C,EAAoBT,EAAkBC,EAAgBC,EAAmBG,EAA2BnC,EAAkBwC,GAE7L,GAAKF,EAAL,CAEA,IAAIG,GAAiB,EACjBC,EAAkB,KAElBC,EAAkB,WACpB,IAAIF,EAAJ,CAMA,IACIG,EAAeC,EADfC,EAAmBzD,EAAM5C,WAG7B,IAGEmG,EAAgBL,EAAmBO,EAAkBhB,EAAiBM,QAIvE,CAHC,MAAOW,GACPF,EAAQE,EACRL,EAAkBK,CACnB,CAEIF,IACHH,EAAkB,MAIhBE,IAAkBb,EAAeK,QAC9BJ,EAAkBI,SACrBpC,KAOF+B,EAAeK,QAAUQ,EACzBT,EAA0BC,QAAUQ,EACpCZ,EAAkBI,SAAU,EAE5BI,EAA6B,CAC3BrF,KAAM,gBACNqE,QAAS,CACPqB,MAAOA,KAnCZ,CAuCF,EAGDrD,EAAaC,cAAgBkD,EAC7BnD,EAAaE,eAGbiD,IAiBA,OAfyB,WAKvB,GAJAF,GAAiB,EACjBjD,EAAaU,iBACbV,EAAaC,cAAgB,KAEzBiD,EAMF,MAAMA,CAET,CAvEoC,CA0EtC,CAED,IAAIM,EAAmB,WACrB,MAAO,CAAC,KAAM,EACf,EAEc,SAASC,EAexBC,EACA3F,QACe,IAATA,IACFA,EAAO,CAAC,GAGV,IAAIxB,EAAQwB,EACR4F,EAAuBpH,EAAMqH,eAC7BA,OAA0C,IAAzBD,EAAkC,SAAUE,GAC/D,MAAO,mBAAqBA,EAAO,GACpC,EAAGF,EACAG,EAAmBvH,EAAMwH,WACzBA,OAAkC,IAArBD,EAA8B,kBAAoBA,EAC/DE,EAAwBzH,EAAM0H,gBAC9BA,OAA4C,IAA1BD,OAAmCvH,EAAYuH,EACjEE,EAAwB3H,EAAMuG,yBAC9BA,OAAqD,IAA1BoB,GAA0CA,EACrEC,EAAiB5H,EAAM6H,SACvBA,OAA8B,IAAnBD,EAA4B,QAAUA,EAGjDE,GAFgB9H,EAAM+H,QAEH/H,EAAMgI,YACzBA,OAAkC,IAArBF,GAAsCA,EACnDG,EAAgBjI,EAAM2E,QACtBA,OAA4B,IAAlBsD,EAA2BpF,EAAoBoF,EACzDC,GAAiBC,EAAAA,EAAAA,GAA8BnI,EAAOkF,GAkBtDF,EAAUL,EACd,OAAO,SAAyByD,GAK9B,IAAIC,EAAuBD,EAAiBE,aAAeF,EAAiBd,MAAQ,YAChFgB,EAAcjB,EAAegB,GAE7BE,GAAyBC,EAAAA,EAAAA,GAAS,CAAC,EAAGN,EAAgB,CACxDb,eAAgBA,EAChBG,WAAYA,EACZE,gBAAiBA,EACjBnB,yBAA0BA,EAC1BsB,SAAUA,EACVS,YAAaA,EACbD,qBAAsBA,EACtBD,iBAAkBA,IAGhBK,EAAOP,EAAeO,KAS1B,IAAIC,EAAkBD,EAAO3D,EAAAA,QAAU,SAAU9B,GAC/C,OAAOA,GACR,EAED,SAAS2F,EAAgBC,GACvB,IAAIC,GAAW/D,EAAAA,EAAAA,UAAQ,WAIrB,IAAIgE,EAAyBF,EAAME,uBAC/B5C,GAAeiC,EAAAA,EAAAA,GAA8BS,EAAOzD,GAExD,MAAO,CAACyD,EAAMjE,QAASmE,EAAwB5C,EAChD,GAAE,CAAC0C,IACAG,EAAeF,EAAS,GACxBC,EAAyBD,EAAS,GAClC3C,EAAe2C,EAAS,GAExBG,GAAelE,EAAAA,EAAAA,UAAQ,WAGzB,OAAOiE,GAAgBA,EAAaE,WAAYC,EAAAA,EAAAA,mBAAgCpG,EAAAA,cAAoBiG,EAAaE,SAAU,OAASF,EAAe/D,CACpJ,GAAE,CAAC+D,EAAc/D,IAEdH,GAAesE,EAAAA,EAAAA,YAAWH,GAI1BI,EAAwBlF,QAAQ0E,EAAMtF,QAAUY,QAAQ0E,EAAMtF,MAAM5C,WAAawD,QAAQ0E,EAAMtF,MAAMpC,UAC3EgD,QAAQW,IAAiBX,QAAQW,EAAavB,OAO5E,IAAIA,EAAQ8F,EAAwBR,EAAMtF,MAAQuB,EAAavB,MAC3DkD,GAAqB1B,EAAAA,EAAAA,UAAQ,WAG/B,OA/CJ,SAA6BxB,GAC3B,OAAO6D,EAAgB7D,EAAMpC,SAAUqH,EACxC,CA6CUc,CAAoB/F,EAC5B,GAAE,CAACA,IAEAgG,GAAYxE,EAAAA,EAAAA,UAAQ,WACtB,IAAKyB,EAA0B,OAAOlB,EAKtC,IAAI5B,EAAeJ,EAAmBC,EAAO8F,EAAwB,KAAOvE,EAAapB,cASrFQ,EAAmBR,EAAaQ,iBAAiBsF,KAAK9F,GAC1D,MAAO,CAACA,EAAcQ,EACvB,GAAE,CAACX,EAAO8F,EAAuBvE,IAC9BpB,EAAe6F,EAAU,GACzBrF,EAAmBqF,EAAU,GAI7BE,GAAyB1E,EAAAA,EAAAA,UAAQ,WACnC,OAAIsE,EAIKvE,GAKF2D,EAAAA,EAAAA,GAAS,CAAC,EAAG3D,EAAc,CAChCpB,aAAcA,GAEjB,GAAE,CAAC2F,EAAuBvE,EAAcpB,IAGrCgG,GAAcC,EAAAA,EAAAA,YAAWpE,EAA0BF,EAAa6B,GAEhE0C,EADeF,EAAY,GACc,GACzChD,EAA+BgD,EAAY,GAG/C,GAAIE,GAA6BA,EAA0B7C,MACzD,MAAM6C,EAA0B7C,MAIlC,IAAId,GAAiB4D,EAAAA,EAAAA,UACjB7D,GAAmB6D,EAAAA,EAAAA,QAAO1D,GAC1BE,GAA4BwD,EAAAA,EAAAA,UAC5B3D,GAAoB2D,EAAAA,EAAAA,SAAO,GAC3BzD,EAAmBuC,GAAgB,WAOrC,OAAItC,EAA0BC,SAAWH,IAAiBH,EAAiBM,QAClED,EAA0BC,QAO5BG,EAAmBlD,EAAM5C,WAAYwF,EAC7C,GAAE,CAAC5C,EAAOqG,EAA2BzD,IAItCR,EAAkCI,EAAqB,CAACC,EAAkBC,EAAgBC,EAAmBC,EAAcC,EAAkBC,EAA2BnC,IAExKyB,EAAkCY,EAAkB,CAACC,EAA0BjD,EAAOG,EAAc+C,EAAoBT,EAAkBC,EAAgBC,EAAmBG,EAA2BnC,EAAkBwC,GAA+B,CAACnD,EAAOG,EAAc+C,IAG/Q,IAAIqD,GAA2B/E,EAAAA,EAAAA,UAAQ,WACrC,OAAoBhC,EAAAA,cAAoBsF,GAAkBI,EAAAA,EAAAA,GAAS,CAAC,EAAGrC,EAAkB,CACvF2D,IAAKhB,IAER,GAAE,CAACA,EAAwBV,EAAkBjC,IAe9C,OAZoBrB,EAAAA,EAAAA,UAAQ,WAC1B,OAAIyB,EAIkBzD,EAAAA,cAAoBkG,EAAa/D,SAAU,CAC7D7H,MAAOoM,GACNK,GAGEA,CACR,GAAE,CAACb,EAAca,EAA0BL,GAE7C,CAGD,IAAIO,EAAUtB,EAAO3F,EAAAA,KAAW6F,GAAmBA,EAInD,GAHAoB,EAAQ3B,iBAAmBA,EAC3B2B,EAAQzB,YAAcK,EAAgBL,YAAcA,EAEhDN,EAAY,CACd,IAAIgC,EAAYlH,EAAAA,YAAiB,SAA2B8F,EAAOkB,GACjE,OAAoBhH,EAAAA,cAAoBiH,GAASvB,EAAAA,EAAAA,GAAS,CAAC,EAAGI,EAAO,CACnEE,uBAAwBgB,IAE3B,IAGD,OAFAE,EAAU1B,YAAcA,EACxB0B,EAAU5B,iBAAmBA,EACtB6B,IAAaD,EAAW5B,EAChC,CAED,OAAO6B,IAAaF,EAAS3B,EAC9B,CACF,CCxXD,SAAS8B,EAAGC,EAAGC,GACb,OAAID,IAAMC,EACK,IAAND,GAAiB,IAANC,GAAW,EAAID,IAAM,EAAIC,EAEpCD,IAAMA,GAAKC,IAAMA,CAE3B,CAEc,SAASC,EAAaC,EAAMC,GACzC,GAAIL,EAAGI,EAAMC,GAAO,OAAO,EAE3B,GAAoB,kBAATD,GAA8B,OAATA,GAAiC,kBAATC,GAA8B,OAATA,EAC3E,OAAO,EAGT,IAAIC,EAAQ9K,OAAO+K,KAAKH,GACpBI,EAAQhL,OAAO+K,KAAKF,GACxB,GAAIC,EAAMhN,SAAWkN,EAAMlN,OAAQ,OAAO,EAE1C,IAAK,IAAIC,EAAI,EAAGA,EAAI+M,EAAMhN,OAAQC,IAChC,IAAKiC,OAAOiL,UAAUC,eAAeC,KAAKN,EAAMC,EAAM/M,MAAQyM,EAAGI,EAAKE,EAAM/M,IAAK8M,EAAKC,EAAM/M,KAC1F,OAAO,EAIX,OAAO,CACR,CCzBM,SAASqN,EAAuBC,GACrC,OAAO,SAA8B7J,EAAU8J,GAC7C,IAAIC,EAAWF,EAAY7J,EAAU8J,GAErC,SAASE,IACP,OAAOD,CACR,CAGD,OADAC,EAAiBC,mBAAoB,EAC9BD,CACR,CACF,CAQM,SAASE,EAAqBC,GACnC,OAAwC,OAAjCA,EAAWF,wBAA+DjL,IAAjCmL,EAAWF,kBAAkCjH,QAAQmH,EAAWF,mBAA2C,IAAtBE,EAAW7N,MACjJ,CAaM,SAAS8N,EAAmBD,EAAY7D,GAC7C,OAAO,SAA2BtG,EAAUM,GACxBA,EAAK8G,YAAvB,IAEIiD,EAAQ,SAAyBC,EAAiBC,GACpD,OAAOF,EAAMJ,kBAAoBI,EAAMF,WAAWG,EAAiBC,GAAYF,EAAMF,WAAWG,EACjG,EAoBD,OAjBAD,EAAMJ,mBAAoB,EAE1BI,EAAMF,WAAa,SAAgCG,EAAiBC,GAClEF,EAAMF,WAAaA,EACnBE,EAAMJ,kBAAoBC,EAAqBC,GAC/C,IAAIzC,EAAQ2C,EAAMC,EAAiBC,GASnC,MAPqB,oBAAV7C,IACT2C,EAAMF,WAAazC,EACnB2C,EAAMJ,kBAAoBC,EAAqBxC,GAC/CA,EAAQ2C,EAAMC,EAAiBC,IAI1B7C,CACR,EAEM2C,CACR,CACF,CC9CD,OAfO,SAA0CG,GAC/C,MAAqC,oBAAvBA,EAAoCJ,EAAmBI,QAA4CxL,CAClH,EACM,SAAyCwL,GAC9C,OAAQA,OAIHxL,EAJwB4K,GAAuB,SAAU5J,GAC5D,MAAO,CACLA,SAAUA,EAEb,GACF,EACM,SAAwCwK,GAC7C,OAAOA,GAAoD,kBAAvBA,EAAkCZ,GAAuB,SAAU5J,GACrG,OCdW,SAA4Be,EAAgBf,GACzD,IAAIgB,EAAsB,CAAC,EAEvByJ,EAAQ,SAAexJ,GACzB,IAAIJ,EAAgBE,EAAeE,GAEN,oBAAlBJ,IACTG,EAAoBC,GAAO,WACzB,OAAOjB,EAASa,EAAczD,WAAM,EAAQF,WAC7C,EAEJ,EAED,IAAK,IAAI+D,KAAOF,EACd0J,EAAMxJ,GAGR,OAAOD,CACR,CDJUF,CAAmB0J,EAAoBxK,EAC/C,SAAIhB,CACN,GEPD,OARO,SAAuC0L,GAC5C,MAAkC,oBAApBA,EAAiCN,EAAmBM,QAAsC1L,CACzG,EACM,SAAsC0L,GAC3C,OAAQA,OAEH1L,EAFqB4K,GAAuB,WAC/C,MAAO,CAAC,CACT,GACF,GCNM,SAASe,EAAkBC,EAAYC,EAAeN,GAC3D,OAAOjD,EAAAA,EAAAA,GAAS,CAAC,EAAGiD,EAAUK,EAAYC,EAC3C,CA+BD,OARO,SAAkCC,GACvC,MAA6B,oBAAfA,EAvBT,SAA4BA,GACjC,OAAO,SAA6B9K,EAAUM,GAC1BA,EAAK8G,YAAvB,IAII2D,EAHAxD,EAAOjH,EAAKiH,KACZyD,EAAsB1K,EAAK0K,oBAC3BC,GAAa,EAEjB,OAAO,SAAyBL,EAAYC,EAAeN,GACzD,IAAIW,EAAkBJ,EAAWF,EAAYC,EAAeN,GAU5D,OARIU,EACG1D,GAASyD,EAAoBE,EAAiBH,KAAcA,EAAcG,IAE/ED,GAAa,EACbF,EAAcG,GAITH,CACR,CACF,CACF,CAE2CI,CAAmBL,QAAc9L,CAC5E,EACM,SAAiC8L,GACtC,OAAQA,OAEJ9L,EAFiB,WACnB,OAAO2L,CACR,CACF,GCjCG3G,EAAY,CAAC,sBAAuB,yBAA0B,kBAE3D,SAASoH,EAAgCV,EAAiBF,EAAoBM,EAAY9K,GAC/F,OAAO,SAAkCqE,EAAOkG,GAC9C,OAAOO,EAAWJ,EAAgBrG,EAAOkG,GAAWC,EAAmBxK,EAAUuK,GAAWA,EAC7F,CACF,CACM,SAASc,EAA8BX,EAAiBF,EAAoBM,EAAY9K,EAAUM,GACvG,IAII+D,EACAkG,EACAK,EACAC,EACAE,EARAO,EAAiBhL,EAAKgL,eACtBC,EAAmBjL,EAAKiL,iBACxBC,EAAqBlL,EAAKkL,mBAC1BC,GAAoB,EAuCxB,SAASC,EAAsBC,EAAWC,GACxC,IAAIC,GAAgBN,EAAiBK,EAAcrB,GAC/CuB,GAAgBR,EAAeK,EAAWtH,GAG9C,OAFAA,EAAQsH,EACRpB,EAAWqB,EACPC,GAAgBC,GA1BpBlB,EAAaF,EAAgBrG,EAAOkG,GAChCC,EAAmBP,oBAAmBY,EAAgBL,EAAmBxK,EAAUuK,IACvFQ,EAAcD,EAAWF,EAAYC,EAAeN,IAyBhDsB,GApBAnB,EAAgBT,oBAAmBW,EAAaF,EAAgBrG,EAAOkG,IACvEC,EAAmBP,oBAAmBY,EAAgBL,EAAmBxK,EAAUuK,IACvFQ,EAAcD,EAAWF,EAAYC,EAAeN,IAmBhDuB,EAfN,WACE,IAAIC,EAAiBrB,EAAgBrG,EAAOkG,GACxCyB,GAAqBR,EAAmBO,EAAgBnB,GAG5D,OAFAA,EAAamB,EACTC,IAAmBjB,EAAcD,EAAWF,EAAYC,EAAeN,IACpEQ,CACR,CAS0BkB,GAClBlB,CACR,CAED,OAAO,SAAgCY,EAAWC,GAChD,OAAOH,EAAoBC,EAAsBC,EAAWC,IAzC5DhB,EAAaF,EAFbrG,EA2C4FsH,EA1C5FpB,EA0CuGqB,GAxCvGf,EAAgBL,EAAmBxK,EAAUuK,GAC7CQ,EAAcD,EAAWF,EAAYC,EAAeN,GACpDkB,GAAoB,EACbV,EAsCR,CACF,CAMc,SAASmB,EAA0BlM,EAAUlB,GAC1D,IAAIqN,EAAsBrN,EAAMqN,oBAC5BC,EAAyBtN,EAAMsN,uBAC/BC,EAAiBvN,EAAMuN,eACvBvC,GAAU7C,EAAAA,EAAAA,GAA8BnI,EAAOkF,GAE/C0G,EAAkByB,EAAoBnM,EAAU8J,GAChDU,EAAqB4B,EAAuBpM,EAAU8J,GACtDgB,EAAauB,EAAerM,EAAU8J,GAO1C,OADsBA,EAAQvC,KAAO8D,EAAgCD,GAC9CV,EAAiBF,EAAoBM,EAAY9K,EAAU8J,EACnF,CCrFD,IAAI9F,EAAY,CAAC,OAAQ,iBAAkB,mBAAoB,qBAAsB,uBAwBrF,SAASsI,EAAM/K,EAAKgL,EAAWnG,GAC7B,IAAK,IAAI7J,EAAIgQ,EAAUjQ,OAAS,EAAGC,GAAK,EAAGA,IAAK,CAC9C,IAAIiQ,EAASD,EAAUhQ,GAAGgF,GAC1B,GAAIiL,EAAQ,OAAOA,CACpB,CAED,OAAO,SAAUxM,EAAU8J,GACzB,MAAM,IAAI/K,MAAM,gCAAkCwC,EAAM,QAAU6E,EAAO,uCAAyC0D,EAAQ3C,qBAAuB,IAClJ,CACF,CAED,SAASsF,EAAYhL,EAAGC,GACtB,OAAOD,IAAMC,CACd,CAIM,SAASgL,EAAcC,GAC5B,IAAIrM,OAAiB,IAAVqM,EAAmB,CAAC,EAAIA,EAC/BC,EAAkBtM,EAAKuM,WACvBA,OAAiC,IAApBD,EAA6B5G,EAAkB4G,EAC5DE,EAAwBxM,EAAKyM,yBAC7BA,OAAqD,IAA1BD,EAAmCE,EAAkCF,EAChGG,EAAwB3M,EAAK4M,4BAC7BA,OAAwD,IAA1BD,EAAmCE,EAAqCF,EACtGG,EAAwB9M,EAAK+M,oBAC7BA,OAAgD,IAA1BD,EAAmCE,EAA6BF,EACtFG,EAAuBjN,EAAK2F,gBAC5BA,OAA2C,IAAzBsH,EAAkCC,EAAyBD,EAEjF,OAAO,SAAiB7C,EAAiBF,EAAoBM,EAAYhM,QACzD,IAAVA,IACFA,EAAQ,CAAC,GAGX,IAAI2O,EAAQ3O,EACR4O,EAAaD,EAAMlG,KACnBA,OAAsB,IAAfmG,GAA+BA,EACtCC,EAAuBF,EAAMnC,eAC7BA,OAA0C,IAAzBqC,EAAkClB,EAAckB,EACjEC,EAAwBH,EAAMlC,iBAC9BA,OAA6C,IAA1BqC,EAAmCzE,EAAeyE,EACrEC,EAAwBJ,EAAMjC,mBAC9BA,OAA+C,IAA1BqC,EAAmC1E,EAAe0E,EACvEC,EAAwBL,EAAMzC,oBAC9BA,OAAgD,IAA1B8C,EAAmC3E,EAAe2E,EACxEC,GAAe9G,EAAAA,EAAAA,GAA8BwG,EAAOzJ,GAEpDmI,EAAsBG,EAAM5B,EAAiBqC,EAA0B,mBACvEX,EAAyBE,EAAM9B,EAAoB0C,EAA6B,sBAChFb,EAAiBC,EAAMxB,EAAYuC,EAAqB,cAC5D,OAAOR,EAAW5G,GAAiBqB,EAAAA,EAAAA,GAAS,CAE1ChB,WAAY,UAEZH,eAAgB,SAAwBC,GACtC,MAAO,WAAaA,EAAO,GAC5B,EAEDf,yBAA0BrC,QAAQ0H,GAElCyB,oBAAqBA,EACrBC,uBAAwBA,EACxBC,eAAgBA,EAChB9E,KAAMA,EACN+D,eAAgBA,EAChBC,iBAAkBA,EAClBC,mBAAoBA,EACpBR,oBAAqBA,GACpB+C,GACJ,CACF,CACD,OAA4BrB,IC2DrB,IbtJiCsB,ecSxC,SAASC,GAAWC,EAAWC,GAC7B,IAAIC,GAAUC,EAAAA,EAAAA,WAAS,WACrB,MAAO,CACLF,OAAQA,EACR3B,OAAQ0B,IAEX,IAAE,GACCI,GAAa5F,EAAAA,EAAAA,SAAO,GACpB6F,GAAY7F,EAAAA,EAAAA,QAAO0F,GAEnBI,EADWF,EAAWnJ,SAAWnC,QAAQmL,GAAUI,EAAUpJ,QAAQgJ,QAvB3E,SAAwB/R,EAAWC,GACjC,GAAID,EAAUE,SAAWD,EAAWC,OAClC,OAAO,EAGT,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAUE,OAAQC,IACpC,GAAIH,EAAUG,KAAOF,EAAWE,GAC9B,OAAO,EAIX,OAAO,CACR,CAWoFJ,CAAegS,EAAQI,EAAUpJ,QAAQgJ,SACrGI,EAAUpJ,QAAU,CACzCgJ,OAAQA,EACR3B,OAAQ0B,KAMV,OAJA1K,EAAAA,EAAAA,YAAU,WACR8K,EAAWnJ,SAAU,EACrBoJ,EAAUpJ,QAAUqJ,CACrB,GAAE,CAACA,IACGA,EAAMhC,MACd,Cd5BuCwB,GeF/BnM,GAAAA,wBfGAA,EAAQmM,GciCjB,IAAIpK,GAAUqK,GACVQ,GANJ,SAAwB3M,EAAUqM,GAChC,OAAOF,IAAW,WAChB,OAAOnM,CACR,GAAEqM,EACJ,cEtCGO,GAAU,SAAiBpO,GAC7B,IAAIqO,EAAMrO,EAAKqO,IACXC,EAAQtO,EAAKsO,MACbC,EAASvO,EAAKuO,OACdC,EAAOxO,EAAKwO,KAiBhB,MAdW,CACTH,IAAKA,EACLC,MAAOA,EACPC,OAAQA,EACRC,KAAMA,EACNC,MAPUH,EAAQE,EAQlBE,OAPWH,EAASF,EAQpB1F,EAAG6F,EACH5F,EAAGyF,EACHM,OAAQ,CACNhG,GAAI2F,EAAQE,GAAQ,EACpB5F,GAAI2F,EAASF,GAAO,GAIzB,EACGO,GAAS,SAAgBC,EAAQC,GACnC,MAAO,CACLT,IAAKQ,EAAOR,IAAMS,EAAST,IAC3BG,KAAMK,EAAOL,KAAOM,EAASN,KAC7BD,OAAQM,EAAON,OAASO,EAASP,OACjCD,MAAOO,EAAOP,MAAQQ,EAASR,MAElC,EACGS,GAAS,SAAgBF,EAAQG,GACnC,MAAO,CACLX,IAAKQ,EAAOR,IAAMW,EAASX,IAC3BG,KAAMK,EAAOL,KAAOQ,EAASR,KAC7BD,OAAQM,EAAON,OAASS,EAAST,OACjCD,MAAOO,EAAOP,MAAQU,EAASV,MAElC,EAWGW,GAAY,CACdZ,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,GAEJU,GAAY,SAAmB1Q,GACjC,IAAI2Q,EAAY3Q,EAAM2Q,UAClBC,EAAe5Q,EAAM6Q,OACrBA,OAA0B,IAAjBD,EAA0BH,GAAYG,EAC/CE,EAAe9Q,EAAM+Q,OACrBA,OAA0B,IAAjBD,EAA0BL,GAAYK,EAC/CE,EAAgBhR,EAAMiR,QACtBA,OAA4B,IAAlBD,EAA2BP,GAAYO,EACjDE,EAAYtB,GAAQQ,GAAOO,EAAWE,IACtCM,EAAavB,GAAQW,GAAOI,EAAWI,IACvCK,EAAaxB,GAAQW,GAAOY,EAAYF,IAC5C,MAAO,CACLC,UAAWA,EACXP,UAAWf,GAAQe,GACnBQ,WAAYA,EACZC,WAAYA,EACZP,OAAQA,EACRE,OAAQA,EACRE,QAASA,EAEZ,EAEGI,GAAQ,SAAeC,GACzB,IAAIlU,EAAQkU,EAAI7Q,MAAM,GAAI,GAG1B,GAAe,OAFF6Q,EAAI7Q,OAAO,GAGtB,OAAO,EAGT,IAAIiN,EAASxQ,OAAOE,GAEpB,OADED,MAAMuQ,KAAgJ6D,EAAAA,GAAAA,IAAU,GAC3J7D,CACR,EASG8D,GAAS,SAAgBC,EAAUC,GACrC,IA1DyBrB,EAAQsB,EA0D7BhB,EAAYc,EAASd,UACrBI,EAASU,EAASV,OAClBF,EAASY,EAASZ,OAClBI,EAAUQ,EAASR,QACnBW,GA9D6BD,EA8DFD,EA7DxB,CACL7B,KAFuBQ,EA8DLM,GA5DNd,IAAM8B,EAAQvH,EAC1B4F,KAAMK,EAAOL,KAAO2B,EAAQxH,EAC5B4F,OAAQM,EAAON,OAAS4B,EAAQvH,EAChC0F,MAAOO,EAAOP,MAAQ6B,EAAQxH,IA0DhC,OAAOuG,GAAU,CACfC,UAAWiB,EACXb,OAAQA,EACRF,OAAQA,EACRI,QAASA,GAEZ,EACGY,GAAa,SAAoBJ,EAAUK,GAK7C,YAJe,IAAXA,IACFA,EArBK,CACL3H,EAAG7F,OAAOyN,YACV3H,EAAG9F,OAAO0N,cAsBLR,GAAOC,EAAUK,EACzB,EACGG,GAAe,SAAsBtB,EAAWuB,GAClD,IAAIrB,EAAS,CACXhB,IAAKwB,GAAMa,EAAOC,WAClBrC,MAAOuB,GAAMa,EAAOE,aACpBrC,OAAQsB,GAAMa,EAAOG,cACrBrC,KAAMqB,GAAMa,EAAOI,aAEjBrB,EAAU,CACZpB,IAAKwB,GAAMa,EAAOK,YAClBzC,MAAOuB,GAAMa,EAAOM,cACpBzC,OAAQsB,GAAMa,EAAOO,eACrBzC,KAAMqB,GAAMa,EAAOQ,cAEjB3B,EAAS,CACXlB,IAAKwB,GAAMa,EAAOS,gBAClB7C,MAAOuB,GAAMa,EAAOU,kBACpB7C,OAAQsB,GAAMa,EAAOW,mBACrB7C,KAAMqB,GAAMa,EAAOY,kBAErB,OAAOpC,GAAU,CACfC,UAAWA,EACXE,OAAQA,EACRI,QAASA,EACTF,OAAQA,GAEX,EACGgC,GAAS,SAAgBC,GAC3B,IAAIrC,EAAYqC,EAAGC,wBACff,EAAS5N,OAAO4O,iBAAiBF,GACrC,OAAOf,GAAatB,EAAWuB,EAChC,cCpHD,GAjCc,SAAiBiB,GAC7B,IAAInV,EAAW,GACXoV,EAAU,KAEVC,EAAY,WACd,IAAK,IAAIhR,EAAOjE,UAAUZ,OAAQ8V,EAAO,IAAI/Q,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/E8Q,EAAK9Q,GAAQpE,UAAUoE,GAGzBxE,EAAWsV,EAEPF,IAIJA,EAAUG,uBAAsB,WAC9BH,EAAU,KACVD,EAAG7U,WAAM,EAAQN,EAClB,IACF,EAWD,OATAqV,EAAUG,OAAS,WACZJ,IAILK,qBAAqBL,GACrBA,EAAU,KACX,EAEMC,CACR,ECJD,SAASK,GAAItS,EAAMuS,GAYlB,CACaD,GAAInK,KAAK,KAAM,QACjBmK,GAAInK,KAAK,KAAM,SAE3B,SAASqK,KAAS,CAMlB,SAASC,GAAWb,EAAIc,EAAUC,GAChC,IAAIC,EAAaF,EAASG,KAAI,SAAUC,GACtC,IAAIlJ,EANR,SAAoBmJ,EAAQC,GAC1B,OAAO5L,EAAAA,EAAAA,GAAS,CAAC,EAAG2L,EAAQ,CAAC,EAAGC,EACjC,CAIiBC,CAAWN,EAAeG,EAAQlJ,SAEhD,OADAgI,EAAGsB,iBAAiBJ,EAAQK,UAAWL,EAAQf,GAAInI,GAC5C,WACLgI,EAAGwB,oBAAoBN,EAAQK,UAAWL,EAAQf,GAAInI,EACvD,CACF,IACD,OAAO,WACLgJ,EAAWS,SAAQ,SAAUC,GAC3BA,GACD,GACF,CACF,CAED,IACIC,GAAS,mBACb,SAASC,GAAajB,GACpBtV,KAAKsV,QAAUA,CAChB,CAMD,SAASpC,GAAUsD,EAAWlB,GAC5B,IAAIkB,EAKF,MAAM,IAAID,GAAaD,GAI1B,CAdDC,GAAajK,UAAU5L,SAAW,WAChC,OAAOV,KAAKsV,OACb,EAcD,IAAImB,GAAgB,SAAUC,GAG5B,SAASD,IAGP,IAFA,IAAIE,EAEK3S,EAAOjE,UAAUZ,OAAQ8V,EAAO,IAAI/Q,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/E8Q,EAAK9Q,GAAQpE,UAAUoE,GAsCzB,OAnCAwS,EAAQD,EAAiBlK,KAAKvM,MAAMyW,EAAkB,CAAC1W,MAAM4W,OAAO3B,KAAUjV,MACxE6W,UAAY,KAClBF,EAAMN,OAASd,GAEfoB,EAAMG,cAAgB,SAAUC,GAC9B,IAAIF,EAAYF,EAAMK,eAElBH,EAAUI,cACZJ,EAAUK,WAIFH,EAAMtO,iBAEG8N,IACjBQ,EAAMI,gBAMT,EAEDR,EAAMK,aAAe,WACnB,IAAKL,EAAME,UACT,MAAM,IAAIjV,MAAM,mDAGlB,OAAO+U,EAAME,SACd,EAEDF,EAAMS,aAAe,SAAUP,GAC7BF,EAAME,UAAYA,CACnB,EAEMF,CACR,EA7CDU,EAAAA,EAAAA,GAAeZ,EAAeC,GA+C9B,IAAIY,EAASb,EAAcnK,UA8B3B,OA5BAgL,EAAOC,kBAAoB,WACzBvX,KAAKqW,OAASb,GAAWvP,OAAQ,CAAC,CAChCiQ,UAAW,QACXpB,GAAI9U,KAAK8W,gBAEZ,EAEDQ,EAAOE,kBAAoB,SAA2BC,GACpD,KAAIA,aAAelB,IASnB,MAAMkB,EAJJzX,KAAK0X,SAAS,CAAC,EAKlB,EAEDJ,EAAOK,qBAAuB,WAC5B3X,KAAKqW,QACN,EAEDiB,EAAOM,OAAS,WACd,OAAO5X,KAAKuK,MAAMhE,SAASvG,KAAKoX,aACjC,EAEMX,CACR,CA/EmB,CA+ElBhS,EAAAA,WAIEoT,GAAW,SAAkBnV,GAC/B,OAAOA,EAAQ,CAChB,EAMGoV,GAAe,SAAsBC,EAAQC,GAC/C,IAAIC,EAAeF,EAAOG,cAAgBF,EAAYE,YAClDC,EAAgBN,GAASE,EAAOrV,OAChC0V,EAAcP,GAASG,EAAYtV,OAEvC,OAAIuV,EACK,iDAAmDE,EAAgB,uBAAyBC,EAAc,SAG5G,+CAAiDD,EAAgB,iBAAmBJ,EAAOG,YAAc,iBAAmBF,EAAYE,YAAc,qBAAuBE,EAAc,MACnM,EAEGC,GAAc,SAAqBC,EAAIP,EAAQQ,GAGjD,OAFiBR,EAAOG,cAAgBK,EAAQL,YAGvC,oBAAsBI,EAAK,kCAAoCC,EAAQC,YAGzE,oBAAsBF,EAAK,mBAAqBP,EAAOG,YAAc,kCAAoCK,EAAQC,YAAc,mBAAqBD,EAAQL,YAAc,QAClL,EAkBGO,GAAkB,SAAyBV,GAC7C,MAAO,4DAA8DF,GAASE,EAAOrV,OAAS,IAC/F,EAqBGgW,GAvE8B,8NAuE9BA,GAjEc,SAAqBC,GACrC,MAAO,2CAA6Cd,GAASc,EAAMZ,OAAOrV,OAAS,IACpF,EA+DGgW,GAvCe,SAAsBE,GACvC,IAAIC,EAAWD,EAAOZ,YAEtB,GAAIa,EACF,OAAOf,GAAac,EAAOb,OAAQc,GAGrC,IAAIN,EAAUK,EAAOL,QAErB,OAAIA,EACKF,GAAYO,EAAOJ,YAAaI,EAAOb,OAAQQ,GAGjD,gDACR,EAyBGG,GAnBY,SAAmBrJ,GACjC,GAAsB,WAAlBA,EAAOyJ,OACT,MAAO,sCAAwCL,GAAgBpJ,EAAO0I,QAAU,SAGlF,IAAIc,EAAWxJ,EAAO2I,YAClBO,EAAUlJ,EAAOkJ,QAErB,OAAIM,EACK,6CAA+Cf,GAAazI,EAAO0I,OAAQc,GAAY,SAG5FN,EACK,6CAA+CF,GAAYhJ,EAAOmJ,YAAanJ,EAAO0I,OAAQQ,GAAW,SAG3G,oEAAsEE,GAAgBpJ,EAAO0I,QAAU,MAC/G,EASGgB,GAAS,CACXjN,EAAG,EACHC,EAAG,GAEDiN,GAAM,SAAaC,EAAQC,GAC7B,MAAO,CACLpN,EAAGmN,EAAOnN,EAAIoN,EAAOpN,EACrBC,EAAGkN,EAAOlN,EAAImN,EAAOnN,EAExB,EACGoN,GAAW,SAAkBF,EAAQC,GACvC,MAAO,CACLpN,EAAGmN,EAAOnN,EAAIoN,EAAOpN,EACrBC,EAAGkN,EAAOlN,EAAImN,EAAOnN,EAExB,EACGvM,GAAU,SAAiByZ,EAAQC,GACrC,OAAOD,EAAOnN,IAAMoN,EAAOpN,GAAKmN,EAAOlN,IAAMmN,EAAOnN,CACrD,EACGqN,GAAS,SAAgBC,GAC3B,MAAO,CACLvN,EAAe,IAAZuN,EAAMvN,GAAWuN,EAAMvN,EAAI,EAC9BC,EAAe,IAAZsN,EAAMtN,GAAWsN,EAAMtN,EAAI,EAEjC,EACGuN,GAAQ,SAAeC,EAAMxa,EAAOya,GACtC,IAAIrW,EAMJ,YAJmB,IAAfqW,IACFA,EAAa,IAGRrW,EAAO,CAAC,GAAQoW,GAAQxa,EAAOoE,EAAc,MAAToW,EAAe,IAAM,KAAOC,EAAYrW,CACpF,EACGsW,GAAW,SAAkBR,EAAQC,GACvC,OAAO1Y,KAAKkZ,KAAKlZ,KAAKmZ,IAAIT,EAAOpN,EAAImN,EAAOnN,EAAG,GAAKtL,KAAKmZ,IAAIT,EAAOnN,EAAIkN,EAAOlN,EAAG,GACnF,EACG6N,GAAU,SAAiB5H,EAAQ6H,GACrC,OAAOrZ,KAAKsZ,IAAI7Z,MAAMO,KAAMqZ,EAAOjE,KAAI,SAAUyD,GAC/C,OAAOI,GAASzH,EAAQqH,EACzB,IACF,EACGpZ,GAAQ,SAAe6U,GACzB,OAAO,SAAUuE,GACf,MAAO,CACLvN,EAAGgJ,EAAGuE,EAAMvN,GACZC,EAAG+I,EAAGuE,EAAMtN,GAEf,CACF,EAiBGgO,GAAmB,SAA0BC,EAASX,GACxD,MAAO,CACL7H,IAAKwI,EAAQxI,IAAM6H,EAAMtN,EACzB4F,KAAMqI,EAAQrI,KAAO0H,EAAMvN,EAC3B4F,OAAQsI,EAAQtI,OAAS2H,EAAMtN,EAC/B0F,MAAOuI,EAAQvI,MAAQ4H,EAAMvN,EAEhC,EACGmO,GAAa,SAAoBD,GACnC,MAAO,CAAC,CACNlO,EAAGkO,EAAQrI,KACX5F,EAAGiO,EAAQxI,KACV,CACD1F,EAAGkO,EAAQvI,MACX1F,EAAGiO,EAAQxI,KACV,CACD1F,EAAGkO,EAAQrI,KACX5F,EAAGiO,EAAQtI,QACV,CACD5F,EAAGkO,EAAQvI,MACX1F,EAAGiO,EAAQtI,QAEd,EA0BGwI,GAAO,SAAclI,EAAQmI,GAC/B,OAAIA,GAASA,EAAMC,kBAhEF,SAAUD,EAAOE,GAClC,IAAIhL,EAASkC,GAAQ,CACnBC,IAAKhR,KAAK8Z,IAAID,EAAQ7I,IAAK2I,EAAM3I,KACjCC,MAAOjR,KAAKsZ,IAAIO,EAAQ5I,MAAO0I,EAAM1I,OACrCC,OAAQlR,KAAKsZ,IAAIO,EAAQ3I,OAAQyI,EAAMzI,QACvCC,KAAMnR,KAAK8Z,IAAID,EAAQ1I,KAAMwI,EAAMxI,QAGrC,OAAItC,EAAOuC,OAAS,GAAKvC,EAAOwC,QAAU,EACjC,KAGFxC,CACR,CAoDUkL,CAAYJ,EAAMK,cAAexI,GAGnCT,GAAQS,EAChB,EAEGyI,GAAc,SAAUtX,GAC1B,IAAIuX,EAAOvX,EAAKuX,KACZC,EAAkBxX,EAAKwX,gBACvBC,EAAOzX,EAAKyX,KACZT,EAAQhX,EAAKgX,MACbU,EA/BO,SAAgB7I,EAAQmI,GACnC,OAAKA,EAIEJ,GAAiB/H,EAAQmI,EAAM1G,OAAOqH,KAAKC,cAHzC/I,CAIV,CAyBgByB,CAAOiH,EAAK7H,UAAWsH,GAClCa,EAxBS,SAAkBhJ,EAAQ4I,EAAMD,GAE3C,IAAIM,EADN,OAAIN,GAAmBA,EAAgBO,aAG9B/Q,EAAAA,EAAAA,GAAS,CAAC,EAAG6H,IAASiJ,EAAY,CAAC,GAAaL,EAAKO,KAAOnJ,EAAO4I,EAAKO,KAAOR,EAAgBO,YAAYN,EAAKrB,MAAO0B,IAGzHjJ,CACR,CAgBiBoJ,CAASP,EAAUD,EAAMD,GAEzC,MAAO,CACLD,KAAMA,EACNC,gBAAiBA,EACjBU,OAJYnB,GAAKc,EAAWb,GAM/B,EAEGmB,GAAmB,SAAUC,EAAWC,GACzCD,EAAUpB,OAAmEjH,IAAU,GACxF,IAAIuI,EAAaF,EAAUpB,MACvBuB,EAAavC,GAASqC,EAAWC,EAAWhI,OAAOxC,SACnD0K,EAAqBvC,GAAOsC,GAE5BvB,GAAQhQ,EAAAA,EAAAA,GAAS,CAAC,EAAGsR,EAAY,CACnChI,OAAQ,CACNxC,QAASwK,EAAWhI,OAAOxC,QAC3BjJ,QAASwT,EACTV,KAAM,CACJ/b,MAAO2c,EACPX,aAAcY,GAEhBrB,IAAKmB,EAAWhI,OAAO6G,OAIvBD,EAAUI,GAAW,CACvBC,KAAMa,EAAUlB,QAAQK,KACxBC,gBAAiBY,EAAUlB,QAAQM,gBACnCC,KAAMW,EAAUX,KAChBT,MAAOA,IAQT,OALahQ,EAAAA,EAAAA,GAAS,CAAC,EAAGoR,EAAW,CACnCpB,MAAOA,EACPE,QAASA,GAIZ,EASD,SAASuB,GAAOhG,GACd,OAAIvU,OAAOua,OACFva,OAAOua,OAAOhG,GAGhBvU,OAAO+K,KAAKwJ,GAAKA,KAAI,SAAU9R,GACpC,OAAO8R,EAAI9R,EACZ,GACF,CACD,SAAS+X,GAAUC,EAAMC,GACvB,GAAID,EAAKD,UACP,OAAOC,EAAKD,UAAUE,GAGxB,IAAK,IAAI3c,EAAI,EAAGA,EAAI0c,EAAK3c,OAAQC,IAC/B,GAAI2c,EAAUD,EAAK1c,IACjB,OAAOA,EAIX,OAAQ,CACT,CACD,SAAS4c,GAAKF,EAAMC,GAClB,GAAID,EAAKE,KACP,OAAOF,EAAKE,KAAKD,GAGnB,IAAIrZ,EAAQmZ,GAAUC,EAAMC,GAE5B,OAAe,IAAXrZ,EACKoZ,EAAKpZ,QADd,CAKD,CACD,SAASuZ,GAAQH,GACf,OAAO5X,MAAMoI,UAAUlK,MAAMoK,KAAKsP,EACnC,CAED,IAAII,IAAiBC,EAAAA,GAAAA,IAAW,SAAUC,GACxC,OAAOA,EAAW/X,QAAO,SAAUgY,EAAUrU,GAE3C,OADAqU,EAASrU,EAAQsU,WAAWhE,IAAMtQ,EAC3BqU,CACR,GAAE,CAAC,EACL,IACGE,IAAiBJ,EAAAA,GAAAA,IAAW,SAAUK,GACxC,OAAOA,EAAWnY,QAAO,SAAUgY,EAAUrU,GAE3C,OADAqU,EAASrU,EAAQsU,WAAWhE,IAAMtQ,EAC3BqU,CACR,GAAE,CAAC,EACL,IACGI,IAAkBN,EAAAA,GAAAA,IAAW,SAAUC,GACzC,OAAOR,GAAOQ,EACf,IACGM,IAAkBP,EAAAA,GAAAA,IAAW,SAAUK,GACzC,OAAOZ,GAAOY,EACf,IAEGG,IAA+BR,EAAAA,GAAAA,IAAW,SAAUjE,EAAasE,GACnE,IAAInN,EAASqN,GAAgBF,GAAYI,QAAO,SAAUC,GACxD,OAAO3E,IAAgB2E,EAAUP,WAAWpE,WAC7C,IAAE4E,MAAK,SAAUxY,EAAGC,GACnB,OAAOD,EAAEgY,WAAW5Z,MAAQ6B,EAAE+X,WAAW5Z,KAC1C,IACD,OAAO2M,CACR,IAED,SAAS0N,GAAkBC,GACzB,OAAIA,EAAOC,IAAyB,YAAnBD,EAAOC,GAAGla,KAClBia,EAAOC,GAAGjF,YAGZ,IACR,CACD,SAASkF,GAAcF,GACrB,OAAIA,EAAOC,IAAyB,YAAnBD,EAAOC,GAAGla,KAClBia,EAAOC,GAAG1E,QAGZ,IACR,CAED,IAAI4E,IAA0BhB,EAAAA,GAAAA,IAAW,SAAUiB,EAAQtB,GACzD,OAAOA,EAAKc,QAAO,SAAUS,GAC3B,OAAOA,EAAKf,WAAWhE,KAAO8E,EAAOd,WAAWhE,EACjD,GACF,IAgEGgF,GAAY,SAAUT,EAAW7E,GACnC,OAAO6E,EAAUP,WAAWpE,cAAgBF,EAAYsE,WAAWhE,EACpE,EAEGiF,GAAgB,CAClBlE,MAAON,GACPha,MAAO,GAELye,GAAc,CAChBC,UAAW,CAAC,EACZC,QAAS,CAAC,EACVC,IAAK,IAEHC,GAAW,CACbC,UAAWL,GACXM,YAAaP,GACbN,GAAI,MAGFc,GAAY,SAAUC,EAAYC,GACpC,OAAO,SAAUlf,GACf,OAAOif,GAAcjf,GAASA,GAASkf,CACxC,CACF,EAEGC,GAAkC,SAAU/D,GAC9C,IAAIgE,EAAmBJ,GAAS5D,EAAM3I,IAAK2I,EAAMzI,QAC7C0M,EAAqBL,GAAS5D,EAAMxI,KAAMwI,EAAM1I,OACpD,OAAO,SAAU4I,GAGf,GAFkB8D,EAAiB9D,EAAQ7I,MAAQ2M,EAAiB9D,EAAQ3I,SAAW0M,EAAmB/D,EAAQ1I,OAASyM,EAAmB/D,EAAQ5I,OAGpJ,OAAO,EAGT,IAAI4M,EAA+BF,EAAiB9D,EAAQ7I,MAAQ2M,EAAiB9D,EAAQ3I,QACzF4M,EAAiCF,EAAmB/D,EAAQ1I,OAASyM,EAAmB/D,EAAQ5I,OAGpG,GAF2B4M,GAAgCC,EAGzD,OAAO,EAGT,IAAIC,EAAqBlE,EAAQ7I,IAAM2I,EAAM3I,KAAO6I,EAAQ3I,OAASyI,EAAMzI,OACvE8M,EAAuBnE,EAAQ1I,KAAOwI,EAAMxI,MAAQ0I,EAAQ5I,MAAQ0I,EAAM1I,MAG9E,SAF8B8M,IAAsBC,KAMtBD,GAAsBD,GAAkCE,GAAwBH,EAE/G,CACF,EAEGI,GAAgC,SAAUtE,GAC5C,IAAIgE,EAAmBJ,GAAS5D,EAAM3I,IAAK2I,EAAMzI,QAC7C0M,EAAqBL,GAAS5D,EAAMxI,KAAMwI,EAAM1I,OACpD,OAAO,SAAU4I,GAEf,OADkB8D,EAAiB9D,EAAQ7I,MAAQ2M,EAAiB9D,EAAQ3I,SAAW0M,EAAmB/D,EAAQ1I,OAASyM,EAAmB/D,EAAQ5I,MAEvJ,CACF,EAEGiN,GAAW,CACbC,UAAW,WACXpF,KAAM,IACNqF,cAAe,IACfjG,MAAO,MACPwC,IAAK,SACL0D,KAAM,SACNC,eAAgB,OAChBC,aAAc,QACdC,cAAe,SAEbC,GAAa,CACfN,UAAW,aACXpF,KAAM,IACNqF,cAAe,IACfjG,MAAO,OACPwC,IAAK,QACL0D,KAAM,QACNC,eAAgB,MAChBC,aAAc,SACdC,cAAe,UAkCbE,GAAY,SAAmB/b,GACjC,IAAIgc,EAAgBhc,EAAK6O,OACrBgG,EAAc7U,EAAK6U,YACnBoH,EAAWjc,EAAKic,SAChBC,EAA4Blc,EAAKkc,0BACjCC,EAA0Bnc,EAAKmc,wBAC/BC,EAAkBF,EAvBI,SAA+BrN,EAAQgG,GACjE,IAAI+C,EAAe/C,EAAYmC,MAAQnC,EAAYmC,MAAM1G,OAAOqH,KAAKC,aAAehC,GACpF,OAAOgB,GAAiB/H,EAAQ+I,EACjC,CAoBmDyE,CAAsBL,EAAenH,GAAemH,EACtG,OAnByB,SAA8BnN,EAAQgG,EAAasH,GAC5E,QAAKtH,EAAYqC,QAAQgB,QAIlBiE,EAAwBtH,EAAYqC,QAAQgB,OAA5CiE,CAAoDtN,EAC5D,CAaQyN,CAAqBF,EAAiBvH,EAAasH,IAXlC,SAA6BtN,EAAQoN,EAAUE,GACvE,OAAOA,EAAwBF,EAAxBE,CAAkCtN,EAC1C,CASuF0N,CAAoBH,EAAiBH,EAAUE,EACtI,EAEGK,GAAqB,SAA4B1K,GACnD,OAAOiK,IAAU/U,EAAAA,EAAAA,GAAS,CAAC,EAAG8K,EAAM,CAClCqK,wBAAyBpB,KAE5B,EACG0B,GAAmB,SAA0B3K,GAC/C,OAAOiK,IAAU/U,EAAAA,EAAAA,GAAS,CAAC,EAAG8K,EAAM,CAClCqK,wBAAyBb,KAE5B,EAsCD,SAASoB,GAAsB1c,GAC7B,IAAI2c,EAAgB3c,EAAK2c,cACrB9H,EAAc7U,EAAK6U,YACnB8F,EAAc3a,EAAK2a,YACnBsB,EAAWjc,EAAKic,SAChBW,EAAqB5c,EAAK4c,mBAC1Bva,EAAOrC,EAAKqC,KAChB,OAAOsa,EAAczb,QAAO,SAAiB2b,EAAQnD,GACnD,IAAI7K,EAnBR,SAAmB6K,EAAWiB,GAC5B,IAAIjL,EAAYgK,EAAUnC,KAAK7H,UAC3BZ,EAAW,CACbT,IAAKsM,EAAYzE,MAAMtN,EACvB0F,MAAO,EACPC,OAAQ,EACRC,KAAMmM,EAAYzE,MAAMvN,GAE1B,OAAOyF,GAAQQ,GAAOc,EAAWZ,GAClC,CAUgBgO,CAAUpD,EAAWiB,GAC9BxF,EAAKuE,EAAUP,WAAWhE,GAS9B,GARA0H,EAAOrC,IAAIlb,KAAK6V,IACAqH,GAAmB,CACjC3N,OAAQA,EACRgG,YAAaA,EACboH,SAAUA,EACVC,2BAA2B,IAK3B,OADAW,EAAOvC,UAAUZ,EAAUP,WAAWhE,KAAM,EACrC0H,EAGT,IAAIE,EAtDe,SAA0B5H,EAAI9S,EAAMua,GACzD,GAAkC,mBAAvBA,EACT,OAAOA,EAGT,IAAKva,EACH,OAAO,EAGT,IAAIiY,EAAYjY,EAAKiY,UACjBC,EAAUlY,EAAKkY,QAEnB,GAAID,EAAUnF,GACZ,OAAO,EAGT,IAAI+D,EAAWqB,EAAQpF,GACvB,OAAO+D,GAAWA,EAAS6D,aAC5B,CAoCuBC,CAAiB7H,EAAI9S,EAAMua,GAC3ChF,EAAe,CACjBvC,YAAaF,EACb4H,cAAeA,GAGjB,OADAF,EAAOtC,QAAQpF,GAAMyC,EACdiF,CACR,GAAE,CACDrC,IAAK,GACLD,QAAS,CAAC,EACVD,UAAW,CAAC,GAEf,CAWD,SAAS2C,GAAQjd,GACf,IAAIkd,EAAoBld,EAAKkd,kBACzBC,EAAand,EAAKmd,WAClBxC,EAAc3a,EAAK2a,YACnB9F,EAAc7U,EAAK6U,YACnBuI,EAdN,SAA4B/D,EAAY7P,GACtC,IAAK6P,EAAWrd,OACd,OAAO,EAGT,IAAIqhB,EAAkBhE,EAAWA,EAAWrd,OAAS,GAAGmd,WAAW5Z,MACnE,OAAOiK,EAAQ2T,WAAaE,EAAkBA,EAAkB,CACjE,CAOgBC,CAAmBJ,EAAmB,CACnDC,WAAYA,IAEd,MAAO,CACLzC,UAAWL,GACXM,YAAaA,EACbb,GAAI,CACFla,KAAM,UACNiV,YAAa,CACXE,YAAaF,EAAYsE,WAAWhE,GACpC5V,MAAO6d,IAId,CAED,SAASG,GAAuB/e,GAC9B,IAAIkb,EAAYlb,EAAMkb,UAClBwD,EAAoB1e,EAAM0e,kBAC1BrI,EAAcrW,EAAMqW,YACpBoH,EAAWzd,EAAMyd,SACjBtB,EAAcnc,EAAMmc,YACpBtY,EAAO7D,EAAM6D,KACb9C,EAAQf,EAAMe,MACdqd,EAAqBpe,EAAMoe,mBAC3BO,EAAahD,GAAST,EAAW7E,GAErC,GAAa,MAATtV,EACF,OAAO0d,GAAQ,CACbC,kBAAmBA,EACnBC,WAAYA,EACZxC,YAAaA,EACb9F,YAAaA,IAIjB,IAAI7I,EAAQ6M,GAAKqE,GAAmB,SAAUhD,GAC5C,OAAOA,EAAKf,WAAW5Z,QAAUA,CAClC,IAED,IAAKyM,EACH,OAAOiR,GAAQ,CACbC,kBAAmBA,EACnBC,WAAYA,EACZxC,YAAaA,EACb9F,YAAaA,IAIjB,IAAI2I,EAAkBxD,GAAwBN,EAAWwD,GACrDO,EAAYP,EAAkB1d,QAAQwM,GAU1C,MAAO,CACL0O,UATcgC,GAAsB,CACpCC,cAFaa,EAAgBve,MAAMwe,GAGnC5I,YAAaA,EACb8F,YAAaA,EACbtY,KAAMA,EACN4Z,SAAUA,EAASjF,MACnB4F,mBAAoBA,IAIpBjC,YAAaA,EACbb,GAAI,CACFla,KAAM,UACNiV,YAAa,CACXE,YAAaF,EAAYsE,WAAWhE,GACpC5V,MAAOA,IAId,CAED,SAASme,GAAsBrI,EAAasI,GAC1C,OAAOjb,QAAQib,EAAcC,SAASvI,GACvC,CAED,IA0DIwI,GAAmB,SAAU7d,GAC/B,IAAI8d,EAAkB9d,EAAK8d,gBACvBhJ,EAAe9U,EAAK8U,aACpB4E,EAAY1Z,EAAK0Z,UACjBL,EAAarZ,EAAKqZ,WAClBxE,EAAc7U,EAAK6U,YACnBqI,EAAoBld,EAAKkd,kBACzBa,EAAiB/d,EAAK+d,eACtB9B,EAAWjc,EAAKic,SAChB0B,EAAgB3d,EAAK2d,cACrBK,EAAQD,EAAejE,GAG3B,GAFCkE,GAAgIjO,IAAU,GAExH,YAAfiO,EAAMpe,KAAoB,CAC5B,IAAIqe,EAzCW,SAAUje,GAC3B,IAAI8d,EAAkB9d,EAAK8d,gBACvBhJ,EAAe9U,EAAK8U,aACpBoI,EAAoBld,EAAKkd,kBACzBxH,EAAW1V,EAAK0V,SAEpB,IAAKwH,EAAkBlhB,OACrB,OAAO,KAGT,IAAIkiB,EAAexI,EAASnW,MACxB4e,EAAgBL,EAAkBI,EAAe,EAAIA,EAAe,EACpEE,EAAalB,EAAkB,GAAG/D,WAAW5Z,MAC7C8e,EAAYnB,EAAkBA,EAAkBlhB,OAAS,GAAGmd,WAAW5Z,MAG3E,OAAI4e,EAAgBC,GAIhBD,GANarJ,EAAeuJ,EAAYA,EAAY,GAG/C,KAOFF,CACR,CAgBmBG,CAAY,CAC1BR,gBAAiBA,EACjBhJ,aAAcA,EACdY,SAAUsI,EAAMnJ,YAChBqI,kBAAmBA,IAGrB,OAAiB,MAAbe,EACK,KAGFV,GAAuB,CAC5B7D,UAAWA,EACXwD,kBAAmBA,EACnBrI,YAAaA,EACboH,SAAUA,EACV5Z,KAAM0b,EAAerD,UACrBC,YAAaoD,EAAepD,YAC5Bpb,MAAO0e,GAEV,CAED,IAAIb,EA9Fa,SAAUpd,GAC3B,IAAI8d,EAAkB9d,EAAK8d,gBACvBjJ,EAAc7U,EAAK6U,YACnBwE,EAAarZ,EAAKqZ,WAClBjE,EAAUpV,EAAKoV,QACfuI,EAAgB3d,EAAK2d,cAEzB,IAAK9I,EAAY0J,iBACf,OAAO,KAGT,IAAIC,EAAYpJ,EAAQC,YAEpBoJ,EADcpF,EAAWmF,GACMrF,WAAW5Z,MAG9C,OAFuCme,GAAsBc,EAAWb,GAGlEG,EACKW,EAGFA,EAAmB,EAGxBX,EACKW,EAAmB,EAGrBA,CACR,CAiEgBC,CAAY,CACzBZ,gBAAiBA,EACjBjJ,YAAaA,EACb6F,UAAWqD,EAAerD,UAC1BrB,WAAYA,EACZjE,QAAS4I,EAAM5I,QACfuI,cAAeA,IAGjB,OAAgB,MAAZP,EACK,KAGFG,GAAuB,CAC5B7D,UAAWA,EACXwD,kBAAmBA,EACnBrI,YAAaA,EACboH,SAAUA,EACV5Z,KAAM0b,EAAerD,UACrBC,YAAaoD,EAAepD,YAC5Bpb,MAAO6d,GAEV,EAgBGuB,GAAiB,SAAU3e,GAC7B,IAAI2d,EAAgB3d,EAAK2d,cACrB9D,EAAS7Z,EAAK6Z,OACdR,EAAarZ,EAAKqZ,WAClBjE,EAAU2E,GAAcF,GAC3BzE,GAAqErF,IAAU,GAChF,IAAI6O,EAAcxJ,EAAQC,YACtB1G,EAAS0K,EAAWuF,GAAarH,KAAKpI,UAAUR,OAChDkQ,EAtB6B,SAAU7e,GAC3C,IAAI0a,EAAY1a,EAAK0a,UACjBiD,EAAgB3d,EAAK2d,cACrBiB,EAAc5e,EAAK4e,YACnBjE,EAAc3a,EAAK2a,YACnBmE,EAAcpc,QAAQgY,EAAUH,QAAQqE,IAAgBlE,EAAUJ,UAAUsE,IAEhF,OAAIlB,GAAsBkB,EAAajB,GAC9BmB,EAAclJ,GAASK,GAAO0E,EAAYzE,OAG5C4I,EAAcnE,EAAYzE,MAAQN,EAC1C,CAUkBmJ,CAA4B,CAC3CrE,UAAWb,EAAOa,UAClBiD,cAAeA,EACfiB,YAAaA,EACbjE,YAAad,EAAOc,cAEtB,OAAO9E,GAAIlH,EAAQkQ,EACpB,EAEGG,GAAqC,SAA4CvH,EAAMwH,GACzF,OAAOA,EAAI5P,OAAOoI,EAAKjC,OAASyJ,EAAI9P,UAAUsI,EAAKiE,MAAQ,CAC5D,EAMGwD,GAA8B,SAAqCzH,EAAM5I,EAAQsQ,GACnF,OAAOtQ,EAAO4I,EAAKkE,gBAAkBwD,EAAS9P,OAAOoI,EAAKkE,gBAAkBwD,EAAShQ,UAAUsI,EAAKoE,eAAiB,CACtH,EAEGuD,GAAU,SAAiBpf,GAC7B,IAAIyX,EAAOzX,EAAKyX,KACZ4H,EAAiBrf,EAAKqf,eACtBF,EAAWnf,EAAKmf,SACpB,OAAOhJ,GAAMsB,EAAKrB,KAAMiJ,EAAe3P,UAAU+H,EAAKO,KAAOgH,GAAmCvH,EAAM0H,GAAWD,GAA4BzH,EAAM4H,EAAe3P,UAAWyP,GAC9K,EACGG,GAAW,SAAkB9gB,GAC/B,IAAIiZ,EAAOjZ,EAAMiZ,KACb4H,EAAiB7gB,EAAM6gB,eACvBF,EAAW3gB,EAAM2gB,SACrB,OAAOhJ,GAAMsB,EAAKrB,KAAMiJ,EAAe3P,UAAU+H,EAAKjC,OAlBjB,SAA0CiC,EAAMwH,GACrF,OAAOA,EAAI5P,OAAOoI,EAAKO,KAAOiH,EAAI9P,UAAUsI,EAAKiE,MAAQ,CAC1D,CAgBgE6D,CAAiC9H,EAAM0H,GAAWD,GAA4BzH,EAAM4H,EAAe3P,UAAWyP,GAC9K,EAQGK,GAAkB,SAAUxf,GAC9B,IAAI6Z,EAAS7Z,EAAK6Z,OACdH,EAAY1Z,EAAK0Z,UACjBL,EAAarZ,EAAKqZ,WAClBjB,EAAYpY,EAAKoY,UACjBuF,EAAgB3d,EAAK2d,cACrBT,EAAoB1D,GAA6BpB,EAAUe,WAAWhE,GAAIkE,GAC1EoG,EAAgB/F,EAAUnC,KAC1BE,EAAOW,EAAUX,KAErB,IAAKyF,EAAkBlhB,OACrB,OAlBc,SAAqBmR,GACrC,IAAIsK,EAAOtK,EAAMsK,KACbiI,EAAWvS,EAAMuS,SACjBP,EAAWhS,EAAMgS,SACrB,OAAOhJ,GAAMsB,EAAKrB,KAAMsJ,EAAS9P,WAAW6H,EAAKjC,OAASwJ,GAAmCvH,EAAM0H,GAAWD,GAA4BzH,EAAMiI,EAAS9P,WAAYuP,GACtK,CAaUQ,CAAY,CACjBlI,KAAMA,EACNiI,SAAUtH,EAAUb,KACpB4H,SAAUM,IAId,IAAI/E,EAAYb,EAAOa,UACnBC,EAAcd,EAAOc,YACrBiF,EAAelF,EAAUF,IAAI,GAEjC,GAAIoF,EAAc,CAChB,IAAInJ,EAAU4C,EAAWuG,GAEzB,GAAIlC,GAAsBkC,EAAcjC,GACtC,OAAO2B,GAAS,CACd7H,KAAMA,EACN4H,eAAgB5I,EAAQc,KACxB4H,SAAUM,IAId,IAAII,EAAmB7P,GAAOyG,EAAQc,KAAMoD,EAAYzE,OACxD,OAAOoJ,GAAS,CACd7H,KAAMA,EACN4H,eAAgBQ,EAChBV,SAAUM,GAEb,CAED,IAAIpd,EAAO6a,EAAkBA,EAAkBlhB,OAAS,GAExD,GAAIqG,EAAK8W,WAAWhE,KAAOuE,EAAUP,WAAWhE,GAC9C,OAAOsK,EAActQ,UAAUR,OAGjC,GAAI+O,GAAsBrb,EAAK8W,WAAWhE,GAAIwI,GAAgB,CAC5D,IAAIpG,EAAOvH,GAAO3N,EAAKkV,KAAMtB,GAAO0H,EAAchD,YAAYzE,QAC9D,OAAOkJ,GAAQ,CACb3H,KAAMA,EACN4H,eAAgB9H,EAChB4H,SAAUM,GAEb,CAED,OAAOL,GAAQ,CACb3H,KAAMA,EACN4H,eAAgBhd,EAAKkV,KACrB4H,SAAUM,GAEb,EAEGvD,GAA6B,SAAU9D,EAAWlC,GACpD,IAAIc,EAAQoB,EAAUpB,MAEtB,OAAKA,EAIEnB,GAAIK,EAAOc,EAAM1G,OAAOqH,KAAKC,cAH3B1B,CAIV,EAoCG4J,GAAoC,SAAUhO,GAChD,IAAIiO,EAnCsC,SAA+C/f,GACzF,IAAI6Z,EAAS7Z,EAAK6Z,OACdH,EAAY1Z,EAAK0Z,UACjBtB,EAAYpY,EAAKoY,UACjBiB,EAAarZ,EAAKqZ,WAClBsE,EAAgB3d,EAAK2d,cACrB1N,EAAWyJ,EAAUnC,KAAKpI,UAAUR,OACpCmL,EAAKD,EAAOC,GAEhB,OAAK1B,GAIA0B,EAIW,YAAZA,EAAGla,KACE4f,GAAe,CACpB3F,OAAQA,EACRH,UAAWA,EACXL,WAAYA,EACZjB,UAAWA,EACXuF,cAAeA,IAIZgB,GAAc,CACnB9E,OAAQA,EACRR,WAAYA,EACZsE,cAAeA,IApBR1N,CAsBV,CAG2B+P,CAAsClO,GAC5DsG,EAAYtG,EAAKsG,UAErB,OADuBA,EAAY8D,GAA0B9D,EAAW2H,GAAuBA,CAEhG,EAEGE,GAAkB,SAAUhE,EAAU5D,GACxC,IAAIV,EAAO3B,GAASqC,EAAW4D,EAAS3L,OAAOxC,SAC3C8J,EAAe3B,GAAO0B,GAmB1B,MAZc,CACZX,MAPU5I,GAAQ,CAClBC,IAAKgK,EAAUzP,EACf2F,OAAQ8J,EAAUzP,EAAIqT,EAASjF,MAAMtI,OACrCF,KAAM6J,EAAU1P,EAChB2F,MAAO+J,EAAU1P,EAAIsT,EAASjF,MAAMvI,QAIpC6B,OAAQ,CACNxC,QAASmO,EAAS3L,OAAOxC,QACzBqJ,IAAK8E,EAAS3L,OAAO6G,IACrBtS,QAASwT,EACTV,KAAM,CACJ/b,MAAO+b,EACPC,aAAcA,IAKrB,EAED,SAASsI,GAAcC,EAAK9G,GAC1B,OAAO8G,EAAI1N,KAAI,SAAU0C,GACvB,OAAOkE,EAAWlE,EACnB,GACF,CAcD,IAsDIiL,GAAoC,SAAUpgB,GAChD,IAAIqgB,EAAsBrgB,EAAKqgB,oBAC3B3G,EAAY1Z,EAAK0Z,UAEjB4G,EAR0B,SAAUrE,EAAU/F,GAClD,OAAOL,GAAIoG,EAAS3L,OAAOqH,KAAKC,aAAc1B,EAC/C,CAM+BqK,CADfvgB,EAAKic,SAC6CoE,GAC7DrQ,EAASgG,GAASsK,EAAyB5G,EAAUnC,KAAKpI,UAAUR,QACxE,OAAOkH,GAAI6D,EAAU8G,OAAOrR,UAAUR,OAAQqB,EAC/C,EAEGyQ,GAAiC,SAAUzgB,GAC7C,IAAI0Z,EAAY1Z,EAAK0Z,UACjB7E,EAAc7U,EAAK6U,YACnB6L,EAAyB1gB,EAAK0gB,uBAC9BzE,EAAWjc,EAAKic,SAChBC,EAA4Blc,EAAKkc,0BACjCyE,EAAsB3gB,EAAK4gB,eAC3BA,OAAyC,IAAxBD,GAAyCA,EAC1DE,EAAe7K,GAAS0K,EAAwBhH,EAAUnC,KAAKpI,UAAUR,QAEzEmD,EAAO,CACTjD,OAFY+H,GAAiB8C,EAAUnC,KAAKpI,UAAW0R,GAGvDhM,YAAaA,EACbqH,0BAA2BA,EAC3BD,SAAUA,GAEZ,OAAO2E,EApkBoB,SAAgC9O,GAC3D,OAAOiK,IAAU/U,EAAAA,EAAAA,GAAS,CAAC,EAAG8K,EAAM,CAClCqK,yBArDgD1E,EAqDY3F,EAAK+C,YAAY4C,KApDxE,SAAUT,GACf,IAAIgE,EAAmBJ,GAAS5D,EAAM3I,IAAK2I,EAAMzI,QAC7C0M,EAAqBL,GAAS5D,EAAMxI,KAAMwI,EAAM1I,OACpD,OAAO,SAAU4I,GACf,OAAIO,IAAS8D,GACJP,EAAiB9D,EAAQ7I,MAAQ2M,EAAiB9D,EAAQ3I,QAG5D0M,EAAmB/D,EAAQ1I,OAASyM,EAAmB/D,EAAQ5I,MACvE,CACF,MAXuC,IAAUmJ,CAuDnD,CAgkByBqJ,CAAuBhP,GAAQ2K,GAAiB3K,EACzE,EAEGiP,GAAmB,SAAU/gB,GAC/B,IAAI8d,EAAkB9d,EAAK8d,gBACvBpE,EAAY1Z,EAAK0Z,UACjB7E,EAAc7U,EAAK6U,YACnBwE,EAAarZ,EAAKqZ,WAClB0E,EAAiB/d,EAAK+d,eACtB9B,EAAWjc,EAAKic,SAChB+E,EAA8BhhB,EAAKghB,4BACnCC,EAA0BjhB,EAAKihB,wBAC/BtD,EAAgB3d,EAAK2d,cAEzB,IAAK9I,EAAYqM,UACf,OAAO,KAGT,IAAIhE,EAAoB1D,GAA6B3E,EAAYsE,WAAWhE,GAAIkE,GAC5EvE,EAAeqF,GAAST,EAAW7E,GACnCgF,EAjyBmB,SAAU7Z,GACjC,IAAI8d,EAAkB9d,EAAK8d,gBACvBpE,EAAY1Z,EAAK0Z,UACjB7E,EAAc7U,EAAK6U,YACnBqI,EAAoBld,EAAKkd,kBACzBa,EAAiB/d,EAAK+d,eAE1B,IAAKlJ,EAAY0J,iBACf,OAAO,KAKT,IAFe3E,GAAkBmE,GAG/B,OAAO,KAGT,SAASoD,EAAUtS,GACjB,IAAIiL,EAAK,CACPla,KAAM,UACNwV,QAAS,CACPC,YAAaxG,EACbkG,YAAaF,EAAYsE,WAAWhE,KAGxC,OAAOnO,EAAAA,EAAAA,GAAS,CAAC,EAAG+W,EAAgB,CAClCjE,GAAIA,GAEP,CAED,IAAIU,EAAMuD,EAAerD,UAAUF,IAC/B4G,EAAY5G,EAAIxe,OAASwe,EAAI,GAAK,KAEtC,GAAIsD,EACF,OAAOsD,EAAYD,EAAUC,GAAa,KAG5C,IAAIC,EAAmBrH,GAAwBN,EAAWwD,GAE1D,IAAKkE,EACH,OAAKC,EAAiBrlB,OAKfmlB,EADIE,EAAiBA,EAAiBrlB,OAAS,GAChCmd,WAAWhE,IAJxB,KAOX,IAAImM,EAAiB5I,GAAU2I,GAAkB,SAAUE,GACzD,OAAOA,EAAEpI,WAAWhE,KAAOiM,CAC5B,KACqB,IAApBE,GAA4HvR,IAAU,GACxI,IAAIoO,EAAgBmD,EAAiB,EAErC,OAAInD,EAAgB,EACX,KAIFgD,EADME,EAAiBlD,GACNhF,WAAWhE,GACpC,CAquBcqM,CAAkB,CAC7B1D,gBAAiBA,EACjBpE,UAAWA,EACX7E,YAAaA,EACbqI,kBAAmBA,EACnBa,eAAgBA,KACZF,GAAgB,CACpBC,gBAAiBA,EACjBhJ,aAAcA,EACd4E,UAAWA,EACXL,WAAYA,EACZxE,YAAaA,EACbqI,kBAAmBA,EACnBa,eAAgBA,EAChB9B,SAAUA,EACV0B,cAAeA,IAGjB,IAAK9D,EACH,OAAO,KAGT,IAAIwG,EAAsBP,GAAiC,CACzDjG,OAAQA,EACRH,UAAWA,EACXtB,UAAWvD,EACXwE,WAAYA,EACZsE,cAAeA,IAWjB,GAT6B8C,GAA8B,CACzD/G,UAAWA,EACX7E,YAAaA,EACb6L,uBAAwBL,EACxBpE,SAAUA,EAASjF,MACnBkF,2BAA2B,EAC3B0E,gBAAgB,IAShB,MAAO,CACLa,gBANoBrB,GAAiC,CACrDC,oBAAqBA,EACrB3G,UAAWA,EACXuC,SAAUA,IAIVpC,OAAQA,EACR6H,kBAAmB,MAIvB,IAAIpL,EAAWN,GAASqK,EAAqBW,GACzCW,EAvJuB,SAAU3hB,GACrC,IAAI6Z,EAAS7Z,EAAK6Z,OACdoC,EAAWjc,EAAKic,SAChBpH,EAAc7U,EAAK6U,YACnBwE,EAAarZ,EAAKqZ,WAClBuI,EAAkB5hB,EAAK4hB,gBACvBC,EAAmB5B,GAAehE,EAAUpG,GAAIoG,EAAS3L,OAAOzL,QAAS+c,IACzEE,EAAoBjN,EAAYmC,MAAQmB,GAAgBtD,EAAagB,GAAIhB,EAAYmC,MAAM1G,OAAOzL,QAAS+c,IAAoB/M,EAC/HxS,EAAOwX,EAAOa,UACdqH,EAAqBrF,GAAsB,CAC7CC,cAAeuD,GAAc7d,EAAKmY,IAAKnB,GACvCxE,YAAaA,EACb8F,YAAad,EAAOc,YACpBsB,SAAU4F,EAAiB7K,MAC3B3U,KAAMA,EACNua,oBAAoB,IAElBoF,EAAsBtF,GAAsB,CAC9CC,cAAeuD,GAAc7d,EAAKmY,IAAKnB,GACvCxE,YAAaiN,EACbnH,YAAad,EAAOc,YACpBsB,SAAUA,EAASjF,MACnB3U,KAAMA,EACNua,oBAAoB,IAElBtC,EAAY,CAAC,EACbC,EAAU,CAAC,EACXsC,EAAS,CAACxa,EAAM0f,EAAoBC,GAoBxC,OAnBA3f,EAAKmY,IAAIvH,SAAQ,SAAUkC,GACzB,IAAIyC,EAzCR,SAAuBzC,EAAI0H,GACzB,IAAK,IAAI5gB,EAAI,EAAGA,EAAI4gB,EAAO7gB,OAAQC,IAAK,CACtC,IAAI2b,EAAeiF,EAAO5gB,GAAGse,QAAQpF,GAErC,GAAIyC,EACF,OAAOA,CAEV,CAED,OAAO,IACR,CA+BsBqK,CAAc9M,EAAI0H,GAEjCjF,EACF2C,EAAQpF,GAAMyC,EAIhB0C,EAAUnF,IAAM,CACjB,KAEenO,EAAAA,EAAAA,GAAS,CAAC,EAAG6S,EAAQ,CACnCa,UAAW,CACTF,IAAKnY,EAAKmY,IACVF,UAAWA,EACXC,QAASA,IAKd,CAuGgB2H,CAAsB,CACnCrI,OAAQA,EACRoC,SAAUA,EACVpH,YAAaA,EACbwE,WAAYA,EACZuI,gBAAiBtL,IAEnB,MAAO,CACLmL,gBAAiBR,EACjBpH,OAAQ8H,EACRD,kBAAmBpL,EAEtB,EAEG6L,GAAiB,SAAwB/J,GAC3C,IAAIgK,EAAOhK,EAAUlB,QAAQgB,OAE7B,OADCkK,GAA4GrS,IAAU,GAChHqS,CACR,EAoFGC,GAAgC,SAAuC3I,EAAWiE,GACpF,IAAI1N,EAAWyJ,EAAUnC,KAAKpI,UAAUR,OACxC,OAAO+O,GAAsBhE,EAAUP,WAAWhE,GAAIwI,GAAiB3H,GAAS/F,EAAU0N,EAAchD,YAAYzE,OAASjG,CAC9H,EACGqS,GAA0B,SAAiC5I,EAAWiE,GACxE,IAAI1N,EAAWyJ,EAAUnC,KAAKpI,UAC9B,OAAOuO,GAAsBhE,EAAUP,WAAWhE,GAAIwI,GAAiB/G,GAAiB3G,EAAUgG,GAAO0H,EAAchD,YAAYzE,QAAUjG,CAC9I,EAgCGsS,IAAiBvJ,EAAAA,GAAAA,IAAW,SAAwBvB,EAAMoH,GAC5D,IAAIjH,EAAeiH,EAAWpH,EAAKrB,MACnC,MAAO,CACLxa,MAAOgc,EACP1B,MAAOC,GAAMsB,EAAKrB,KAAMwB,GAE3B,IAwBG4K,GAAgB,SAAuBxL,EAAOG,GAChD,OAAOnQ,EAAAA,EAAAA,GAAS,CAAC,EAAGgQ,EAAO,CACzB1G,QAAQtJ,EAAAA,EAAAA,GAAS,CAAC,EAAGgQ,EAAM1G,OAAQ,CACjC6G,IAAKA,KAGV,EAEGsL,GAAiB,SAAwBrK,EAAWsB,EAAWL,GACjE,IAAIrC,EAAQoB,EAAUpB,MACpBmD,GAAST,EAAWtB,IAAyHrI,IAAU,GACvJqI,EAAUlB,QAAQM,iBAAiJzH,IAAU,GAC/K,IAAI2S,EAAkBH,GAAenK,EAAUX,KAAMiC,EAAUmF,YAAY3I,MACvEyM,EAnCgC,SAAyCvK,EAAWsK,EAAiBrJ,GACzG,IAAI5B,EAAOW,EAAUX,KAErB,GAAkC,YAA9BW,EAAUe,WAAWyJ,KACvB,OAAOzM,GAAMsB,EAAKrB,KAAMsM,EAAgBjL,EAAKrB,OAG/C,IAAIyM,EAAiBzK,EAAUlB,QAAQK,KAAK3H,WAAW6H,EAAKiE,MAMxDoH,EALkBtJ,GAA6BpB,EAAUe,WAAWhE,GAAIkE,GAC5CnY,QAAO,SAAU6hB,EAAKC,GACpD,OAAOD,EAAMC,EAAUxC,OAAO9Q,UAAU+H,EAAKiE,KAC9C,GAAE,GAC6BgH,EAAgBjL,EAAKrB,MACjByM,EAEpC,OAAIC,GAAiB,EACZ,KAGF3M,GAAMsB,EAAKrB,KAAM0M,EACzB,CAesBG,CAAgC7K,EAAWsK,EAAiBrJ,GAC7E6J,EAAQ,CACVR,gBAAiBA,EACjB3K,YAAa4K,EACbQ,kBAAmB/K,EAAUpB,MAAQoB,EAAUpB,MAAM1G,OAAO6G,IAAM,MAGpE,IAAKH,EAAO,CACV,IAAIoM,EAAW9L,GAAW,CACxBC,KAAMa,EAAUlB,QAAQK,KACxBC,gBAAiB0L,EACjBzL,KAAMW,EAAUX,KAChBT,MAAOoB,EAAUpB,QAGnB,OAAOhQ,EAAAA,EAAAA,GAAS,CAAC,EAAGoR,EAAW,CAC7BlB,QAASkM,GAEZ,CAED,IAAIC,EAAYV,EAAiB9M,GAAImB,EAAM1G,OAAO6G,IAAKwL,GAAkB3L,EAAM1G,OAAO6G,IAClFmM,EAAWd,GAAcxL,EAAOqM,GAChCnM,EAAUI,GAAW,CACvBC,KAAMa,EAAUlB,QAAQK,KACxBC,gBAAiB0L,EACjBzL,KAAMW,EAAUX,KAChBT,MAAOsM,IAET,OAAOtc,EAAAA,EAAAA,GAAS,CAAC,EAAGoR,EAAW,CAC7BlB,QAASA,EACTF,MAAOsM,GAEV,EA2GGC,GAAiB,SAAUvjB,GAC7B,IAAI8d,EAAkB9d,EAAK8d,gBACvBkD,EAA8BhhB,EAAKghB,4BACnCtH,EAAY1Z,EAAK0Z,UACjB8J,EAASxjB,EAAKwjB,OACdnK,EAAarZ,EAAKqZ,WAClBJ,EAAajZ,EAAKiZ,WAClBgD,EAAWjc,EAAKic,SAChB0B,EAAgB3d,EAAK2d,cACrB9I,EAxT2B,SAAU7U,GACzC,IAAI8d,EAAkB9d,EAAK8d,gBACvBuC,EAAsBrgB,EAAKqgB,oBAC3BzL,EAAS5U,EAAK4U,OACdqE,EAAajZ,EAAKiZ,WAClBgD,EAAWjc,EAAKic,SAChB/D,EAAStD,EAAOsC,QAAQgB,OAE5B,IAAKA,EACH,OAAO,KAGT,IAAIT,EAAO7C,EAAO6C,KACdgM,EAAyB7I,GAAS1C,EAAOT,EAAKjC,OAAQ0C,EAAOT,EAAKO,MAClE0L,EAAapK,GAAgBL,GAAYQ,QAAO,SAAUrB,GAC5D,OAAOA,IAAcxD,CACtB,IAAE6E,QAAO,SAAUrB,GAClB,OAAOA,EAAU8I,SAClB,IAAEzH,QAAO,SAAUrB,GAClB,OAAO1V,QAAQ0V,EAAUlB,QAAQgB,OAClC,IAAEuB,QAAO,SAAUrB,GAClB,OAAO2C,GAA+BkB,EAASjF,MAAxC+D,CAA+CoH,GAAe/J,GACtE,IAAEqB,QAAO,SAAUrB,GAClB,IAAIuL,EAAiBxB,GAAe/J,GAEpC,OAAI0F,EACK5F,EAAOT,EAAKmE,cAAgB+H,EAAelM,EAAKmE,cAGlD+H,EAAelM,EAAKkE,gBAAkBzD,EAAOT,EAAKkE,eAC1D,IAAElC,QAAO,SAAUrB,GAClB,IAAIuL,EAAiBxB,GAAe/J,GAChCwL,EAA8BhJ,GAAS+I,EAAelM,EAAKjC,OAAQmO,EAAelM,EAAKO,MAC3F,OAAOyL,EAAuBE,EAAelM,EAAKjC,SAAWiO,EAAuBE,EAAelM,EAAKO,OAAS4L,EAA4B1L,EAAOT,EAAKjC,SAAWoO,EAA4B1L,EAAOT,EAAKO,KAC7M,IAAE2B,MAAK,SAAUxY,EAAGC,GACnB,IAAIlF,EAAQimB,GAAehhB,GAAGsW,EAAKkE,gBAC/Bxf,EAASgmB,GAAe/gB,GAAGqW,EAAKkE,gBAEpC,OAAImC,EACK5hB,EAAQC,EAGVA,EAASD,CACjB,IAAEud,QAAO,SAAUrB,EAAW7Y,EAAOskB,GACpC,OAAO1B,GAAe/J,GAAWX,EAAKkE,kBAAoBwG,GAAe0B,EAAM,IAAIpM,EAAKkE,eACzF,IAED,IAAK+H,EAAW1nB,OACd,OAAO,KAGT,GAA0B,IAAtB0nB,EAAW1nB,OACb,OAAO0nB,EAAW,GAGpB,IAAII,EAAWJ,EAAWjK,QAAO,SAAUrB,GAEzC,OADwBwC,GAASuH,GAAe/J,GAAWX,EAAKjC,OAAQ2M,GAAe/J,GAAWX,EAAKO,KAChG+L,CAAkB1D,EAAoB5I,EAAKrB,MACnD,IAED,OAAwB,IAApB0N,EAAS9nB,OACJ8nB,EAAS,GAGdA,EAAS9nB,OAAS,EACb8nB,EAASnK,MAAK,SAAUxY,EAAGC,GAChC,OAAO+gB,GAAehhB,GAAGsW,EAAKjC,OAAS2M,GAAe/gB,GAAGqW,EAAKjC,MAC/D,IAAE,GAGEkO,EAAW/J,MAAK,SAAUxY,EAAGC,GAClC,IAAIlF,EAAQua,GAAQ4J,EAAqBvJ,GAAWqL,GAAehhB,KAC/DhF,EAASsa,GAAQ4J,EAAqBvJ,GAAWqL,GAAe/gB,KAEpE,OAAIlF,IAAUC,EACLD,EAAQC,EAGVgmB,GAAehhB,GAAGsW,EAAKjC,OAAS2M,GAAe/gB,GAAGqW,EAAKjC,MAC/D,IAAE,EACJ,CAwOmBwO,CAA0B,CAC1ClG,gBAAiBA,EACjBuC,oBAAqBW,EACrBpM,OAAQ4O,EACRvK,WAAYA,EACZgD,SAAUA,IAGZ,IAAKpH,EACH,OAAO,KAGT,IAAIqI,EAAoB1D,GAA6B3E,EAAYsE,WAAWhE,GAAIkE,GAC5EgG,EA1OqB,SAAUrf,GACnC,IAAIqgB,EAAsBrgB,EAAKqgB,oBAC3BpE,EAAWjc,EAAKic,SAChBpH,EAAc7U,EAAK6U,YACnBqI,EAAoBld,EAAKkd,kBACzBS,EAAgB3d,EAAK2d,cACrBsG,EAAS/G,EAAkBzD,QAAO,SAAUC,GAC9C,OAAO+C,GAAiB,CACtB5N,OAAQyT,GAAwB5I,EAAWiE,GAC3C9I,YAAaA,EACboH,SAAUA,EAASjF,MACnBkF,2BAA2B,GAE9B,IAAEvC,MAAK,SAAUxY,EAAGC,GACnB,IAAI8iB,EAAc5N,GAAS+J,EAAqBnE,GAA0BrH,EAAawN,GAA8BlhB,EAAGwc,KACpHwG,EAAc7N,GAAS+J,EAAqBnE,GAA0BrH,EAAawN,GAA8BjhB,EAAGuc,KAExH,OAAIuG,EAAcC,GACR,EAGNA,EAAcD,EACT,EAGF/iB,EAAEgY,WAAW5Z,MAAQ6B,EAAE+X,WAAW5Z,KAC1C,IACD,OAAO0kB,EAAO,IAAM,IACrB,CA8MsBG,CAAoB,CACvC/D,oBAAqBW,EACrB/E,SAAUA,EACVpH,YAAaA,EACbqI,kBAAmBA,EACnBS,cAAeA,IAEb9D,EAtGoB,SAAU7Z,GAClC,IAAIghB,EAA8BhhB,EAAKghB,4BACnC3B,EAAiBrf,EAAKqf,eACtBnC,EAAoBld,EAAKkd,kBACzBxD,EAAY1Z,EAAK0Z,UACjBL,EAAarZ,EAAKqZ,WAClBxE,EAAc7U,EAAK6U,YACnBoH,EAAWjc,EAAKic,SAChB0B,EAAgB3d,EAAK2d,cAEzB,IAAK0B,EAAgB,CACnB,GAAInC,EAAkBlhB,OACpB,OAAO,KAGT,IAAIqoB,EAAW,CACb3J,UAAWL,GACXM,YAAaP,GACbN,GAAI,CACFla,KAAM,UACNiV,YAAa,CACXE,YAAaF,EAAYsE,WAAWhE,GACpC5V,MAAO,KAIT+kB,EAA8BxE,GAAiC,CACjEjG,OAAQwK,EACR3K,UAAWA,EACXtB,UAAWvD,EACXwE,WAAYA,EACZsE,cAAeA,IAEbnG,EAAkB2C,GAAST,EAAW7E,GAAeA,EAAc4N,GAAe5N,EAAa6E,EAAWL,GAS9G,OAR6BoH,GAA8B,CACzD/G,UAAWA,EACX7E,YAAa2C,EACbkJ,uBAAwB4D,EACxBrI,SAAUA,EAASjF,MACnBkF,2BAA2B,EAC3B0E,gBAAgB,IAEcyD,EAAW,IAC5C,CAED,IAAIE,EAAsB7hB,QAAQse,EAA4BnM,EAAY4C,KAAKrB,OAASiJ,EAAe9H,KAAKpI,UAAUR,OAAOkG,EAAY4C,KAAKrB,OAE1I+H,EAAgB,WAClB,IAAIqG,EAAanF,EAAelG,WAAW5Z,MAE3C,OAAI8f,EAAelG,WAAWhE,KAAOuE,EAAUP,WAAWhE,IAItDoP,EAHKC,EAOFA,EAAa,CACrB,CAZmB,GAchB7J,EAAc4H,GAAe1N,EAAY4C,KAAMiC,EAAUmF,YAC7D,OAAOtB,GAAuB,CAC5B7D,UAAWA,EACXwD,kBAAmBA,EACnBrI,YAAaA,EACboH,SAAUA,EACVtB,YAAaA,EACbtY,KAAMgY,GACN9a,MAAO4e,GAEV,CA+BcsG,CAAmB,CAC9BzD,4BAA6BA,EAC7BnM,YAAaA,EACb6E,UAAWA,EACXL,WAAYA,EACZgG,eAAgBA,EAChBnC,kBAAmBA,EACnBjB,SAAUA,EACV0B,cAAeA,IAGjB,IAAK9D,EACH,OAAO,KAGT,IAAIwG,EAAsBP,GAAiC,CACzDjG,OAAQA,EACRH,UAAWA,EACXtB,UAAWvD,EACXwE,WAAYA,EACZsE,cAAeA,IAOjB,MAAO,CACL8D,gBANoBrB,GAAiC,CACrDC,oBAAqBA,EACrB3G,UAAWA,EACXuC,SAAUA,IAIVpC,OAAQA,EACR6H,kBAAmB,KAEtB,EAEGgD,GAAqB,SAAU7K,GACjC,IAAIC,EAAKD,EAAOC,GAEhB,OAAKA,EAIW,YAAZA,EAAGla,KACEka,EAAGjF,YAAYE,YAGjB+E,EAAG1E,QAAQL,YAPT,IAQV,EAOG4P,GAAmB,SAAU3kB,GAC/B,IAAI+D,EAAQ/D,EAAK+D,MACbnE,EAAOI,EAAKJ,KACZglB,EARiB,SAA0B/K,EAAQZ,GACvD,IAAI9D,EAAKuP,GAAkB7K,GAC3B,OAAO1E,EAAK8D,EAAW9D,GAAM,IAC9B,CAKsB0P,CAAiB9gB,EAAM8V,OAAQ9V,EAAM+gB,WAAW7L,YACjE8L,EAA4BriB,QAAQkiB,GACpCI,EAAOjhB,EAAM+gB,WAAW7L,WAAWlV,EAAMkhB,SAAS7M,UAAUjD,IAC5DqO,EAASoB,GAAkBI,EAC3BxJ,EAAYgI,EAAO/L,KAAK+D,UACxB0J,EAAmC,aAAd1J,IAAsC,YAAT5b,GAA+B,cAATA,IAAuC,eAAd4b,IAAwC,cAAT5b,GAAiC,eAATA,GAE5J,GAAIslB,IAAuBH,EACzB,OAAO,KAGT,IAAIjH,EAA2B,cAATle,GAAiC,eAATA,EAC1C8Z,EAAY3V,EAAM+gB,WAAWzL,WAAWtV,EAAMkhB,SAASvL,UAAUvE,IACjE6L,EAA8Bjd,EAAMc,QAAQ0S,KAAK4N,gBACjDC,EAAoBrhB,EAAM+gB,WAC1BzL,EAAa+L,EAAkB/L,WAC/BJ,EAAamM,EAAkBnM,WACnC,OAAOiM,EAAqBnE,GAAgB,CAC1CjD,gBAAiBA,EACjBkD,4BAA6BA,EAC7BtH,UAAWA,EACX7E,YAAa2O,EACbnK,WAAYA,EACZ4C,SAAUlY,EAAMkY,SAChBgF,wBAAyBld,EAAMc,QAAQ2b,OAAO6E,UAC9CtH,eAAgBha,EAAM8V,OACtB8D,cAAe5Z,EAAM4Z,gBAClB4F,GAAc,CACjBzF,gBAAiBA,EACjBkD,4BAA6BA,EAC7BtH,UAAWA,EACX8J,OAAQA,EACRnK,WAAYA,EACZJ,WAAYA,EACZgD,SAAUlY,EAAMkY,SAChB0B,cAAe5Z,EAAM4Z,eAExB,EAED,SAAS2H,GAAkBvhB,GACzB,MAAuB,aAAhBA,EAAMwhB,OAAwC,eAAhBxhB,EAAMwhB,KAC5C,CAED,SAASC,GAAkBxO,GACzB,IAAIgE,EAAmBJ,GAAS5D,EAAM3I,IAAK2I,EAAMzI,QAC7C0M,EAAqBL,GAAS5D,EAAMxI,KAAMwI,EAAM1I,OACpD,OAAO,SAAa4H,GAClB,OAAO8E,EAAiB9E,EAAMtN,IAAMqS,EAAmB/E,EAAMvN,EAC9D,CACF,CAwBD,SAAS8c,GAAmBjnB,GAC1B,IAAIknB,EAAgBlnB,EAAMknB,cACtBhM,EAAYlb,EAAMkb,UAClBT,EAAaza,EAAMya,WACnByK,EAAapK,GAAgBL,GAAYQ,QAAO,SAAUS,GAC5D,IAAKA,EAAKgH,UACR,OAAO,EAGT,IA/BmBhlB,EAAOC,EA+BtB+b,EAASgC,EAAKhD,QAAQgB,OAE1B,IAAKA,EACH,OAAO,EAGT,GArC0B/b,EAqCQ+b,KArCfhc,EAqCAwpB,GApCRlX,KAAOrS,EAAOmS,OAASpS,EAAMoS,MAAQnS,EAAOqS,MAAQtS,EAAMmS,IAAMlS,EAAOoS,QAAUrS,EAAMqS,OAASpS,EAAOkS,KAqChH,OAAO,EAGT,GAAImX,GAAkBtN,EAAlBsN,CAA0BE,EAAc/W,QAC1C,OAAO,EAGT,IAAI8I,EAAOyC,EAAKzC,KACZkO,EAAczN,EAAOvJ,OAAO8I,EAAKgE,eACjCE,EAAiB+J,EAAcjO,EAAKkE,gBACpCC,EAAe8J,EAAcjO,EAAKmE,cAClCgK,EAAchL,GAAS1C,EAAOT,EAAKkE,gBAAiBzD,EAAOT,EAAKmE,eAChEiK,EAAmBD,EAAYjK,GAC/BmK,EAAiBF,EAAYhK,GAEjC,OAAKiK,IAAqBC,IAItBD,EACKlK,EAAiBgK,EAGnB/J,EAAe+J,EACvB,IAED,OAAKjC,EAAW1nB,OAIU,IAAtB0nB,EAAW1nB,OACN0nB,EAAW,GAAGvK,WAAWhE,GAjEpC,SAAyBnV,GACvB,IAAI0lB,EAAgB1lB,EAAK0lB,cACrBhM,EAAY1Z,EAAK0Z,UACjBgK,EAAa1jB,EAAK0jB,WAClBqC,EAAcrM,EAAUnC,KAAKpI,UAAUR,OACvCsV,EAASP,EAAWjR,KAAI,SAAUuT,GACpC,IAAIvO,EAAOuO,EAAUvO,KACjB5I,EAASsH,GAAM6P,EAAUvO,KAAKrB,KAAMsP,EAAc/W,OAAO8I,EAAKrB,MAAO4P,EAAUzO,KAAKpI,UAAUR,OAAO8I,EAAKgE,gBAC9G,MAAO,CACLtG,GAAI6Q,EAAU7M,WAAWhE,GACzBmB,SAAUA,GAASyP,EAAalX,GAEnC,IAAE8K,MAAK,SAAUxY,EAAGC,GACnB,OAAOA,EAAEkV,SAAWnV,EAAEmV,QACvB,IACD,OAAO2N,EAAO,GAAKA,EAAO,GAAG9O,GAAK,IACnC,CAoDQ8Q,CAAgB,CACrBP,cAAeA,EACfhM,UAAWA,EACXgK,WAAYA,IAVL,IAYV,CAED,IAAIwC,GAAuB,SAA8B9D,EAAMlM,GAC7D,OAAO9H,GAAQwI,GAAiBwL,EAAMlM,GACvC,EAYD,SAASiQ,GAAenmB,GACtB,IAAI0a,EAAY1a,EAAK0a,UACjBvF,EAAKnV,EAAKmV,GACd,OAAOzS,QAAQgY,EAAUH,QAAQpF,IAAOuF,EAAUJ,UAAUnF,GAC7D,CAsBD,IAoHIiR,GAAiB,SAAUpmB,GAC7B,IAAIqmB,EAAarmB,EAAKqmB,WAClB3M,EAAY1Z,EAAK0Z,UACjBL,EAAarZ,EAAKqZ,WAClBJ,EAAajZ,EAAKiZ,WAClB8E,EAAiB/d,EAAK+d,eACtB9B,EAAWjc,EAAKic,SAChB0B,EAAgB3d,EAAK2d,cACrB+H,EAAgBQ,GAAqBxM,EAAUnC,KAAKpI,UAAWkX,GAC/DC,EAAgBb,GAAmB,CACrCC,cAAeA,EACfhM,UAAWA,EACXT,WAAYA,IAGd,IAAKqN,EACH,OAAO7L,GAGT,IAAI5F,EAAcoE,EAAWqN,GACzBpJ,EAAoB1D,GAA6B3E,EAAYsE,WAAWhE,GAAIkE,GAC5EkN,EA7KqB,SAAUnO,EAAWoO,GAC9C,IAAIxP,EAAQoB,EAAUpB,MAEtB,OAAKA,EAIEkP,GAAqBM,EAAMxP,EAAM1G,OAAOqH,KAAK/b,OAH3C4qB,CAIV,CAqKwCxE,CAAoBnN,EAAa6Q,GACxE,OApFsB,SAAU1lB,GAChC,IAAI0Z,EAAY1Z,EAAK0Z,UACjB+M,EAAazmB,EAAKumB,iCAClBxI,EAAiB/d,EAAK+d,eACtBlJ,EAAc7U,EAAK6U,YACnBqI,EAAoBld,EAAKkd,kBACzBS,EAAgB3d,EAAK2d,cAEzB,IAAK9I,EAAY0J,iBACf,OAAO,KAGT,IAAI9G,EAAO5C,EAAY4C,KACnBkD,EAAc4H,GAAe1N,EAAY4C,KAAMiC,EAAUmF,YACzDjH,EAAe+C,EAAY/e,MAC3B8qB,EAAcD,EAAWhP,EAAKjC,OAC9BmR,EAAYF,EAAWhP,EAAKO,KAE5B4G,EAAc/F,GADImB,GAAwBN,EAAWwD,IACjB,SAAU0J,GAChD,IAAIzR,EAAKyR,EAAMzN,WAAWhE,GACtB0R,EAAYD,EAAMrP,KAAKpI,UAEvB2X,EADYD,EAAUpP,EAAKiE,MAtBL,EAwBtBqL,EAA0BrJ,GAAsBvI,EAAIwI,GACpDmB,EAAcqH,GAAe,CAC/BzL,UAAWqD,EAAerD,UAC1BvF,GAAIA,IAGN,OAAI4R,EACEjI,EACK6H,EAAYE,EAAUpP,EAAKjC,OAASsR,GAAaH,EAAYE,EAAUpP,EAAKO,KAAO8O,EAGrFJ,EAAcG,EAAUpP,EAAKjC,OAASoC,EAAekP,GAAaJ,EAAcG,EAAUpP,EAAKO,KAAOJ,EAAekP,EAG1HhI,EACK6H,EAAYE,EAAUpP,EAAKjC,OAASoC,EAAekP,GAAaH,EAAYE,EAAUpP,EAAKO,KAAOJ,EAAekP,EAGnHJ,EAAcG,EAAUpP,EAAKjC,OAASsR,GAAaJ,EAAcG,EAAUpP,EAAKO,KAAO8O,CAC/F,IAED,OAAKlI,EAIQ,CACXjE,YAAaA,EACbD,UAAWqD,EAAerD,UAC1BZ,GAAI,CACFla,KAAM,UACNwV,QAAS,CACPC,YAAauJ,EAAYzF,WAAWhE,GACpCJ,YAAaF,EAAYsE,WAAWhE,MAVjC,IAeV,CAwBQ6R,CAAiB,CACtBT,iCAAkCA,EAClC7M,UAAWA,EACXqE,eAAgBA,EAChBlJ,YAAaA,EACbqI,kBAAmBA,EACnBS,cAAeA,KAhJK,SAAUnf,GAChC,IAAIioB,EAAajoB,EAAM+nB,iCACnB7M,EAAYlb,EAAMkb,UAClB7E,EAAcrW,EAAMqW,YACpBqI,EAAoB1e,EAAM0e,kBAC1B7a,EAAO7D,EAAM6D,KACb4Z,EAAWzd,EAAMyd,SACjB0B,EAAgBnf,EAAMmf,cACtBlG,EAAO5C,EAAY4C,KACnBkD,EAAc4H,GAAe1N,EAAY4C,KAAMiC,EAAUmF,YACzDjH,EAAe+C,EAAY/e,MAC3B8qB,EAAcD,EAAWhP,EAAKjC,OAC9BmR,EAAYF,EAAWhP,EAAKO,KAyB5BoF,EAzDN,SAAiBpd,GACf,IAAI0Z,EAAY1Z,EAAK0Z,UACjBjD,EAAUzW,EAAKyW,QACf0G,EAAand,EAAKmd,WAEtB,OAAK1G,EAIA0G,GAID1G,EAAQ0C,WAAW5Z,MAAQma,EAAUP,WAAW5Z,MAC3CkX,EAAQ0C,WAAW5Z,MAAQ,EAJ3BkX,EAAQ0C,WAAW5Z,MAJnB,IAYV,CAuCgB0nB,CAAQ,CACrBvN,UAAWA,EACXjD,QAzBYoC,GADQmB,GAAwBN,EAAWwD,IACrB,SAAU0J,GAC5C,IAAIzR,EAAKyR,EAAMzN,WAAWhE,GACtBwQ,EAAciB,EAAMrP,KAAKpI,UAAUR,OAAO8I,EAAKrB,MAC/C2Q,EAA0BrJ,GAAsBvI,EAAIwI,GACpDmB,EAAcqH,GAAe,CAC/BzL,UAAWrY,EACX8S,GAAIA,IAGN,OAAI4R,EACEjI,EACK6H,GAAahB,EAGfe,EAAcf,EAAc/N,EAGjCkH,EACK6H,GAAahB,EAAc/N,EAG7B8O,EAAcf,CACtB,IAICxI,WAAYhD,GAAST,EAAW7E,KAElC,OAAO0I,GAAuB,CAC5B7D,UAAWA,EACXwD,kBAAmBA,EACnBrI,YAAaA,EACboH,SAAUA,EACV5Z,KAAMA,EACNsY,YAAaA,EACbpb,MAAO6d,GAEV,CA8FO8J,CAAiB,CACrBX,iCAAkCA,EAClC7M,UAAWA,EACX7E,YAAaA,EACbqI,kBAAmBA,EACnB7a,KAAM0b,EAAerD,UACrBuB,SAAUA,EACV0B,cAAeA,GAElB,EAEGwJ,GAAqB,SAAUlO,EAAYmO,GAC7C,IAAItP,EAEJ,OAAO9Q,EAAAA,EAAAA,GAAS,CAAC,EAAGiS,IAAanB,EAAY,CAAC,GAAasP,EAAQjO,WAAWhE,IAAMiS,EAAStP,GAC9F,EAEGuP,GAAyB,SAAgCrnB,GAC3D,IAAI+d,EAAiB/d,EAAK+d,eACtBlE,EAAS7Z,EAAK6Z,OACdZ,EAAajZ,EAAKiZ,WAClB5W,EAAOqiB,GAAkB3G,GACzBuJ,EAAM5C,GAAkB7K,GAE5B,IAAKxX,EACH,OAAO4W,EAGT,GAAI5W,IAASilB,EACX,OAAOrO,EAGT,IAAIsO,EAAgBtO,EAAW5W,GAE/B,IAAKklB,EAAcrQ,QAAQM,gBACzB,OAAOyB,EAGT,IAAImO,EAhiBkB,SAA2BhP,GACjD,IAAI8K,EAAQ9K,EAAUlB,QAAQM,gBAC7B0L,GAAiInT,IAAU,GAC5I,IAAIiH,EAAQoB,EAAUpB,MAEtB,IAAKA,EAAO,CACV,IAAIwQ,EAAYlQ,GAAW,CACzBC,KAAMa,EAAUlB,QAAQK,KACxBE,KAAMW,EAAUX,KAChBT,MAAO,KACPQ,gBAAiB,OAGnB,OAAOxQ,EAAAA,EAAAA,GAAS,CAAC,EAAGoR,EAAW,CAC7BlB,QAASsQ,GAEZ,CAED,IAAIC,EAAevE,EAAMC,kBACxBsE,GAAkK1X,IAAU,GAC7K,IAAIuT,EAAWd,GAAcxL,EAAOyQ,GAChCvQ,EAAUI,GAAW,CACvBC,KAAMa,EAAUlB,QAAQK,KACxBE,KAAMW,EAAUX,KAChBT,MAAOsM,EACP9L,gBAAiB,OAEnB,OAAOxQ,EAAAA,EAAAA,GAAS,CAAC,EAAGoR,EAAW,CAC7BlB,QAASA,EACTF,MAAOsM,GAEV,CAigBeoE,CAAkBH,GAChC,OAAOJ,GAAkBlO,EAAYmO,EACtC,EAiCG3R,GAAU,SAAUzV,GACtB,IAAI+D,EAAQ/D,EAAK+D,MACb4jB,EAAwB3nB,EAAKyhB,gBAC7BmG,EAAmB5nB,EAAK8kB,WACxB+C,EAAiB7nB,EAAKic,SACtB6L,EAAe9nB,EAAK6Z,OACpB6H,EAAoB1hB,EAAK0hB,kBACzBzF,EAAW4L,GAAkB9jB,EAAMkY,SACnC6I,EAAa8C,GAAoB7jB,EAAM+gB,WACvCrD,EAAkBkG,GAAyB5jB,EAAMc,QAAQ2b,OAAO6E,UAChErV,EAASgG,GAASyL,EAAiB1d,EAAM+J,QAAQ0S,OAAO6E,WACxD7E,EAAS,CACXxQ,OAAQA,EACRqV,UAAW5D,EACX0D,gBAAiBtP,GAAI9R,EAAM+J,QAAQ0S,OAAO2E,gBAAiBnV,IAEzDuH,EAAO,CACT8N,UAAWxP,GAAI2K,EAAO6E,UAAWpJ,EAAS3L,OAAOzL,SACjDsgB,gBAAiBtP,GAAI2K,EAAO2E,gBAAiBlJ,EAAS3L,OAAOzL,SAC7DmL,OAAQ6F,GAAI2K,EAAOxQ,OAAQiM,EAAS3L,OAAOqH,KAAK/b,QAE9CiJ,EAAU,CACZ2b,OAAQA,EACRjJ,KAAMA,GAGR,GAAoB,eAAhBxT,EAAMwhB,MACR,OAAOve,EAAAA,EAAAA,GAAS,CACdue,MAAO,cACNxhB,EAAO,CACR+gB,WAAYA,EACZ7I,SAAUA,EACVpX,QAASA,IAIb,IAAI6U,EAAYoL,EAAWzL,WAAWtV,EAAMkhB,SAASvL,UAAUvE,IAC3D4S,EAAYD,GAAgB1B,GAAc,CAC5CC,WAAY9O,EAAKvH,OACjB0J,UAAWA,EACXL,WAAYyL,EAAWzL,WACvBJ,WAAY6L,EAAW7L,WACvB8E,eAAgBha,EAAM8V,OACtBoC,SAAUA,EACV0B,cAAe5Z,EAAM4Z,gBAEnBqK,EA7EuB,SAAUxpB,GACrC,IAAIkb,EAAYlb,EAAMkb,UAClBL,EAAa7a,EAAM6a,WACnBJ,EAAaza,EAAMya,WACnB8E,EAAiBvf,EAAMuf,eACvBlE,EAASrb,EAAMqb,OACfoO,EAAUZ,GAAuB,CACnCtJ,eAAgBA,EAChBlE,OAAQA,EACRZ,WAAYA,IAEVuK,EAASkB,GAAkB7K,GAE/B,IAAK2J,EACH,OAAOyE,EAGT,IAAI7P,EAAYa,EAAWuK,GAE3B,GAAIrJ,GAAST,EAAWtB,GACtB,OAAO6P,EAGT,GAAI7P,EAAUlB,QAAQM,gBACpB,OAAOyQ,EAGT,IAAIC,EAAUzF,GAAerK,EAAWsB,EAAWL,GACnD,OAAO8N,GAAkBc,EAASC,EACnC,CAgD+BC,CAAsB,CAClDzO,UAAWA,EACXG,OAAQkO,EACRhK,eAAgBha,EAAM8V,OACtBR,WAAYyL,EAAWzL,WACvBJ,WAAY6L,EAAW7L,aAezB,OAZajS,EAAAA,EAAAA,GAAS,CAAC,EAAGjD,EAAO,CAC/Bc,QAASA,EACTigB,WAAY,CACVzL,WAAYyL,EAAWzL,WACvBJ,WAAY+O,GAEdnO,OAAQkO,EACR9L,SAAUA,EACVyF,kBAAmBA,GAAqB,KACxC9E,oBAAoB8E,GAA4B,MAInD,EAQD,IAAI0G,GAAa,SAAUpoB,GACzB,IAAI6Z,EAAS7Z,EAAK6Z,OACdoC,EAAWjc,EAAKic,SAChB5C,EAAarZ,EAAKqZ,WAClBxE,EAAc7U,EAAK6U,YACnB+H,EAAqB5c,EAAK4c,mBAC1Bva,EAAOwX,EAAOa,UACdiC,EAbN,SAAyBwD,EAAK9G,GAC5B,OAAO8G,EAAI1N,KAAI,SAAU0C,GACvB,OAAOkE,EAAWlE,EACnB,GACF,CASqBkT,CAAgBhmB,EAAKmY,IAAKnB,GAC1CqB,EAAYgC,GAAsB,CACpCC,cAAeA,EACf9H,YAAaA,EACb8F,YAAad,EAAOc,YACpBsB,SAAUA,EAASjF,MACnB4F,mBAAoBA,EACpBva,KAAMA,IAER,OAAO2E,EAAAA,EAAAA,GAAS,CAAC,EAAG6S,EAAQ,CAC1Ba,UAAWA,GAEd,EAEG4N,GAA4B,SAAUtoB,GACxC,IAAI6Z,EAAS7Z,EAAK6Z,OACdH,EAAY1Z,EAAK0Z,UACjBtB,EAAYpY,EAAKoY,UACjBiB,EAAarZ,EAAKqZ,WAClB4C,EAAWjc,EAAKic,SAChB0B,EAAgB3d,EAAK2d,cACrB0C,EAAsBP,GAAiC,CACzDjG,OAAQA,EACRH,UAAWA,EACXL,WAAYA,EACZjB,UAAWA,EACXuF,cAAeA,IAEjB,OAAOyC,GAAiC,CACtCC,oBAAqBA,EACrB3G,UAAWA,EACXuC,SAAUA,GAEb,EAEGsM,GAAe,SAAUvoB,GAC3B,IAAI+D,EAAQ/D,EAAK+D,MACb6jB,EAAmB5nB,EAAK8kB,WACxB+C,EAAiB7nB,EAAKic,SACD,SAAvBlY,EAAMykB,cAAsFzY,IAAU,GACxG,IAAI0Y,EAAuB1kB,EAAM8V,OAC7BoC,EAAW4L,GAAkB9jB,EAAMkY,SACnC6I,EAAa8C,GAAoB7jB,EAAM+gB,WACvCzL,EAAayL,EAAWzL,WACxBJ,EAAa6L,EAAW7L,WACxBS,EAAYL,EAAWtV,EAAMkhB,SAASvL,UAAUvE,IAChDqO,EAASkB,GAAkB+D,GAC9BjF,GAAwHzT,IAAU,GACnI,IAAI8E,EAAcoE,EAAWuK,GACzB3J,EAASuO,GAAU,CACrBvO,OAAQ4O,EACRxM,SAAUA,EACVpH,YAAaA,EACbwE,WAAYA,IAEVoI,EAAkB6G,GAAyB,CAC7CzO,OAAQA,EACRH,UAAWA,EACXtB,UAAWvD,EACXwE,WAAYA,EACZ4C,SAAUA,EACV0B,cAAe5Z,EAAM4Z,gBAEvB,OAAOlI,GAAO,CACZoE,OAAQA,EACR4H,gBAAiBA,EACjB1d,MAAOA,EACP+gB,WAAYA,EACZ7I,SAAUA,GAEb,EASGyM,GAAiB,SAAU1oB,GAC7B,IAAI0Z,EAAY1Z,EAAK0Z,UACjBsL,EAAOhlB,EAAKglB,KACZ3L,EAAarZ,EAAKqZ,WAClB4C,EAAWjc,EAAKic,SAChBtB,EAAc4H,GAAeyC,EAAKvN,KAAMiC,EAAUmF,YAClD8J,EAAanP,GAA6BwL,EAAK7L,WAAWhE,GAAIkE,GAC9DuP,EAAWD,EAAWnpB,QAAQka,IAClB,IAAdkP,GAA2H7Y,IAAU,GACvI,IAhB+BoJ,EAgB3BwD,EAAgBgM,EAAW1pB,MAAM2pB,EAAW,GAC5ChL,EAAWjB,EAAczb,QAAO,SAAUgY,EAAUgB,GAEtD,OADAhB,EAASgB,EAAKf,WAAWhE,KAAM,EACxB+D,CACR,GAAE,CAAC,GACAyE,EAAgB,CAClBkL,cAAwC,YAAzB7D,EAAK7L,WAAWyJ,KAC/BjI,YAAaA,EACbiD,SAAUA,GAkBZ,MAAO,CACL/D,OATW,CACXa,UATcgC,GAAsB,CACpCC,cAAeA,EACf9H,YAAamQ,EACbrK,YAAaA,EACbtY,KAAM,KACN4Z,SAAUA,EAASjF,MACnB4F,oBAAoB,IAIpBjC,YAAaA,EACbb,GAAI,CACFla,KAAM,UACNiV,aAvC2BsE,EAuCEO,EAAUP,WAtCpC,CACL5Z,MAAO4Z,EAAW5Z,MAClBwV,YAAaoE,EAAWpE,gBAyCxB4I,cAAeA,EAElB,EASGnI,GAAQ,SAAe7U,GACrBmoB,CAKL,EACGC,GAAS,SAAgBpoB,GACvBmoB,CAKL,EA0BGE,GAAmC,SAAUhpB,GAC/C,IAAIipB,EAAYjpB,EAAKipB,UACjBC,EAAoBlpB,EAAKkpB,kBACzBjN,EAAWjc,EAAKic,SAChBkN,EAAqBlN,EAAS3L,OAAOqH,KAAK/b,MAC9C,OAAOqtB,EAAUxW,KAAI,SAAUiH,GAC7B,IAAI3E,EAAc2E,EAAUP,WAAWpE,YAEnCiC,EAdQ,SAAUoB,GACxB,IAAIpB,EAAQoB,EAAUpB,MAEtB,OADCA,GAAyGjH,IAAU,GAC7GiH,CACR,CAUeoS,CADGF,EAAkBnU,IAE7BsU,EAAwBrS,EAAM1G,OAAOqH,KAAK/b,MAE1C0tB,EAnCe,SAAUtpB,GAC/B,IAAI0Z,EAAY1Z,EAAK0Z,UACjB6P,EAAWvpB,EAAKgQ,OAChBwZ,EAAsBxpB,EAAKwpB,oBAC3BhJ,EAASxQ,GAAO0J,EAAU8G,OAAQ+I,GAClChS,EAAOlH,GAAWmQ,EAAQgJ,GAU9B,OARYxiB,EAAAA,EAAAA,GAAS,CAAC,EAAG0S,EAAW,CAClC+P,aAAaziB,EAAAA,EAAAA,GAAS,CAAC,EAAG0S,EAAU+P,YAAa,CAC/CjJ,OAAQA,IAEVA,OAAQA,EACRjJ,KAAMA,GAIT,CAmBemS,CAAgB,CAC1BhQ,UAAWA,EACX1J,OAHgB6F,GAAIsT,EAAoBE,GAIxCG,oBAAqBvN,EAAS3L,OAAOxC,UAEvC,OAAOwb,CACR,GACF,EAiFGK,GAAa,SAAoB5lB,GACnC,MAA8B,SAAvBA,EAAMykB,YACd,EAEGoB,GAAsB,SAA6B7lB,EAAOqjB,EAASyC,GACrE,IAAI/E,EAtJmB,SAAUA,EAAYsC,GAC7C,MAAO,CACL/N,WAAYyL,EAAWzL,WACvBJ,WAAYkO,GAAkBrC,EAAW7L,WAAYmO,GAExD,CAiJkB0C,CAAkB/lB,EAAM+gB,WAAYsC,GAErD,OAAKuC,GAAW5lB,IAAU8lB,EACjBpU,GAAO,CACZ1R,MAAOA,EACP+gB,WAAYA,IAITyD,GAAY,CACjBxkB,MAAOA,EACP+gB,WAAYA,GAEf,EAED,SAASiF,GAAwBhmB,GAC/B,OAAIA,EAAM+P,YAAqC,SAAvB/P,EAAMykB,cACrBxhB,EAAAA,EAAAA,GAAS,CACdue,MAAO,YACNxhB,EAAO,CACR2d,kBAAmB,OAIhB3d,CACR,CAED,IAAIimB,GAAO,CACTzE,MAAO,OACP0E,UAAW,KACXC,aAAa,GAEX7rB,GAAW,SAAU0F,EAAOpE,GAK9B,QAJc,IAAVoE,IACFA,EAAQimB,IAGU,UAAhBrqB,EAAOC,KACT,OAAOoH,EAAAA,EAAAA,GAAS,CAAC,EAAGgjB,GAAM,CACxBE,aAAa,IAIjB,GAAoB,oBAAhBvqB,EAAOC,KAA4B,CACnB,SAAhBmE,EAAMwhB,OAA+HxV,IAAU,GACjJ,IAAIoa,EAAkBxqB,EAAOsE,QACzBghB,EAAWkF,EAAgBlF,SAC3BxD,EAAkB0I,EAAgB1I,gBAClCxF,EAAWkO,EAAgBlO,SAC3B6I,EAAaqF,EAAgBrF,WAC7B0D,EAAe2B,EAAgB3B,aAC/B9O,EAAYoL,EAAWzL,WAAW4L,EAASvL,UAAUvE,IACrD6P,EAAOF,EAAW7L,WAAWgM,EAAS7M,UAAUjD,IAChDqL,EAAS,CACX6E,UAAW5D,EACX0D,gBAAiBzL,EAAU8G,OAAOrR,UAAUR,OAC5CqB,OAAQ4F,IAEN9H,EAAU,CACZ0S,OAAQA,EACRjJ,KAAM,CACJ8N,UAAWxP,GAAI2K,EAAO6E,UAAWpJ,EAAS3L,OAAOxC,SACjDqX,gBAAiBtP,GAAI2K,EAAO6E,UAAWpJ,EAAS3L,OAAOxC,SACvDkC,OAAQ6F,GAAI2K,EAAO6E,UAAWpJ,EAAS3L,OAAOqH,KAAK/b,SAGnDwuB,EAAwB9Q,GAAgBwL,EAAW7L,YAAYoR,OAAM,SAAUnQ,GACjF,OAAQA,EAAKoQ,aACd,IAEGC,EAAiB7B,GAAc,CACjChP,UAAWA,EACXsL,KAAMA,EACN3L,WAAYyL,EAAWzL,WACvB4C,SAAUA,IAERpC,EAAS0Q,EAAe1Q,OAmB5B,MAhBa,CACX0L,MAAO,WACPzR,YAAY,EACZmR,SAAUA,EACVuD,aAAcA,EACd1D,WAAYA,EACZhX,QAASA,EACTjJ,QAASiJ,EACTsc,sBAAuBA,EACvBvQ,OAAQA,EACR8D,cAZkB4M,EAAe5M,cAajC6M,aAAc3Q,EACdoC,SAAUA,EACVyF,kBAAmB,KACnB9E,mBAAoB,KAGvB,CAED,GAAoB,wBAAhBjd,EAAOC,KACT,MAAoB,eAAhBmE,EAAMwhB,OAA0C,iBAAhBxhB,EAAMwhB,MACjCxhB,GAGS,aAAhBA,EAAMwhB,OAAwIxV,IAAU,IAE5I/I,EAAAA,EAAAA,GAAS,CACrBue,MAAO,cACNxhB,EAAO,CACRwhB,MAAO,gBAMX,GAAoB,2BAAhB5lB,EAAOC,KAET,MADkB,eAAhBmE,EAAMwhB,OAA0C,iBAAhBxhB,EAAMwhB,OAA0JxV,IAAU,GAvM3K,SAAU/P,GAC7C,IAAI+D,EAAQ/D,EAAK+D,MACb0mB,EAAYzqB,EAAKyqB,UACrBjV,KACA,IAAIkV,EAAmBD,EAAUE,SAASlY,KAAI,SAAUgD,GACtD,IAAImV,EAAW7mB,EAAM+gB,WAAW7L,WAAWxD,EAAOV,aAElD,OADeoD,GAAgByS,EAAUnV,EAAOnF,OAEjD,IAEG2I,GAAajS,EAAAA,EAAAA,GAAS,CAAC,EAAGjD,EAAM+gB,WAAW7L,WAAY,CAAC,EAAGF,GAAe2R,IAE1EG,EAAmBzR,GAAe4P,GAAgC,CACpEC,UAAWwB,EAAUxB,UACrBC,kBAAmBjQ,EACnBgD,SAAUlY,EAAMkY,YAGd5C,GAAarS,EAAAA,EAAAA,GAAS,CAAC,EAAGjD,EAAM+gB,WAAWzL,WAAY,CAAC,EAAGwR,GAE/DJ,EAAUK,SAAS7X,SAAQ,SAAUkC,UAC5BkE,EAAWlE,EACnB,IACD,IAAI2P,EAAa,CACf7L,WAAYA,EACZI,WAAYA,GAEV0R,EAAYrG,GAAkB3gB,EAAM8V,QACpCmR,EAAUD,EAAYjG,EAAW7L,WAAW8R,GAAa,KACzDrR,EAAYoL,EAAWzL,WAAWtV,EAAMkhB,SAASvL,UAAUvE,IAC3D6P,EAAOF,EAAW7L,WAAWlV,EAAMkhB,SAAS7M,UAAUjD,IAEtDoV,EAAiB7B,GAAc,CACjChP,UAAWA,EACXsL,KAAMA,EACN3L,WAAYA,EACZ4C,SAAUlY,EAAMkY,WAEduO,EAAeD,EAAe1Q,OAC9B8D,EAAgB4M,EAAe5M,cAE/BI,EAAiBiN,GAAWA,EAAQzM,iBAAmBxa,EAAM8V,OAAS2Q,EACtE3Q,EAASuM,GAAc,CACzBC,WAAYtiB,EAAMc,QAAQ0S,KAAKvH,OAC/B0J,UAAWoL,EAAWzL,WAAWtV,EAAMkhB,SAASvL,UAAUvE,IAC1DkE,WAAYyL,EAAWzL,WACvBJ,WAAY6L,EAAW7L,WACvB8E,eAAgBA,EAChB9B,SAAUlY,EAAMkY,SAChB0B,cAAeA,IAEjBoL,KAEA,IAAIkC,GAAgBjkB,EAAAA,EAAAA,GAAS,CAC3Bue,MAAO,YACNxhB,EAAO,CACRwhB,MAAO,WACP1L,OAAQA,EACR2Q,aAAcA,EACd1F,WAAYA,EACZnH,cAAeA,EACff,oBAAoB,IAGtB,MAAoB,eAAhB7Y,EAAMwhB,MACD0F,GAGSjkB,EAAAA,EAAAA,GAAS,CACzBue,MAAO,gBACN0F,EAAe,CAChB1F,MAAO,eACP5P,OAAQ5R,EAAM4R,OACduV,WAAW,GAId,CA2HUC,CAA8B,CACnCpnB,MAAOA,EACP0mB,UAAW9qB,EAAOsE,UAItB,GAAoB,SAAhBtE,EAAOC,KAAiB,CAC1B,GAAoB,iBAAhBmE,EAAMwhB,MACR,OAAOxhB,EAGRuhB,GAAkBvhB,IAA4HgM,IAAU,GACzJ,IAAIqb,EAAmBzrB,EAAOsE,QAAQuc,OAEtC,OAAInkB,GAAQ+uB,EAAkBrnB,EAAMc,QAAQ2b,OAAO6E,WAC1CthB,EAGF0R,GAAO,CACZ1R,MAAOA,EACP0d,gBAAiB2J,EACjBvR,OAAQ8P,GAAW5lB,GAASA,EAAM8V,OAAS,MAE9C,CAED,GAAoB,4BAAhBla,EAAOC,KAAoC,CAC7C,GAAoB,iBAAhBmE,EAAMwhB,MACR,OAAOwE,GAAwBhmB,GAGjC,GAAoB,eAAhBA,EAAMwhB,MACR,OAAOwE,GAAwBhmB,GAGhCuhB,GAAkBvhB,IAA4HgM,IAAU,GACzJ,IAAIsb,EAAmB1rB,EAAOsE,QAC1BkR,EAAKkW,EAAiBlW,GACtBkD,EAAYgT,EAAiBhT,UAC7BxJ,EAAS9K,EAAM+gB,WAAW7L,WAAW9D,GAEzC,IAAKtG,EACH,OAAO9K,EAGT,IAAI2T,EAAWS,GAAgBtJ,EAAQwJ,GACvC,OAAOuR,GAAoB7lB,EAAO2T,GAAU,EAC7C,CAED,GAAoB,gCAAhB/X,EAAOC,KAAwC,CACjD,GAAoB,iBAAhBmE,EAAMwhB,MACR,OAAOxhB,EAGRuhB,GAAkBvhB,IAAiIgM,IAAU,GAC9J,IAAIub,EAAmB3rB,EAAOsE,QAC1BsnB,EAAMD,EAAiBnW,GACvB+L,EAAYoK,EAAiBpK,UAC7BsK,EAAUznB,EAAM+gB,WAAW7L,WAAWsS,GACzCC,GAA2Izb,IAAU,GACpJyb,EAAQtK,YAAcA,GAAgMnR,IAAU,GAElO,IAAIqX,GAAUpgB,EAAAA,EAAAA,GAAS,CAAC,EAAGwkB,EAAS,CAClCtK,UAAWA,IAGb,OAAO0I,GAAoB7lB,EAAOqjB,GAAS,EAC5C,CAED,GAAoB,wCAAhBznB,EAAOC,KAAgD,CACzD,GAAoB,iBAAhBmE,EAAMwhB,MACR,OAAOxhB,EAGRuhB,GAAkBvhB,IAAiIgM,IAAU,GAC9J,IAAI0b,EAAmB9rB,EAAOsE,QAC1BynB,EAAOD,EAAiBtW,GACxBoJ,EAAmBkN,EAAiBlN,iBACpCoN,EAAW5nB,EAAM+gB,WAAW7L,WAAWyS,GAC1CC,GAAsJ5b,IAAU,GAC/J4b,EAASpN,mBAAqBA,GAA6NxO,IAAU,GAEvQ,IAAI6b,GAAW5kB,EAAAA,EAAAA,GAAS,CAAC,EAAG2kB,EAAU,CACpCpN,iBAAkBA,IAGpB,OAAOqL,GAAoB7lB,EAAO6nB,GAAU,EAC7C,CAED,GAAoB,0BAAhBjsB,EAAOC,KAAkC,CAC3C,GAAoB,iBAAhBmE,EAAMwhB,OAA4C,mBAAhBxhB,EAAMwhB,MAC1C,OAAOxhB,EAGRuhB,GAAkBvhB,IAAqHgM,IAAU,GACjJhM,EAAMqmB,uBAAkJra,IAAU,GACnK,IAAI8b,EAAalsB,EAAOsE,QAAQoU,UAEhC,GAAIhc,GAAQ0H,EAAMkY,SAAS3L,OAAOzL,QAASgnB,GACzC,OAAO9B,GAAwBhmB,GAGjC,IAAI+nB,EAAY7L,GAAelc,EAAMkY,SAAU4P,GAE/C,OAAIlC,GAAW5lB,GACNwkB,GAAY,CACjBxkB,MAAOA,EACPkY,SAAU6P,IAIPrW,GAAO,CACZ1R,MAAOA,EACPkY,SAAU6P,GAEb,CAED,GAAoB,+BAAhBnsB,EAAOC,KAAuC,CAChD,IAAK0lB,GAAkBvhB,GACrB,OAAOA,EAGT,IAAIsf,EAAY1jB,EAAOsE,QAAQof,UAE/B,GAAIhnB,GAAQgnB,EAAWtf,EAAMkY,SAAS3L,OAAO6G,KAC3C,OAAOpT,EAGT,IAAIye,GAAgBxb,EAAAA,EAAAA,GAAS,CAAC,EAAGjD,EAAMkY,SAAU,CAC/C3L,QAAQtJ,EAAAA,EAAAA,GAAS,CAAC,EAAGjD,EAAMkY,SAAS3L,OAAQ,CAC1C6G,IAAKkM,MAIT,OAAOrc,EAAAA,EAAAA,GAAS,CACdue,MAAO,YACNxhB,EAAO,CACRkY,SAAUuG,GAEb,CAED,GAAoB,YAAhB7iB,EAAOC,MAAsC,cAAhBD,EAAOC,MAAwC,cAAhBD,EAAOC,MAAwC,eAAhBD,EAAOC,KAAuB,CAC3H,GAAoB,eAAhBmE,EAAMwhB,OAA0C,iBAAhBxhB,EAAMwhB,MACxC,OAAOxhB,EAGS,aAAhBA,EAAMwhB,OAA0IxV,IAAU,GAE5J,IAAIgc,EAAWpH,GAAgB,CAC7B5gB,MAAOA,EACPnE,KAAMD,EAAOC,OAGf,OAAKmsB,EAIEtW,GAAO,CACZ1R,MAAOA,EACP8V,OAAQkS,EAASlS,OACjB4H,gBAAiBsK,EAAStK,gBAC1BC,kBAAmBqK,EAASrK,oBAPrB3d,CASV,CAED,GAAoB,iBAAhBpE,EAAOC,KAAyB,CAClC,IAAI+V,EAAShW,EAAOsE,QAAQ0R,OAW5B,MAVkB,eAAhB5R,EAAMwhB,OAA4JxV,IAAU,IAE/J/I,EAAAA,EAAAA,GAAS,CACtBue,MAAO,gBACNxhB,EAAO,CACRwhB,MAAO,eACP2F,WAAW,EACXvV,OAAQA,GAIX,CAED,GAAoB,iBAAhBhW,EAAOC,KAAyB,CAClC,IAAIosB,EAAmBrsB,EAAOsE,QAC1BgmB,EAAY+B,EAAiB/B,UAC7BgC,EAAeD,EAAiBC,aAChCC,EAAsBF,EAAiBE,oBAS3C,MARkB,aAAhBnoB,EAAMwhB,OAAwC,iBAAhBxhB,EAAMwhB,OAAwIxV,IAAU,GACzK,CACbwV,MAAO,iBACP0E,UAAWA,EACXgC,aAAcA,EACdC,oBAAqBA,EACrBpH,WAAY/gB,EAAM+gB,WAGrB,CAED,MAAoB,kBAAhBnlB,EAAOC,KAEF,CACL2lB,MAAO,OACP0E,UAHetqB,EAAOsE,QAAQgmB,UAI9BC,aAAa,GAIVnmB,CACR,EAoBGooB,GAAuB,SAA8Bra,GACvD,MAAO,CACLlS,KAAM,yBACNqE,QAAS6N,EAEZ,EACGsa,GAAqB,WACvB,MAAO,CACLxsB,KAAM,sBACNqE,QAAS,KAEZ,EACGooB,GAAwB,SAA+Bva,GACzD,MAAO,CACLlS,KAAM,0BACNqE,QAAS6N,EAEZ,EACGwa,GAA2B,SAAkCxa,GAC/D,MAAO,CACLlS,KAAM,8BACNqE,QAAS6N,EAEZ,EACGya,GAAkC,SAAyCza,GAC7E,MAAO,CACLlS,KAAM,sCACNqE,QAAS6N,EAEZ,EACG0a,GAAO,SAAc1a,GACvB,MAAO,CACLlS,KAAM,OACNqE,QAAS6N,EAEZ,EAaG2a,GAAS,WACX,MAAO,CACL7sB,KAAM,UACNqE,QAAS,KAEZ,EACGyoB,GAAW,WACb,MAAO,CACL9sB,KAAM,YACNqE,QAAS,KAEZ,EACG0oB,GAAY,WACd,MAAO,CACL/sB,KAAM,aACNqE,QAAS,KAEZ,EACG2oB,GAAW,WACb,MAAO,CACLhtB,KAAM,YACNqE,QAAS,KAEZ,EAaG4oB,GAAe,SAAsB/a,GACvC,MAAO,CACLlS,KAAM,gBACNqE,QAAS6N,EAEZ,EACGgb,GAAO,SAAchb,GACvB,MAAO,CACLlS,KAAM,OACNqE,QAAS6N,EAEZ,EAOGib,GAAwB,WAC1B,MAAO,CACLntB,KAAM,0BACNqE,QAAS,KAEZ,EAuCD,IA6EI+oB,GAEI,0BAEJ5X,GACO,CACP0X,KAAM,EACNG,UAAW,IAHX7X,GAKK,CACL0X,KAAM,KAQNI,GAJW,GAI+B,KAjB/B,6BAkBXC,GAAc,CAChBC,MAAO,WAAaF,GACpBG,KAAM,aAAeH,GAAoB,aAAeA,GACxDJ,KAAM,SAAcQ,GAClB,IAAIC,EAASD,EAAW,KAAON,GAC/B,MAAO,aAAeO,EAAS,aAAeA,CAC/C,EACDC,YAAa,aAAeN,GAC5BzD,YAAa,UAAYyD,GAAoB,WAAaA,GAAoB,YAAcA,IAG1FO,GAAS,SAAgBzd,GAC3B,OAAO3T,GAAQ2T,EAAQ4F,IAAU,KAAO,aAAe5F,EAAOrH,EAAI,OAASqH,EAAOpH,EAAI,KACvF,EAEG8kB,GACMD,GADNC,GAEI,SAAc1d,EAAQ2d,GAC1B,IAAIC,EAAYH,GAAOzd,GAEvB,OAAK4d,EAIAD,EAIEC,EAAY,UAAYxY,GAAc0X,KAAO,IAH3Cc,EAJA,IAQV,EAGCC,GApCW,IAqCXC,GApCW,IAqCXC,GAAgBD,GAAcD,GAqF9BG,GAAU,SAAUhuB,GACtB,IAAId,EAAWc,EAAKd,SAChBQ,EAAWM,EAAKN,SACpB,OAAO,SAAUU,GACf,OAAO,SAAUT,GACf,GAAoB,SAAhBA,EAAOC,KAAX,CAKA,IAAImE,EAAQ7E,IACRyW,EAAShW,EAAOsE,QAAQ0R,OAE5B,GAAoB,eAAhB5R,EAAMwhB,OAOV,GAAoB,SAAhBxhB,EAAMwhB,MAAV,CAIuC,iBAAhBxhB,EAAMwhB,OAA4BxhB,EAAMmnB,WACgFnb,IAAU,GACvI,aAAhBhM,EAAMwhB,OAAwC,iBAAhBxhB,EAAMwhB,OAA+HxV,IAAU,GAC/K,IAAIkV,EAAWlhB,EAAMkhB,SACjBH,EAAa/gB,EAAM+gB,WACnBpL,EAAYoL,EAAWzL,WAAWtV,EAAMkhB,SAASvL,UAAUvE,IAE3D8Y,EAtEW,SAAUjuB,GAC7B,IAAIqZ,EAAarZ,EAAKqZ,WAClB1D,EAAS3V,EAAK2V,OACduY,EAAaluB,EAAKkuB,WAClBlJ,EAAOhlB,EAAKglB,KACZ/I,EAAWjc,EAAKic,SAChBuO,EAAexqB,EAAKwqB,aAExB,OAAK0D,EAAWpU,IAAiB,SAAXnE,EAcK,YAAvBuY,EAAWpU,GAAGla,KACT,CACLia,OAAQqU,EACRC,wBAAwB,GAQrB,CACLtU,QALoB7S,EAAAA,EAAAA,GAAS,CAAC,EAAGknB,EAAY,CAC7CxT,UAAWL,KAKX8T,wBAAwB,GAnBjB,CACLtU,OARyBuO,GAAU,CACnC/O,WAAYA,EACZQ,OAAQ2Q,EACR3V,YAAamQ,EACb/I,SAAUA,EACVW,oBAAoB,IAIpBuR,wBAAwB,EAmB7B,CAiC0BC,CAAc,CACjCzY,OAAQA,EACRuY,WAAYnqB,EAAM8V,OAClB8D,cAAe5Z,EAAM4Z,cACrB6M,aAAczmB,EAAMymB,aACpBxF,KAAMjhB,EAAM+gB,WAAW7L,WAAWlV,EAAMkhB,SAAS7M,UAAUjD,IAC3D8G,SAAUlY,EAAMkY,SAChB5C,WAAYtV,EAAM+gB,WAAWzL,aAE3BQ,EAASoU,EAAepU,OACxBsU,EAAyBF,EAAeE,uBAExCtZ,EAAcsZ,EAAyBvU,GAAkBC,GAAU,KACnEzE,EAAU+Y,EAAyBpU,GAAcF,GAAU,KAC3DjF,EAAS,CACXrV,MAAO0lB,EAASvL,UAAUna,MAC1BwV,YAAakQ,EAAS7M,UAAUjD,IAE9BjJ,EAAS,CACXmJ,YAAaqE,EAAUP,WAAWhE,GAClCvV,KAAM8Z,EAAUP,WAAWvZ,KAC3BgV,OAAQA,EACRe,OAAQA,EACRiN,KAAM7e,EAAMykB,aACZ3T,YAAaA,EACbO,QAASA,GAEP8W,EAxHoB,SAAUlsB,GACtC,IAAI6Z,EAAS7Z,EAAK6Z,OACdH,EAAY1Z,EAAK0Z,UACjBoL,EAAa9kB,EAAK8kB,WAClB7I,EAAWjc,EAAKic,SAChB0B,EAAgB3d,EAAK2d,cACrBtE,EAAayL,EAAWzL,WACxBJ,EAAa6L,EAAW7L,WACxBlE,EAAc2P,GAAkB7K,GAChChF,EAAcE,EAAckE,EAAWlE,GAAe,KACtDiQ,EAAO/L,EAAWS,EAAUP,WAAWpE,aACvCsZ,EAAkB/F,GAAyB,CAC7CzO,OAAQA,EACRH,UAAWA,EACXL,WAAYA,EACZsE,cAAeA,EACfvF,UAAWvD,GAAemQ,EAC1B/I,SAAUA,IAGZ,OADajG,GAASqY,EAAiB3U,EAAU8G,OAAOrR,UAAUR,OAEnE,CAmG+B2f,CAAuB,CAC/CzU,OAAQA,EACRH,UAAWA,EACXoL,WAAYA,EACZ7I,SAAUlY,EAAMkY,SAChB0B,cAAe5Z,EAAM4Z,gBAEnBsM,EAAY,CACdhF,SAAUlhB,EAAMkhB,SAChBtH,cAAe5Z,EAAM4Z,cACrBzR,OAAQA,EACR2N,OAAQA,GAIV,IAF2Bxd,GAAQ0H,EAAMc,QAAQ2b,OAAOxQ,OAAQkc,IAAwBxpB,QAAQwJ,EAAOkJ,SAEvG,CAOA,IAAI6W,EAlKa,SAAUjsB,GAC/B,IAAI6E,EAAU7E,EAAK6E,QACfgQ,EAAc7U,EAAK6U,YACnBc,EAAS3V,EAAK2V,OACd4Y,EAAajY,GAASzR,EAASgQ,GAEnC,GAAI0Z,GAAc,EAChB,OAAOV,GAGT,GAAIU,GAZsB,KAaxB,OAAOT,GAGT,IACIR,EAAWO,GAAcE,IADZQ,EAhBS,MAmB1B,OAAO7yB,QADuB,WAAXia,EAjBI,GAiBkB2X,EAAgCA,GAC9CkB,QAAQ,GACpC,CAgJwBC,CAAgB,CACjC5pB,QAASd,EAAMc,QAAQ2b,OAAOxQ,OAC9B6E,YAAaqX,EACbvW,OAAQA,IAOVjW,EArXY,SAAqBoS,GACrC,MAAO,CACLlS,KAAM,eACNqE,QAAS6N,EAEZ,CAgXc4c,CALE,CACTxC,oBAAqBA,EACrBD,aAAcA,EACdhC,UAAWA,IAVZ,MAJCvqB,EAASmtB,GAAa,CACpB5C,UAAWA,IArDd,OARCvqB,EAvRU,SAAqBoS,GACrC,MAAO,CACLlS,KAAM,eACNqE,QAAS6N,EAEZ,CAkRgB6c,CAAY,CACnBhZ,OAAQA,IAPX,MAFCvV,EAAKT,EAqFR,CACF,CACF,EAEGivB,GAAmB,WACrB,MAAO,CACLjmB,EAAG7F,OAAOyN,YACV3H,EAAG9F,OAAO0N,YAEb,EAmBD,SAASqe,GAAkB7uB,GACzB,IAAI8uB,EAAiB9uB,EAAK8uB,eAM1B,IAAIC,EAAYC,IAJhB,WACEF,EAAeF,KAChB,IAGGlc,EAzBN,SAAgC+C,GAC9B,MAAO,CACL1C,UAAW,SACXvJ,QAAS,CACPylB,SAAS,EACTC,SAAS,GAEXvd,GAAI,SAAYiC,GACVA,EAAM/E,SAAW/L,QAAU8Q,EAAM/E,SAAW/L,OAAOC,UAIvD0S,GACD,EAEJ,CAUe0Z,CAAuBJ,GACjC7b,EAASd,GAEb,SAASgd,IACP,OAAOlc,IAAWd,EACnB,CAcD,MAAO,CACLoD,MAbF,WACI4Z,KAA4Hrf,IAAU,GACxImD,EAASb,GAAWvP,OAAQ,CAAC4P,GAC9B,EAWC2c,KATF,WACGD,KAAuHrf,IAAU,GAClIgf,EAAU/c,SACVkB,IACAA,EAASd,EACV,EAKCgd,SAAUA,EAEb,CAED,IAIIE,GAAkB,SAAUxtB,GAC9B,IAAI1C,EAAWyvB,GAAkB,CAC/BC,eAAgB,SAAwBzW,GACtCvW,EAAMpC,SApeH,CACLE,KAAM,wBACNqE,QAkeoC,CAChCoU,UAAWA,IAEd,IAEH,OAAO,SAAUjY,GACf,OAAO,SAAUT,GACVP,EAASgwB,YAA8B,oBAAhBzvB,EAAOC,MACjCR,EAASoW,QAGPpW,EAASgwB,YAlBH,SAAmBzvB,GACjC,MAAuB,kBAAhBA,EAAOC,MAA4C,iBAAhBD,EAAOC,MAA2C,UAAhBD,EAAOC,IACpF,CAgBgC2vB,CAAU5vB,IACnCP,EAASiwB,OAGXjvB,EAAKT,EACN,CACF,CACF,EAgCG6vB,GAAmB,WACrB,IAAIC,EAAU,GAsCd,MAAO,CACL5Z,IAzBQ,SAAalE,GACrB,IAAI+d,EAAUC,YAAW,WACvB,OAdU,SAAiBD,GAC7B,IAAInwB,EAAQmZ,GAAU+W,GAAS,SAAUvV,GACvC,OAAOA,EAAKwV,UAAYA,CACzB,KACY,IAAXnwB,GAAmGwQ,IAAU,GAEzF0f,EAAQhwB,OAAOF,EAAO,GAChB,GAEtBiC,UACP,CAIUouB,CAAQF,EAChB,IACGG,EAAQ,CACVH,QAASA,EACTluB,SAAUmQ,GAEZ8d,EAAQnwB,KAAKuwB,EACd,EAiBCC,MAfU,WACV,GAAKL,EAAQzzB,OAAb,CAIA,IAAI+zB,EAAU,GAAGtc,OAAOgc,GACxBA,EAAQzzB,OAAS,EACjB+zB,EAAQ9c,SAAQ,SAAU4c,GACxBG,aAAaH,EAAMH,SACnBG,EAAMruB,UACP,GAPA,CAQF,EAMF,EAkCGyuB,GAAc,SAAqBtvB,EAAKgR,GAC1C6D,KACA7D,IACAoX,IACD,EAEGmH,GAAe,SAAsBjL,EAAUrC,GACjD,MAAO,CACLvN,YAAa4P,EAASvL,UAAUvE,GAChCvV,KAAMqlB,EAAS7M,UAAUxY,KACzBgV,OAAQ,CACNG,YAAakQ,EAAS7M,UAAUjD,GAChC5V,MAAO0lB,EAASvL,UAAUna,OAE5BqjB,KAAMA,EAET,EAEGgN,GAAU,SAAiBO,EAAWC,EAAMC,EAAUC,GACxD,GAAKH,EAAL,CAKA,IAAII,EAnIqB,SAAUF,GACnC,IAAIG,GAAY,EACZC,GAAY,EACZC,EAAYf,YAAW,WACzBc,GAAY,CACb,IAEGvkB,EAAS,SAAgBiG,GACvBqe,GAKAC,IAKJD,GAAY,EACZH,EAASle,GACT6d,aAAaU,GACd,EAMD,OAJAxkB,EAAOskB,UAAY,WACjB,OAAOA,CACR,EAEMtkB,CACR,CAuGkBykB,CAAoBN,GAIrCF,EAAUC,EAHK,CACbC,SAAUE,IAIPA,EAAWC,aACdH,EAASC,EAAkBF,GAT5B,MAFCC,EAASC,EAAkBF,GAa9B,EA4HGQ,GAAc,SAAUC,EAAeR,GACzC,IAAIS,EA3Hc,SAAUD,EAAeR,GAC3C,IAAIU,EAAevB,KACfwB,EAAW,KAuFXlE,EAAO,SAAc5gB,GACtB8kB,GAAqIjhB,IAAU,GAChJihB,EAAW,KACXf,GAAY,GAAa,WACvB,OAAOL,GAAQiB,IAAgBI,UAAW/kB,EAAQmkB,EAAU9a,GAC7D,GACF,EAgBD,MAAO,CACL2b,cA5GkB,SAAuB7b,EAAauN,GACpDoO,GAAgJjhB,IAAU,GAC5JkgB,GAAY,GAAmB,WAC7B,IAAIte,EAAKkf,IAAgBM,gBAErBxf,GAKFA,EAJa,CACX0D,YAAaA,EACbuN,KAAMA,GAIX,GACF,EAgGCwO,YA9FgB,SAAqBnM,EAAUrC,GAC7CoO,GAAkJjhB,IAAU,GAC9JkgB,GAAY,GAAqB,WAC/B,IAAIte,EAAKkf,IAAgBQ,kBAErB1f,GACFA,EAAGue,GAAajL,EAAUrC,GAE7B,GACF,EAsFCpN,MApFU,SAAeyP,EAAUrC,GACjCoO,GAAkJjhB,IAAU,GAC9J,IAAIqgB,EAAOF,GAAajL,EAAUrC,GAClCoO,EAAW,CACTpO,KAAMA,EACN0O,aAAcrM,EACdsM,aAAcnB,EAAKxb,OACnB4c,YAAa,MAEfT,EAAalb,KAAI,WACfoa,GAAY,GAAe,WACzB,OAAOL,GAAQiB,IAAgBY,YAAarB,EAAMC,EAAU9a,GAC7D,GACF,GACF,EAuECE,OArEW,SAAgBwP,EAAUpL,GACrC,IAAInE,EAAWkE,GAAkBC,GAC7BzE,EAAU2E,GAAcF,GAC3BmX,GAAqIjhB,IAAU,GAChJ,IAAI2hB,GA/Fc,SAAyBx1B,EAAOC,GACpD,GAAID,IAAUC,EACZ,OAAO,EAGT,IAAIw1B,EAAmBz1B,EAAMwd,UAAUvE,KAAOhZ,EAAOud,UAAUvE,IAAMjZ,EAAMwd,UAAU3E,cAAgB5Y,EAAOud,UAAU3E,aAAe7Y,EAAMwd,UAAU9Z,OAASzD,EAAOud,UAAU9Z,MAAQ1D,EAAMwd,UAAUna,QAAUpD,EAAOud,UAAUna,MAC9NqyB,EAAmB11B,EAAMkc,UAAUjD,KAAOhZ,EAAOic,UAAUjD,IAAMjZ,EAAMkc,UAAUxY,OAASzD,EAAOic,UAAUxY,KAC/G,OAAO+xB,GAAoBC,CAC5B,CAuF6BC,CAAgB5M,EAAU+L,EAASM,cAEzDI,IACFV,EAASM,aAAerM,GAG1B,IA3H+C/oB,EAAOC,EA2HlD21B,GA3HkD31B,EA2HauZ,IA1HxD,OADoCxZ,EA2HH80B,EAASO,eA1HxB,MAAVp1B,GAIR,MAATD,GAA2B,MAAVC,GAIdD,EAAM6Y,cAAgB5Y,EAAO4Y,aAAe7Y,EAAMqD,QAAUpD,EAAOoD,QAoHpEuyB,IACFd,EAASO,aAAe7b,GAG1B,IAAIqc,GAtHa,SAAwB71B,EAAOC,GAClD,OAAa,MAATD,GAA2B,MAAVC,GAIR,MAATD,GAA2B,MAAVC,GAIdD,EAAMmZ,cAAgBlZ,EAAOkZ,aAAenZ,EAAM6Y,cAAgB5Y,EAAO4Y,WACjF,CA4G6Bid,CAAehB,EAASQ,YAAapc,GAM/D,GAJI2c,IACFf,EAASQ,YAAcpc,GAGpBsc,GAAuBI,GAAuBC,EAAnD,CAIA,IAAI3B,GAAOppB,EAAAA,EAAAA,GAAS,CAAC,EAAGkpB,GAAajL,EAAU+L,EAASpO,MAAO,CAC7DxN,QAASA,EACTP,YAAaa,IAGfqb,EAAalb,KAAI,WACfoa,GAAY,GAAgB,WAC1B,OAAOL,GAAQiB,IAAgBoB,aAAc7B,EAAMC,EAAU9a,GAC9D,GACF,GAXA,CAYF,EAkCCua,MAhCU,WACTkB,GAAkHjhB,IAAU,GAC7HghB,EAAajB,OACd,EA8BChD,KAAMA,EACNoF,MArBU,WACV,GAAKlB,EAAL,CAIA,IAAI9kB,GAASlF,EAAAA,EAAAA,GAAS,CAAC,EAAGkpB,GAAac,EAASM,aAAcN,EAASpO,MAAO,CAC5ExN,QAAS,KACTP,YAAa,KACbc,OAAQ,WAGVmX,EAAK5gB,EARJ,CASF,EAWF,CAGiBimB,CAAatB,EAAeR,GAC5C,OAAO,SAAUvuB,GACf,OAAO,SAAU1B,GACf,OAAO,SAAUT,GACf,GAAoB,2BAAhBA,EAAOC,KAAX,CAKA,GAAoB,oBAAhBD,EAAOC,KAA4B,CACrC,IAAIqlB,EAAWtlB,EAAOsE,QAAQghB,SAI9B,OAHA6L,EAAUM,YAAYnM,EAAUtlB,EAAOsE,QAAQukB,cAC/CpoB,EAAKT,QACLmxB,EAAUtb,MAAMyP,EAAUtlB,EAAOsE,QAAQukB,aAE1C,CAED,GAAoB,kBAAhB7oB,EAAOC,KAA0B,CACnC,IAAIsM,EAASvM,EAAOsE,QAAQgmB,UAAU/d,OAItC,OAHA4kB,EAAUhB,QACV1vB,EAAKT,QACLmxB,EAAUhE,KAAK5gB,EAEhB,CAID,GAFA9L,EAAKT,GAEe,UAAhBA,EAAOC,KAAX,CAKA,IAAImE,EAAQjC,EAAM5C,WAEE,aAAhB6E,EAAMwhB,OACRuL,EAAUrb,OAAO1R,EAAMkhB,SAAUlhB,EAAM8V,OALxC,MAFCiX,EAAUoB,OArBX,MAFCpB,EAAUI,cAAcvxB,EAAOsE,QAAQoR,YAAa1V,EAAOsE,QAAQukB,aAgCtE,CACF,CACF,CACF,EAEG4J,GAAuB,SAAUtwB,GACnC,OAAO,SAAU1B,GACf,OAAO,SAAUT,GACf,GAAoB,4BAAhBA,EAAOC,KAAX,CAKA,IAAImE,EAAQjC,EAAM5C,WACA,mBAAhB6E,EAAMwhB,OAAqJxV,IAAU,GACvKjO,EAAMpC,SAASmtB,GAAa,CAC1B5C,UAAWlmB,EAAMkmB,YALlB,MAFC7pB,EAAKT,EASR,CACF,CACF,EAEG0yB,GAA8B,SAAUvwB,GAC1C,IAAIoR,EAAS,KACTtB,EAAU,KAcd,OAAO,SAAUxR,GACf,OAAO,SAAUT,GAOf,GANoB,UAAhBA,EAAOC,MAAoC,kBAAhBD,EAAOC,MAA4C,4BAAhBD,EAAOC,OAbvEgS,IACFK,qBAAqBL,GACrBA,EAAU,MAGRsB,IACFA,IACAA,EAAS,OAUT9S,EAAKT,GAEe,iBAAhBA,EAAOC,KAAX,CAIA,IAAI8S,EAAU,CACZK,UAAW,SACXvJ,QAAS,CACP0lB,SAAS,EACTD,SAAS,EACTqD,MAAM,GAER3gB,GAAI,WAGkB,mBAFR7P,EAAM5C,WAERqmB,OACRzjB,EAAMpC,SAjyBT,CACLE,KAAM,0BACNqE,QAAS,MAiyBJ,GAEH2N,EAAUG,uBAAsB,WAC9BH,EAAU,KACVsB,EAASb,GAAWvP,OAAQ,CAAC4P,GAC9B,GApBA,CAqBF,CACF,CACF,EAqFG6f,GAAe,SAAUzwB,GAC3B,OAAO,SAAU1B,GACf,OAAO,SAAUT,GAGf,GAFAS,EAAKT,GAEe,2BAAhBA,EAAOC,KAAX,CAIA,IAAI4yB,EAAkB1wB,EAAM5C,WAEE,iBAA1BszB,EAAgBjN,QAIhBiN,EAAgBtH,WAIpBppB,EAAMpC,SAASotB,GAAK,CAClBnX,OAAQ6c,EAAgB7c,UAbzB,CAeF,CACF,CACF,EAEG8c,GAEC7xB,EACDxC,GAAe,SAAU4B,GAC3B,IA5zBqB0yB,EA4zBjBC,EAAmB3yB,EAAK2yB,iBACxBC,EAAe5yB,EAAK4yB,aACpBC,EAAe7yB,EAAK6yB,aACpBhC,EAAgB7wB,EAAK6wB,cACrBR,EAAWrwB,EAAKqwB,SAChByC,EAAe9yB,EAAK8yB,aACxB,OAAOC,EAAc10B,GAASo0B,GpB7oGhC,WACE,IAAK,IAAI5xB,EAAOjE,UAAUZ,OAAQg3B,EAAc,IAAIjyB,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IACtFgyB,EAAYhyB,GAAQpE,UAAUoE,GAGhC,OAAO,SAAU5C,GACf,OAAO,WACL,IAAI0D,EAAQ1D,EAAYtB,WAAM,EAAQF,WAElCq2B,EAAY,WACd,MAAM,IAAIx0B,MAA8C1B,EAAuB,IAChF,EAEGm2B,EAAgB,CAClBh0B,SAAU4C,EAAM5C,SAChBQ,SAAU,WACR,OAAOuzB,EAAUn2B,WAAM,EAAQF,UAChC,GAECu2B,EAAQH,EAAYvgB,KAAI,SAAU2gB,GACpC,OAAOA,EAAWF,EACnB,IAED,OADAD,EAAYryB,EAAQ9D,WAAM,EAAQq2B,EAAtBvyB,CAA6BkB,EAAMpC,WACxC2zB,EAAAA,EAAAA,IAAcA,EAAAA,EAAAA,GAAc,CAAC,EAAGvxB,GAAQ,CAAC,EAAG,CACjDpC,SAAUuzB,GAEb,CACF,CACF,CoBinGgDK,EAl0B1BZ,EAk0BgDG,EAj0B9D,WACL,OAAO,SAAUzyB,GACf,OAAO,SAAUT,GACK,oBAAhBA,EAAOC,MACT8yB,EAAQ1B,WAGU,iBAAhBrxB,EAAOC,MACT8yB,EAAQa,SAAS5zB,EAAOsE,QAAQgmB,UAAU/d,OAAOyJ,QAG/B,UAAhBhW,EAAOC,MAAoC,kBAAhBD,EAAOC,MACpC8yB,EAAQc,UAGVpzB,EAAKT,EACN,CACF,CACF,GAwrB4B,SAAU+yB,GACvC,OAAO,WACL,OAAO,SAAUtyB,GACf,OAAO,SAAUT,GACK,kBAAhBA,EAAOC,MAA4C,UAAhBD,EAAOC,MAAoC,iBAAhBD,EAAOC,MACvE8yB,EAAQe,iBAGVrzB,EAAKT,EACN,CACF,CACF,CACF,CA2GqF+zB,CAAwBf,GAz3BhG,SAAUD,GACtB,OAAO,SAAU1yB,GACf,IAAId,EAAWc,EAAKd,SAChBQ,EAAWM,EAAKN,SACpB,OAAO,SAAUU,GACf,OAAO,SAAUT,GACf,GAAoB,SAAhBA,EAAOC,KAAX,CAKA,IAAIuqB,EAAkBxqB,EAAOsE,QACzBkR,EAAKgV,EAAgBhV,GACrBsM,EAAkB0I,EAAgB1I,gBAClC+G,EAAe2B,EAAgB3B,aAC/B1a,EAAU5O,IAEQ,mBAAlB4O,EAAQyX,OACV7lB,EAASmtB,GAAa,CACpB5C,UAAWnc,EAAQmc,aAIA,SAArB/qB,IAAWqmB,OAAmHxV,IAAU,GAC1IrQ,EAjGC,CACLE,KAAM,QACNqE,QAAS,OAgGLvE,EA5LC,CACLE,KAAM,yBACNqE,QA0LkC,CAC5BoR,YAAaF,EACbqT,aAAcA,KAEhB,IAGImL,EAAU,CACZte,YAAaF,EACbye,cALkB,CAClBC,yBAA2C,SAAjBrL,IAOxBsL,EAAwBpB,EAAQqB,gBAAgBJ,GAChD1O,EAAW6O,EAAsB7O,SACjCH,EAAagP,EAAsBhP,WACnC7I,EAAW6X,EAAsB7X,SAGrCvc,EAlMC,CACLE,KAAM,kBACNqE,QAgM4B,CACtBghB,SAAUA,EACVH,WAAYA,EACZrD,gBAAiBA,EACjB+G,aAAcA,EACdvM,SAAUA,IAvCX,MAFC7b,EAAKT,EA2CR,CACF,CACF,CACF,CAo0BgIq0B,CAAOrB,GAAmB3E,GAAQoE,GAAqBC,GAA4BE,GA7DlM,SAAUO,GAC1B,OAAO,SAAUhxB,GACf,OAAO,SAAU1B,GACf,OAAO,SAAUT,GACf,GARS,SAAoBA,GACnC,MAAuB,kBAAhBA,EAAOC,MAA4C,iBAAhBD,EAAOC,MAA2C,UAAhBD,EAAOC,IACpF,CAMWq0B,CAAWt0B,GAGb,OAFAmzB,EAAazD,YACbjvB,EAAKT,GAIP,GAAoB,oBAAhBA,EAAOC,KAA4B,CACrCQ,EAAKT,GACL,IAAIoE,EAAQjC,EAAM5C,WAGlB,MAFkB,aAAhB6E,EAAMwhB,OAA0IxV,IAAU,QAC5J+iB,EAAatd,MAAMzR,EAEpB,CAED3D,EAAKT,GACLmzB,EAAaxiB,OAAOxO,EAAM5C,WAC3B,CACF,CACF,CACF,CAsCgOg1B,CAAWpB,GAAexD,GAzG9O,SAAUoD,GACrB,IAAIyB,GAAa,EACjB,OAAO,WACL,OAAO,SAAU/zB,GACf,OAAO,SAAUT,GACf,GAAoB,oBAAhBA,EAAOC,KAKT,OAJAu0B,GAAa,EACbzB,EAAQ0B,eAAez0B,EAAOsE,QAAQghB,SAASvL,UAAUvE,IACzD/U,EAAKT,QACL+yB,EAAQ2B,0BAMV,GAFAj0B,EAAKT,GAEAw0B,EAAL,CAIA,GAAoB,UAAhBx0B,EAAOC,KAGT,OAFAu0B,GAAa,OACbzB,EAAQ2B,0BAIV,GAAoB,kBAAhB10B,EAAOC,KAA0B,CACnCu0B,GAAa,EACb,IAAIjoB,EAASvM,EAAOsE,QAAQgmB,UAAU/d,OAElCA,EAAOkJ,SACTsd,EAAQ4B,eAAepoB,EAAOmJ,YAAanJ,EAAOkJ,QAAQC,aAG5Dqd,EAAQ2B,yBACT,CAjBA,CAkBF,CACF,CACF,CACF,CAmE0QE,CAAM3B,GAAehC,GAAWC,EAAeR,KACzT,EA6FD,IAAImE,GAAgB,SAAUx0B,GAC5B,IAAIy0B,EAAez0B,EAAKy0B,aACpBC,EAAc10B,EAAK00B,YACnBhmB,EAAS1O,EAAK0O,OACdD,EAAQzO,EAAKyO,MACb4U,EAAYrN,GAAS,CACvBrN,EAAG+rB,EACH9rB,EAAG6rB,GACF,CACD9rB,EAAG8F,EACH7F,EAAG8F,IAML,MAJwB,CACtB/F,EAAGtL,KAAK8Z,IAAI,EAAGkM,EAAU1a,GACzBC,EAAGvL,KAAK8Z,IAAI,EAAGkM,EAAUza,GAG5B,EAEG+rB,GAAsB,WACxB,IAAIC,EAAM7xB,SAAS8xB,gBAEnB,OADCD,GAAyG7kB,IAAU,GAC7G6kB,CACR,EAEGE,GAAsB,WACxB,IAAIF,EAAMD,KAOV,OANgBH,GAAa,CAC3BC,aAAcG,EAAIH,aAClBC,YAAaE,EAAIF,YACjBjmB,MAAOmmB,EAAIG,YACXrmB,OAAQkmB,EAAII,cAGf,EAiCGC,GAAqB,SAAUj1B,GACjC,IAAIilB,EAAWjlB,EAAKilB,SAChB2O,EAAgB5zB,EAAK4zB,cACrBsB,EAAWl1B,EAAKk1B,SACpB1f,KACA,IAAIyG,EApCa,WACjB,IAAI3L,EAASse,KACTvL,EAAYyR,KACZzmB,EAAMiC,EAAO1H,EACb4F,EAAO8B,EAAO3H,EACdisB,EAAMD,KACNlmB,EAAQmmB,EAAIG,YACZrmB,EAASkmB,EAAII,aAqBjB,MAZe,CACbhe,MAPU5I,GAAQ,CAClBC,IAAKA,EACLG,KAAMA,EACNF,MALUE,EAAOC,EAMjBF,OALWF,EAAMK,IASjB4B,OAAQ,CACNxC,QAASwC,EACTzL,QAASyL,EACT6G,IAAKkM,EACL1L,KAAM,CACJ/b,MAAOga,GACPgC,aAAchC,KAKrB,CAOgBuf,GACXC,EAAenZ,EAAS3L,OAAOzL,QAC/BmgB,EAAOC,EAAS7M,UAChBa,EAAaic,EAAS9c,UAAUid,aAAarQ,EAAKplB,MAAM6S,KAAI,SAAUod,GACxE,OAAOA,EAAMnc,UAAU4hB,2BAA2BF,EAAcxB,EACjE,IACGva,EAAa6b,EAASxb,UAAU2b,aAAapQ,EAASvL,UAAU9Z,MAAM6S,KAAI,SAAUod,GACtF,OAAOA,EAAM0F,aAAaH,EAC3B,IACGtQ,EAAa,CACfzL,WAAYD,GAAeC,GAC3BJ,WAAYF,GAAeE,IAQ7B,OANA8P,KACa,CACXjE,WAAYA,EACZG,SAAUA,EACVhJ,SAAUA,EAGb,EAED,SAASuZ,GAAoBN,EAAUlE,EAAUnB,GAC/C,OAAIA,EAAM1W,WAAWhE,KAAO6b,EAAS7b,KAIjC0a,EAAM1W,WAAWvZ,OAASoxB,EAASpxB,MAMV,YAFlBs1B,EAAS9c,UAAUqd,QAAQ5F,EAAM1W,WAAWpE,aAE9CoE,WAAWyJ,KAMrB,CAED,IAAI8S,GAA0B,SAAUR,EAAUxhB,GAChD,IAAIiiB,EAAa,KACb7E,EAvMN,SAAyB9wB,GACvB,IAAIk1B,EAAWl1B,EAAKk1B,SAChBxhB,EAAY1T,EAAK0T,UACjBkiB,EATG,CACL3M,UAAW,CAAC,EACZ6B,SAAU,CAAC,EACXH,SAAU,CAAC,GAOT/Y,EAAU,KAEVikB,EAAU,WACRjkB,IAIJ8B,EAAU0Y,qBACVxa,EAAUG,uBAAsB,WAC9BH,EAAU,KACV4D,KACA,IAAIsgB,EAAWF,EACX3M,EAAY6M,EAAS7M,UACrB6B,EAAWgL,EAAShL,SACpBH,EAAWmL,EAASnL,SACpBzH,EAAQhlB,OAAO+K,KAAKggB,GAAWxW,KAAI,SAAU0C,GAC/C,OAAO+f,EAASxb,UAAU+b,QAAQtgB,GAAIogB,aAAa3f,GACpD,IAAE+D,MAAK,SAAUxY,EAAGC,GACnB,OAAOD,EAAEgY,WAAW5Z,MAAQ6B,EAAE+X,WAAW5Z,KAC1C,IACG6nB,EAAUlpB,OAAO+K,KAAK0hB,GAAUlY,KAAI,SAAU0C,GAGhD,MAAO,CACLJ,YAAaI,EACb7E,OAJU4kB,EAAS9c,UAAUqd,QAAQtgB,GACpBzB,UAAUqiB,yBAK9B,IACG7pB,EAAS,CACX+c,UAAW/F,EACX4H,SAAU5sB,OAAO+K,KAAK6hB,GACtBH,SAAUvD,GAEZwO,EA3CG,CACL3M,UAAW,CAAC,EACZ6B,SAAU,CAAC,EACXH,SAAU,CAAC,GAyCT5B,KACArV,EAAUsiB,QAAQ9pB,EACnB,IACF,EAoCD,MAAO,CACL2J,IAnCQ,SAAaga,GACrB,IAAI1a,EAAK0a,EAAM1W,WAAWhE,GAC1BygB,EAAQ3M,UAAU9T,GAAM0a,EACxB+F,EAAQjL,SAASkF,EAAM1W,WAAWpE,cAAe,EAE7C6gB,EAAQ9K,SAAS3V,WACZygB,EAAQ9K,SAAS3V,GAG1B0gB,GACD,EA0BC5b,OAxBW,SAAgB4V,GAC3B,IAAI1W,EAAa0W,EAAM1W,WACvByc,EAAQ9K,SAAS3R,EAAWhE,KAAM,EAClCygB,EAAQjL,SAASxR,EAAWpE,cAAe,EAEvC6gB,EAAQ3M,UAAU9P,EAAWhE,YACxBygB,EAAQ3M,UAAU9P,EAAWhE,IAGtC0gB,GACD,EAeCxG,KAbS,WACJzd,IAILK,qBAAqBL,GACrBA,EAAU,KACVgkB,EAhFK,CACL3M,UAAW,CAAC,EACZ6B,SAAU,CAAC,EACXH,SAAU,CAAC,GA8EZ,EAOF,CAqHiBsL,CAAgB,CAC9BviB,UAAW,CACTsiB,QAAStiB,EAAUyY,qBACnBC,mBAAoB1Y,EAAU0Y,oBAEhC8I,SAAUA,IA8DRgB,EAAa,SAAoBtiB,GAClC+hB,GAAoI5lB,IAAU,GAC/I,IAAIihB,EAAW2E,EAAW1Q,SAASvL,UAEhB,aAAf9F,EAAMhU,MACJ41B,GAAoBN,EAAUlE,EAAUpd,EAAMhY,QAChDk1B,EAAUjb,IAAIjC,EAAMhY,OAIL,YAAfgY,EAAMhU,MACJ41B,GAAoBN,EAAUlE,EAAUpd,EAAMhY,QAChDk1B,EAAU7W,OAAOrG,EAAMhY,MAG5B,EAsBG82B,EAAU,CACZpG,yBAjG6B,SAAkCnX,EAAI+L,GAClEgU,EAAS9c,UAAU+d,OAAOhhB,IAAkJpF,IAAU,GAElL4lB,GAILjiB,EAAU4Y,yBAAyB,CACjCnX,GAAIA,EACJ+L,UAAWA,GAEd,EAuFCqL,gCArFoC,SAAyCpX,EAAIoJ,GAC5EoX,IAIJT,EAAS9c,UAAU+d,OAAOhhB,IAAwJpF,IAAU,GAC7L2D,EAAU6Y,gCAAgC,CACxCpX,GAAIA,EACJoJ,iBAAkBA,IAErB,EA4ECpG,gBA9DoB,SAAyBhD,EAAIjF,GAC5CylB,GAILT,EAAS9c,UAAUqd,QAAQtgB,GAAIzB,UAAUpD,OAAOJ,EACjD,EAyDCmc,sBA3E0B,SAA+BlX,EAAIkD,GACxDsd,IAIJT,EAAS9c,UAAU+d,OAAOhhB,IAA6IpF,IAAU,GAClL2D,EAAU2Y,sBAAsB,CAC9BlX,GAAIA,EACJkD,UAAWA,IAEd,EAkEC0b,gBAzBoB,SAAyBJ,GAC3CgC,GAAuJ5lB,IAAU,GACnK,IAAI8f,EAAQqF,EAASxb,UAAU+b,QAAQ9B,EAAQte,aAC3C2P,EAAOkQ,EAAS9c,UAAUqd,QAAQ5F,EAAM1W,WAAWpE,aACnDkQ,EAAW,CACbvL,UAAWmW,EAAM1W,WACjBf,UAAW4M,EAAK7L,YAEd9Y,EAAc60B,EAAS/1B,UAAU+2B,GAKrC,OAJAP,EAAa,CACX1Q,SAAUA,EACV5kB,YAAaA,GAER40B,GAAkB,CACvBhQ,SAAUA,EACViQ,SAAUA,EACVtB,cAAeD,EAAQC,eAE1B,EAQCH,eAzDmB,WACnB,GAAKkC,EAAL,CAIA7E,EAAUzB,OACV,IAAIrK,EAAO2Q,EAAW1Q,SAAS7M,UAC/B8c,EAAS9c,UAAUid,aAAarQ,EAAKplB,MAAMqT,SAAQ,SAAU4c,GAC3D,OAAOA,EAAMnc,UAAU0iB,aACxB,IACDT,EAAWt1B,cACXs1B,EAAa,IARZ,CASF,GA+CD,OAAOjD,CACR,EAEG2D,GAAgB,SAAUtyB,EAAOoR,GACnC,MAAoB,SAAhBpR,EAAMwhB,OAIU,mBAAhBxhB,EAAMwhB,QAINxhB,EAAMkmB,UAAU/d,OAAOmJ,cAAgBF,GAIF,SAAlCpR,EAAMkmB,UAAU/d,OAAOyJ,OAC/B,EAEG2gB,GAAgB,SAAUpmB,GAC5BpN,OAAOyzB,SAASrmB,EAAOvH,EAAGuH,EAAOtH,EAClC,EAEG4tB,IAA0Bxd,EAAAA,GAAAA,IAAW,SAAUC,GACjD,OAAOK,GAAgBL,GAAYQ,QAAO,SAAUrB,GAClD,QAAKA,EAAU8I,aAIV9I,EAAUpB,KAKhB,GACF,IAUGyf,GAA8B,SAAUz2B,GAC1C,IAAI2O,EAAS3O,EAAK2O,OACdkG,EAAc7U,EAAK6U,YACnBoE,EAAajZ,EAAKiZ,WAEtB,GAAIpE,EAAa,CACf,IAAI6hB,EAAazd,EAAWpE,GAE5B,OAAK6hB,EAAW1f,MAIT0f,EAHE,IAIV,CAED,IAAI1T,EAvB2B,SAAoCnU,EAAQoK,GAC3E,IAAI0d,EAAQ9d,GAAK2d,GAAwBvd,IAAa,SAAUb,GAE9D,OADCA,EAAUpB,OAAqFjH,IAAU,GACnGyV,GAAkBpN,EAAUpB,MAAMK,cAAlCmO,CAAiD3W,EACzD,IACD,OAAO8nB,CACR,CAiBiBC,CAA2BjoB,EAAQsK,GACnD,OAAO+J,CACR,EAEG6T,GACmB,IADnBA,GAEqB,IAFrBA,GAGc,GAHdA,GAII,SAAcC,GAClB,OAAOz5B,KAAKmZ,IAAIsgB,EAAY,EAC7B,EANCD,GAOiB,CACjBE,gBAAiB,KACjBC,aAAc,KAcdC,GAAiB,SAAUj3B,GAC7B,IAAIk3B,EAAel3B,EAAKk3B,aACpBC,EAAan3B,EAAKm3B,WAClBtyB,EAAU7E,EAAK6E,QACfuyB,EAAQD,EAAaD,EAEzB,OAAc,IAAVE,EAEK,GAGYvyB,EAAUqyB,GACGE,CAEnC,EA2BGJ,GAAeH,GAAyBG,aACxCK,GAASR,GAAyBE,gBAwBlCO,GAAY,SAAUt3B,GACxB,IAAIu3B,EAAiBv3B,EAAKu3B,eACtBC,EAAax3B,EAAKw3B,WAClBC,EAAgBz3B,EAAKy3B,cACrBC,EAAyB13B,EAAK03B,uBAC9BpnB,EArDsB,SAAUinB,EAAgBC,GACpD,GAAID,EAAiBC,EAAWG,mBAC9B,OAAO,EAGT,GAAIJ,GAAkBC,EAAWI,iBAC/B,OAAOf,GAGT,GAAIU,IAAmBC,EAAWG,mBAChC,OAZY,EAed,IAAIE,EAAiCZ,GAAc,CACjDC,aAAcM,EAAWI,iBACzBT,WAAYK,EAAWG,mBACvB9yB,QAAS0yB,IAGPjnB,EAASumB,GAAwBA,GADE,EAAIgB,GAE3C,OAAOx6B,KAAKy6B,KAAKxnB,EAClB,CAgCcynB,CAAqBR,EAAgBC,GAElD,OAAe,IAAXlnB,EACK,EAGJonB,EAIEr6B,KAAK8Z,IAtCW,SAAU6gB,EAAgBP,GACjD,IAAIP,EAAeO,EACfN,EAAaE,GAEbY,EADMC,KAAK5Q,MACK4P,EAEpB,GAAIe,GAAWZ,GACb,OAAOW,EAGT,GAAIC,EAAUjB,GACZ,OAtCY,EAyCd,IAAImB,EAAyClB,GAAc,CACzDC,aAAcF,GACdG,WAAYA,EACZtyB,QAASozB,IAEP3nB,EAAS0nB,EAAiBnB,GAAYsB,GAC1C,OAAO96B,KAAKy6B,KAAKxnB,EAClB,CAiBiB8nB,CAAkB9nB,EAAQmnB,GAjE5B,GA8DLnnB,CAIV,EAEG+nB,GAAmB,SAAUr4B,GAC/B,IAAIs4B,EAAYt4B,EAAKs4B,UACjBC,EAAkBv4B,EAAKu4B,gBACvBd,EAAgBz3B,EAAKy3B,cACrBhgB,EAAOzX,EAAKyX,KACZigB,EAAyB13B,EAAK03B,uBAC9BF,EApGuB,SAAUc,EAAW7gB,GAOhD,MAJiB,CACfkgB,mBAHuBW,EAAU7gB,EAAKiE,MAAQmb,GAI9Ce,iBAHqBU,EAAU7gB,EAAKiE,MAAQmb,GAM/C,CA4FkB2B,CAAsBF,EAAW7gB,GAGlD,OAFoB8gB,EAAgB9gB,EAAKO,KAAOugB,EAAgB9gB,EAAKjC,OAG5D8hB,GAAS,CACdC,eAAgBgB,EAAgB9gB,EAAKO,KACrCwf,WAAYA,EACZC,cAAeA,EACfC,uBAAwBA,KAIpB,EAAIJ,GAAS,CACnBC,eAAgBgB,EAAgB9gB,EAAKjC,OACrCgiB,WAAYA,EACZC,cAAeA,EACfC,uBAAwBA,GAE3B,EAuBGe,GAAU37B,IAAM,SAAUlB,GAC5B,OAAiB,IAAVA,EAAc,EAAIA,CAC1B,IACG88B,GAAa,SAAU14B,GACzB,IAAIy3B,EAAgBz3B,EAAKy3B,cACrBa,EAAYt4B,EAAKs4B,UACjBphB,EAAUlX,EAAKkX,QACfvI,EAAS3O,EAAK2O,OACd+oB,EAAyB13B,EAAK03B,uBAC9Ba,EAAkB,CACpBlqB,IAAKM,EAAO/F,EAAI0vB,EAAUjqB,IAC1BC,MAAOgqB,EAAUhqB,MAAQK,EAAOhG,EAChC4F,OAAQ+pB,EAAU/pB,OAASI,EAAO/F,EAClC4F,KAAMG,EAAOhG,EAAI2vB,EAAU9pB,MAEzB5F,EAAIyvB,GAAgB,CACtBC,UAAWA,EACXC,gBAAiBA,EACjBd,cAAeA,EACfhgB,KAAM8D,GACNmc,uBAAwBA,IAEtB/uB,EAAI0vB,GAAgB,CACtBC,UAAWA,EACXC,gBAAiBA,EACjBd,cAAeA,EACfhgB,KAAMqE,GACN4b,uBAAwBA,IAEtBiB,EAAWF,GAAQ,CACrB9vB,EAAGA,EACHC,EAAGA,IAGL,GAAIvM,GAAQs8B,EAAU/iB,IACpB,OAAO,KAGT,IAAIgjB,EA3DqB,SAAU54B,GACnC,IAAIs4B,EAAYt4B,EAAKs4B,UACjBphB,EAAUlX,EAAKkX,QACf8gB,EAAiBh4B,EAAKg4B,eACtBa,EAAqB3hB,EAAQxI,OAAS4pB,EAAU5pB,OAChDoqB,EAAuB5hB,EAAQzI,MAAQ6pB,EAAU7pB,MAErD,OAAKqqB,GAAyBD,EAI1BC,GAAwBD,EACnB,KAGF,CACLlwB,EAAGmwB,EAAuB,EAAId,EAAervB,EAC7CC,EAAGiwB,EAAqB,EAAIb,EAAepvB,GATpCovB,CAWV,CAwCee,CAAoB,CAChCT,UAAWA,EACXphB,QAASA,EACT8gB,eAAgBW,IAGlB,OAAKC,EAIEv8B,GAAQu8B,EAAShjB,IAAU,KAAOgjB,EAHhC,IAIV,EAEGI,GAAiBl8B,IAAM,SAAUlB,GACnC,OAAc,IAAVA,EACK,EAGFA,EAAQ,EAAI,GAAK,CACzB,IACGq9B,GAAa,WACf,IAAIC,EAAe,SAAsBrqB,EAAQsI,GAC/C,OAAItI,EAAS,EACJA,EAGLA,EAASsI,EACJtI,EAASsI,EAGX,CACR,EAED,OAAO,SAAUnX,GACf,IAAI6E,EAAU7E,EAAK6E,QACfsS,EAAMnX,EAAKmX,IACXjH,EAASlQ,EAAKkQ,OACdipB,EAAetjB,GAAIhR,EAASqL,GAC5BkpB,EAAU,CACZzwB,EAAGuwB,EAAaC,EAAaxwB,EAAGwO,EAAIxO,GACpCC,EAAGswB,EAAaC,EAAavwB,EAAGuO,EAAIvO,IAGtC,OAAIvM,GAAQ+8B,EAASxjB,IACZ,KAGFwjB,CACR,CACF,CA7BgB,GA8BbC,GAAqB,SAA4B76B,GACnD,IAAI86B,EAAS96B,EAAM2Y,IACftS,EAAUrG,EAAMqG,QAChBqL,EAAS1R,EAAM0R,OACfiH,EAAM,CACRxO,EAAGtL,KAAK8Z,IAAItS,EAAQ8D,EAAG2wB,EAAO3wB,GAC9BC,EAAGvL,KAAK8Z,IAAItS,EAAQ+D,EAAG0wB,EAAO1wB,IAE5B2wB,EAAiBP,GAAe9oB,GAChCkpB,EAAUH,GAAW,CACvB9hB,IAAKA,EACLtS,QAASA,EACTqL,OAAQqpB,IAGV,OAAKH,IAIoB,IAArBG,EAAe5wB,GAAyB,IAAdywB,EAAQzwB,GAIb,IAArB4wB,EAAe3wB,GAAyB,IAAdwwB,EAAQxwB,EAKvC,EACG4wB,GAAkB,SAAyBvd,EAAU/L,GACvD,OAAOmpB,GAAmB,CACxBx0B,QAASoX,EAAS3L,OAAOzL,QACzBsS,IAAK8E,EAAS3L,OAAO6G,IACrBjH,OAAQA,GAEX,EAcGupB,GAAqB,SAA4BrhB,EAAWlI,GAC9D,IAAI8G,EAAQoB,EAAUpB,MAEtB,QAAKA,GAIEqiB,GAAmB,CACxBx0B,QAASmS,EAAM1G,OAAOzL,QACtBsS,IAAKH,EAAM1G,OAAO6G,IAClBjH,OAAQA,GAEX,EAyDGwpB,GAAY,SAAU15B,GACxB,IAAI+D,EAAQ/D,EAAK+D,MACb0zB,EAAgBz3B,EAAKy3B,cACrBC,EAAyB13B,EAAK03B,uBAC9BpB,EAAet2B,EAAKs2B,aACpBne,EAAkBnY,EAAKmY,gBACvBxJ,EAAS5K,EAAMc,QAAQ0S,KAAK4N,gBAE5BjO,EADYnT,EAAM+gB,WAAWzL,WAAWtV,EAAMkhB,SAASvL,UAAUvE,IAC7CoC,KAAK7H,UAE7B,GAAI3L,EAAMqmB,sBAAuB,CAC/B,IAEIuP,EAnDqB,SAAU35B,GACrC,IAAIic,EAAWjc,EAAKic,SAChB/E,EAAUlX,EAAKkX,QACfvI,EAAS3O,EAAK2O,OACd8oB,EAAgBz3B,EAAKy3B,cACrBC,EAAyB13B,EAAK03B,uBAC9BpnB,EAASooB,GAAU,CACrBjB,cAAeA,EACfa,UAAWrc,EAASjF,MACpBE,QAASA,EACTvI,OAAQA,EACR+oB,uBAAwBA,IAE1B,OAAOpnB,GAAUkpB,GAAgBvd,EAAU3L,GAAUA,EAAS,IAC/D,CAqCiBspB,CAAsB,CAClCnC,cAAeA,EACfxb,SAJalY,EAAMkY,SAKnB/E,QAASA,EACTvI,OAAQA,EACR+oB,uBAAwBA,IAG1B,GAAIiC,EAEF,YADArD,EAAaqD,EAGhB,CAED,IAAIvhB,EAAYqe,GAA2B,CACzC9nB,OAAQA,EACRkG,YAAa6P,GAAkB3gB,EAAM8V,QACrCZ,WAAYlV,EAAM+gB,WAAW7L,aAG/B,GAAKb,EAAL,CAIA,IAAIlI,EA3D0B,SAAUlQ,GACxC,IAAIoY,EAAYpY,EAAKoY,UACjBlB,EAAUlX,EAAKkX,QACfvI,EAAS3O,EAAK2O,OACd8oB,EAAgBz3B,EAAKy3B,cACrBC,EAAyB13B,EAAK03B,uBAC9B1gB,EAAQoB,EAAUpB,MAEtB,IAAKA,EACH,OAAO,KAGT,IAAI1G,EAASooB,GAAU,CACrBjB,cAAeA,EACfa,UAAWthB,EAAMK,cACjBH,QAASA,EACTvI,OAAQA,EACR+oB,uBAAwBA,IAE1B,OAAOpnB,GAAUmpB,GAAmBrhB,EAAW9H,GAAUA,EAAS,IACnE,CAuCcupB,CAAyB,CACpCpC,cAAeA,EACfrf,UAAWA,EACXlB,QAASA,EACTvI,OAAQA,EACR+oB,uBAAwBA,IAGtBxnB,GACFiI,EAAgBC,EAAUe,WAAWhE,GAAIjF,EAX1C,CAaF,EAoEG4pB,GAAsB,SAAU95B,GAClC,IAAIwsB,EAAOxsB,EAAKwsB,KACZrU,EAAkBnY,EAAKmY,gBACvBme,EAAet2B,EAAKs2B,aASpByD,EAA+B,SAAsC3hB,EAAWlI,GAClF,IAAKupB,GAAmBrhB,EAAWlI,GACjC,OAAOA,EAGT,IAAIkpB,EA7LkB,SAA6BhhB,EAAWlI,GAChE,IAAI8G,EAAQoB,EAAUpB,MAEtB,OAAKA,GAIAyiB,GAAmBrhB,EAAWlI,GAI5B+oB,GAAW,CAChBp0B,QAASmS,EAAM1G,OAAOzL,QACtBsS,IAAKH,EAAM1G,OAAO6G,IAClBjH,OAAQA,IAVD,IAYV,CA6KiB8pB,CAAoB5hB,EAAWlI,GAE7C,IAAKkpB,EAEH,OADAjhB,EAAgBC,EAAUe,WAAWhE,GAAIjF,GAClC,KAGT,IAAI+pB,EAA4BjkB,GAAS9F,EAAQkpB,GAGjD,OAFAjhB,EAAgBC,EAAUe,WAAWhE,GAAI8kB,GACzBjkB,GAAS9F,EAAQ+pB,EAElC,EAEGC,EAA4B,SAAmC9P,EAAuBnO,EAAU/L,GAClG,IAAKka,EACH,OAAOla,EAGT,IAAKspB,GAAgBvd,EAAU/L,GAC7B,OAAOA,EAGT,IAAIkpB,EA7Oe,SAA0Bnd,EAAU/L,GACzD,IAAKspB,GAAgBvd,EAAU/L,GAC7B,OAAO,KAGT,IAAIiH,EAAM8E,EAAS3L,OAAO6G,IACtBtS,EAAUoX,EAAS3L,OAAOzL,QAC9B,OAAOo0B,GAAW,CAChBp0B,QAASA,EACTsS,IAAKA,EACLjH,OAAQA,GAEX,CAiOiBiqB,CAAiBle,EAAU/L,GAEzC,IAAKkpB,EAEH,OADA9C,EAAapmB,GACN,KAGT,IAAIkqB,EAAyBpkB,GAAS9F,EAAQkpB,GAG9C,OAFA9C,EAAa8D,GACGpkB,GAAS9F,EAAQkqB,EAElC,EA2BD,OAzBmB,SAAsBr2B,GACvC,IAAI4vB,EAAU5vB,EAAM2d,kBAEpB,GAAKiS,EAAL,CAIA,IAAI9e,EAAc6P,GAAkB3gB,EAAM8V,QACzChF,GAAsI9E,IAAU,GACjJ,IAAIsqB,EAAqBN,EAA6Bh2B,EAAM+gB,WAAW7L,WAAWpE,GAAc8e,GAEhG,GAAK0G,EAAL,CAIA,IAAIpe,EAAWlY,EAAMkY,SACjBqe,EAAkBJ,EAA0Bn2B,EAAMqmB,sBAAuBnO,EAAUoe,GAElFC,GAjEY,SAAsBv2B,EAAOiM,GAC9C,IAAIwQ,EAAS3K,GAAI9R,EAAMc,QAAQ2b,OAAO6E,UAAWrV,GACjDwc,EAAK,CACHhM,OAAQA,GAEX,CAgEC+Z,CAAax2B,EAAOu2B,EATnB,CARA,CAkBF,CAGF,EAEGE,GAAsB,SAAUx6B,GAClC,IAAImY,EAAkBnY,EAAKmY,gBACvBme,EAAet2B,EAAKs2B,aACpB9J,EAAOxsB,EAAKwsB,KACZiO,EAtJqB,SAAUz6B,GACnC,IAAIs2B,EAAet2B,EAAKs2B,aACpBne,EAAkBnY,EAAKmY,gBACvBuiB,EAAuB1L,GAAQsH,GAC/BqE,EAA0B3L,GAAQ7W,GAClC6Y,EAAW,KAEX4J,EAAY,SAAmB72B,GAChCitB,GAA6GjhB,IAAU,GACxH,IAAI8qB,EAAY7J,EACZ0G,EAAyBmD,EAAUnD,uBACnCD,EAAgBoD,EAAUpD,cAC9BiC,GAAS,CACP31B,MAAOA,EACPuyB,aAAcoE,EACdviB,gBAAiBwiB,EACjBlD,cAAeA,EACfC,uBAAwBA,GAE3B,EAwCD,MAAO,CACLliB,MAvCY,SAAiBzR,GAC7ByR,KACEwb,GAA0HjhB,IAAU,GACtI,IAAI0nB,EAAgBS,KAAK5Q,MACrBwT,GAAkB,EAElBC,EAAqB,WACvBD,GAAkB,CACnB,EAEDpB,GAAS,CACP31B,MAAOA,EACP0zB,cAAe,EACfC,wBAAwB,EACxBpB,aAAcyE,EACd5iB,gBAAiB4iB,IAEnB/J,EAAW,CACTyG,cAAeA,EACfC,uBAAwBoD,GAE1B/R,KAEI+R,GACFF,EAAU72B,EAEb,EAcCsrB,KAZS,WACJ2B,IAIL0J,EAAqB1oB,SACrB2oB,EAAwB3oB,SACxBgf,EAAW,KACZ,EAKC1gB,OAAQsqB,EAEX,CAsFqBI,CAAoB,CACtC1E,aAAcA,EACdne,gBAAiBA,IAEf8iB,EAAanB,GAAmB,CAClCtN,KAAMA,EACN8J,aAAcA,EACdne,gBAAiBA,IAyBnB,MALe,CACb7H,OAlBW,SAAgBvM,GACP,aAAhBA,EAAMwhB,QAIiB,UAAvBxhB,EAAMykB,aAKLzkB,EAAM2d,mBAIXuZ,EAAWl3B,GART02B,EAAcnqB,OAAOvM,GASxB,EAICyR,MAAOilB,EAAcjlB,MACrB6Z,KAAMoL,EAAcpL,KAGvB,EAGG6L,GAAa,WACf,IAAIC,EAAOC,uBACX,MAAO,CACLD,KAAMA,EACN9lB,YAAa8lB,EAAO,gBACpBE,UAAWF,EAAO,cAErB,CAPgB,GAQbzhB,GAAY,WACd,IAAIyhB,EAAOC,qBACX,MAAO,CACLD,KAAMA,EACNE,UAAWF,EAAO,cAClBhmB,GAAIgmB,EAAO,MAEd,CAPe,GAQZ/iB,GAAY,WACd,IAAI+iB,EAAOC,qBACX,MAAO,CACLD,KAAMA,EACNE,UAAWF,EAAO,cAClBhmB,GAAIgmB,EAAO,MAEd,CAPe,GAQZG,GAAkB,CACpBD,UAAWD,wCASTG,GAAY,SAAmBC,EAAOC,GACxC,OAAOD,EAAM/oB,KAAI,SAAUipB,GACzB,IAAI9/B,EAAQ8/B,EAAKhrB,OAAO+qB,GAExB,OAAK7/B,EAIE8/B,EAAKC,SAAW,MAAQ//B,EAAQ,KAH9B,EAIV,IAAE8B,KAAK,IACT,EAqDGmF,GAA8C,qBAAXC,QAAqD,qBAApBA,OAAOC,UAAqE,qBAAlCD,OAAOC,SAASC,cAAgCC,EAAAA,gBAAkBC,EAAAA,UAEhL04B,GAAU,WACZ,IAAIC,EAAO94B,SAAS+4B,cAAc,QAElC,OADCD,GAA+G9rB,IAAU,GACnH8rB,CACR,EAEGE,GAAgB,SAAuBC,GACzC,IAAIxqB,EAAKzO,SAASC,cAAc,SAOhC,OALIg5B,GACFxqB,EAAGyqB,aAAa,QAASD,GAG3BxqB,EAAG5R,KAAO,WACH4R,CACR,EAED,SAAS0qB,GAAgBb,EAAWW,GAClC,IAAItrB,EAASpN,IAAQ,WACnB,OAvEe,SAAU+3B,GAC3B,IApB6Cl4B,EAoBzCg5B,GApByCh5B,EAoBXk4B,EAnB3B,SAAUe,GACf,MAAO,IAAMA,EAAY,KAAQj5B,EAAU,IAC5C,GAmBGk5B,EAAe,WACjB,IAAIC,EAAa,2DACjB,MAAO,CACLX,SAAUQ,EAAYjB,GAAWG,WACjC3qB,OAAQ,CACN6rB,OAAQ,mJACR/I,QAAS8I,EACTtL,SAXc,wBAYdwL,cAAeF,GAGpB,CAXkB,GAqCfd,EAAQ,CAxBM,WAChB,IAAIiB,EAAa,uBAAyBtP,GAAYK,YAAc,UACpE,MAAO,CACLmO,SAAUQ,EAAYziB,GAAU2hB,WAChC3qB,OAAQ,CACNsgB,SAAUyL,EACVD,cAAeC,EACfC,WAAYD,GAGjB,CAViB,GAwBQJ,EAZR,CAChBV,SAAUQ,EAAY/jB,GAAUijB,WAChC3qB,OAAQ,CACN6rB,OAAQ,2BAGD,CACTZ,SAAU,OACVjrB,OAAQ,CACNsgB,SAAU,6OAId,MAAO,CACLuL,OAAQhB,GAAUC,EAAO,UACzBhI,QAAS+H,GAAUC,EAAO,WAC1BxK,SAAUuK,GAAUC,EAAO,YAC3BgB,cAAejB,GAAUC,EAAO,iBAChCkB,WAAYnB,GAAUC,EAAO,cAEhC,CAuBUmB,CAAYtB,EACpB,GAAE,CAACA,IACAuB,GAAYx0B,EAAAA,EAAAA,QAAO,MACnBy0B,GAAaz0B,EAAAA,EAAAA,QAAO,MACpB00B,EAAkB3uB,IAAY6K,EAAAA,GAAAA,IAAW,SAAUqL,GACrD,IAAI7S,EAAKqrB,EAAWh4B,QACnB2M,GAAqHzB,IAAU,GAChIyB,EAAGurB,YAAc1Y,CAClB,IAAG,IACA2Y,EAAiB7uB,IAAY,SAAUkW,GACzC,IAAI7S,EAAKorB,EAAU/3B,QAClB2M,GAAqHzB,IAAU,GAChIyB,EAAGurB,YAAc1Y,CAClB,GAAE,IACHxhB,IAA0B,YACrB+5B,EAAU/3B,SAAYg4B,EAAWh4B,UAAwGkL,IAAU,GACtJ,IAAIwsB,EAASR,GAAcC,GACvBiB,EAAUlB,GAAcC,GAS5B,OARAY,EAAU/3B,QAAU03B,EACpBM,EAAWh4B,QAAUo4B,EACrBV,EAAON,aAAab,kBAAsBC,GAC1C4B,EAAQhB,aAAab,mBAAuBC,GAC5CO,KAAUsB,YAAYX,GACtBX,KAAUsB,YAAYD,GACtBD,EAAetsB,EAAO6rB,QACtBO,EAAgBpsB,EAAO8iB,SAChB,WACL,IAAIvZ,EAAS,SAAgB3R,GAC3B,IAAIzD,EAAUyD,EAAIzD,QACjBA,GAA4GkL,IAAU,GACvH6rB,KAAUuB,YAAYt4B,GACtByD,EAAIzD,QAAU,IACf,EAEDoV,EAAO2iB,GACP3iB,EAAO4iB,EACR,CACF,GAAE,CAACb,EAAOgB,EAAgBF,EAAiBpsB,EAAO6rB,OAAQ7rB,EAAO8iB,QAAS6H,IAC3E,IAAIrK,EAAW7iB,IAAY,WACzB,OAAO2uB,EAAgBpsB,EAAOsgB,SAC/B,GAAE,CAAC8L,EAAiBpsB,EAAOsgB,WACxBuC,EAAWplB,IAAY,SAAUwH,GAMnCmnB,EALe,SAAXnnB,EAKYjF,EAAOgsB,WAJLhsB,EAAO8rB,cAK1B,GAAE,CAACM,EAAiBpsB,EAAO8rB,cAAe9rB,EAAOgsB,aAC9ClJ,EAAUrlB,IAAY,WACnB0uB,EAAWh4B,SAIhBi4B,EAAgBpsB,EAAO8iB,QACxB,GAAE,CAACsJ,EAAiBpsB,EAAO8iB,UAQ5B,OAPclwB,IAAQ,WACpB,MAAO,CACL0tB,SAAUA,EACVuC,SAAUA,EACVC,QAASA,EAEZ,GAAE,CAACxC,EAAUuC,EAAUC,GAEzB,CAED,IAAI4J,GAAmB,SAAU5rB,GAC/B,OAAOA,GAAMA,EAAG6rB,cAAgB7rB,EAAG6rB,cAAcC,YAAcx6B,MAChE,EAED,SAASy6B,GAAc/rB,GACrB,OAAOA,aAAc4rB,GAAgB5rB,GAAIgsB,WAC1C,CAED,SAASC,GAAepC,EAAWhmB,GACjC,IAAIsmB,EAAW,IAAMT,GAAWG,UAAY,KAAQA,EAAY,KAC5DqC,EAAW5kB,GAAQ/V,SAAS46B,iBAAiBhC,IAEjD,IAAK+B,EAAS1hC,OAEZ,OAAO,KAGT,IAAI4hC,EAAS/kB,GAAK6kB,GAAU,SAAUlsB,GACpC,OAAOA,EAAGqsB,aAAa3C,GAAW7lB,eAAiBA,CACpD,IAED,OAAKuoB,GAKAL,GAAcK,GAKZA,EARE,IASV,CAwFD,SAASE,KACP,IAAIrO,EAAU,CACZpW,WAAY,CAAC,EACbJ,WAAY,CAAC,GAEX8kB,EAAc,GAelB,SAASp8B,EAAOiS,GACVmqB,EAAY/hC,QACd+hC,EAAY9qB,SAAQ,SAAU+qB,GAC5B,OAAOA,EAAGpqB,EACX,GAEJ,CAED,SAASqqB,EAAkB9oB,GACzB,OAAOsa,EAAQpW,WAAWlE,IAAO,IAClC,CA4DD,SAAS+oB,EAAkB/oB,GACzB,OAAOsa,EAAQxW,WAAW9D,IAAO,IAClC,CA2CD,MAAO,CACLuE,UAlGiB,CACjBykB,SAAU,SAAkBtO,GAC1BJ,EAAQpW,WAAWwW,EAAM1W,WAAWhE,IAAM0a,EAC1CluB,EAAO,CACL/B,KAAM,WACNhE,MAAOi0B,GAEV,EACDpa,OAAQ,SAAgBoa,EAAOxtB,GAC7B,IAAIwC,EAAU4qB,EAAQpW,WAAWhX,EAAK8W,WAAWhE,IAE5CtQ,GAIDA,EAAQu5B,WAAavO,EAAMuO,kBAIxB3O,EAAQpW,WAAWhX,EAAK8W,WAAWhE,IAC1Csa,EAAQpW,WAAWwW,EAAM1W,WAAWhE,IAAM0a,EAC3C,EACDwO,WAAY,SAAoBxO,GAC9B,IAAIxa,EAAcwa,EAAM1W,WAAWhE,GAC/BtQ,EAAUo5B,EAAkB5oB,GAE3BxQ,GAIDgrB,EAAMuO,WAAav5B,EAAQu5B,kBAIxB3O,EAAQpW,WAAWhE,GAC1B1T,EAAO,CACL/B,KAAM,UACNhE,MAAOi0B,IAEV,EACD4F,QA9CF,SAA0BtgB,GACxB,IAAI0a,EAAQoO,EAAkB9oB,GAE9B,OADC0a,GAAuH9f,IAAU,GAC3H8f,CACR,EA2CCyO,SAAUL,EACV9H,OAAQ,SAAgBhhB,GACtB,OAAOzS,QAAQu7B,EAAkB9oB,GAClC,EACDkgB,aAAc,SAAsBz1B,GAClC,OAAO6Y,GAAOgX,EAAQpW,YAAYI,QAAO,SAAUoW,GACjD,OAAOA,EAAM1W,WAAWvZ,OAASA,CAClC,GACF,GAkDDwY,UArCiB,CACjB+lB,SAAU,SAAkBtO,GAC1BJ,EAAQxW,WAAW4W,EAAM1W,WAAWhE,IAAM0a,CAC3C,EACDwO,WAAY,SAAoBxO,GAC9B,IAAIhrB,EAAUq5B,EAAkBrO,EAAM1W,WAAWhE,IAE5CtQ,GAIDgrB,EAAMuO,WAAav5B,EAAQu5B,iBAIxB3O,EAAQxW,WAAW4W,EAAM1W,WAAWhE,GAC5C,EACDsgB,QAvBF,SAA0BtgB,GACxB,IAAI0a,EAAQqO,EAAkB/oB,GAE9B,OADC0a,GAAuH9f,IAAU,GAC3H8f,CACR,EAoBCyO,SAAUJ,EACV/H,OAAQ,SAAgBhhB,GACtB,OAAOzS,QAAQw7B,EAAkB/oB,GAClC,EACDkgB,aAAc,SAAsBz1B,GAClC,OAAO6Y,GAAOgX,EAAQxW,YAAYQ,QAAO,SAAUoW,GACjD,OAAOA,EAAM1W,WAAWvZ,OAASA,CAClC,GACF,GAYDT,UAnIF,SAAmB6+B,GAEjB,OADAD,EAAYz+B,KAAK0+B,GACV,WACL,IAAIz+B,EAAQw+B,EAAYv+B,QAAQw+B,IAEjB,IAAXz+B,GAIJw+B,EAAYt+B,OAAOF,EAAO,EAC3B,CACF,EAyHCg/B,MAVF,WACE9O,EAAQpW,WAAa,CAAC,EACtBoW,EAAQxW,WAAa,CAAC,EACtB8kB,EAAY/hC,OAAS,CACtB,EAQF,CAYD,IAAIwiC,GAAel9B,EAAAA,cAAoB,MAEnCm9B,GAAkB,WACpB,IAAIC,EAAO37B,SAAS27B,KAEpB,OADCA,GAA+F3uB,IAAU,GACnG2uB,CACR,EAEGC,GAAiB,CACnBjqB,SAAU,WACVjG,MAAO,MACPC,OAAQ,MACRW,OAAQ,OACRE,OAAQ,IACRE,QAAS,IACTmvB,SAAU,SACV7nB,KAAM,gBACN,YAAa,eAgDf,IAAI8nB,GAAQ,EACRC,GAAW,CACbC,UAAW,MAKb,SAASC,GAAY7rB,EAAQ3J,GAK3B,YAJgB,IAAZA,IACFA,EAAUs1B,IAGLx7B,IAAQ,WACb,MAAO,GAAK6P,EAAS3J,EAAQu1B,UAAYF,IAC1C,GAAE,CAACr1B,EAAQu1B,UAAW5rB,GACxB,CAoCD,IAAI8rB,GAAa39B,EAAAA,cAAoB,MAwErC,SAAS49B,GAAOC,GACVrW,CAGL,CAED,SAASsW,GAAmBztB,EAAI9D,GAC9BqxB,IASD,CAED,SAASG,KACPD,IAID,CAED,SAASE,GAAYz6B,GACnB,IAAIyD,GAAMF,EAAAA,EAAAA,QAAOvD,GAIjB,OAHA3B,EAAAA,EAAAA,YAAU,WACRoF,EAAIzD,QAAUA,CACf,IACMyD,CACR,CA2CD,IAaIi3B,GACAC,KAAiBD,GAAiB,CAAC,GAb3B,KAasD,EAAMA,GAd9D,IAcoF,EAAMA,IAChGE,GAA4B,SAAU7rB,GACpC4rB,GAAc5rB,EAAM8rB,UACtB9rB,EAAMI,gBAET,EAEG2rB,GAAqB,WACvB,IAAIxE,EAAO,mBAEX,MAAwB,qBAAbp4B,SACFo4B,EAIOtiB,GADC,CAACsiB,EAAM,KAAOA,EAAM,SAAWA,EAAM,MAAQA,EAAM,IAAMA,IACzC,SAAUpoB,GACzC,MAAO,KAAOA,KAAahQ,QAC5B,KACmBo4B,CACrB,CAZwB,GAqBzB,IAuQIyE,GAvQAC,GAAS,CACXjgC,KAAM,QAGR,SAASkgC,GAAmB9/B,GAC1B,IAAIgS,EAAShS,EAAKgS,OACdiY,EAAYjqB,EAAKiqB,UACjB8V,EAAW//B,EAAK+/B,SAChBC,EAAWhgC,EAAKggC,SACpB,MAAO,CAAC,CACNjtB,UAAW,YACXpB,GAAI,SAAYiC,GACd,IAAIqsB,EAASrsB,EAAMqsB,OACfC,EAAUtsB,EAAMssB,QAChBC,EAAUvsB,EAAMusB,QAEpB,GAvBc,IAuBVF,EAAJ,CAIA,IAAI/pB,EAAQ,CACVvN,EAAGu3B,EACHt3B,EAAGu3B,GAED5a,EAAQwa,IAEZ,GAAmB,aAAfxa,EAAM3lB,KAGR,OAFAgU,EAAMI,sBACNuR,EAAM6a,QAAQ5T,KAAKtW,GAIJ,YAAfqP,EAAM3lB,MAAmGmQ,IAAU,GACrH,IAAIswB,EAAU9a,EAAMrP,MAEpB,GAvCkCjG,EAuCEowB,EAvCQx7B,EAuCCqR,EAtC1C7Y,KAAKijC,IAAIz7B,EAAQ8D,EAAIsH,EAAStH,IAHZ,GAG0CtL,KAAKijC,IAAIz7B,EAAQ+D,EAAIqH,EAASrH,IAHxE,EAyCrB,CAvCN,IAAwCqH,EAAUpL,EA2C5C+O,EAAMI,iBACN,IAAIosB,EAAU7a,EAAM6a,QAAQG,UAAUrqB,GACtC8pB,EAAS,CACPpgC,KAAM,WACNwgC,QAASA,GANV,CAnBA,CA2BF,GACA,CACDrtB,UAAW,UACXpB,GAAI,SAAYiC,GACd,IAAI2R,EAAQwa,IAEO,aAAfxa,EAAM3lB,MAKVgU,EAAMI,iBACNuR,EAAM6a,QAAQtT,KAAK,CACjB0T,sBAAsB,IAExBvW,KAREjY,GASH,GACA,CACDe,UAAW,YACXpB,GAAI,SAAYiC,GACU,aAApBmsB,IAAWngC,MACbgU,EAAMI,iBAGRhC,GACD,GACA,CACDe,UAAW,UACXpB,GAAI,SAAYiC,GAGd,GAAmB,YAFPmsB,IAEFngC,KAKV,OAzHO,KAyHHgU,EAAM8rB,SACR9rB,EAAMI,sBACNhC,UAIFytB,GAAyB7rB,GAVvB5B,GAWH,GACA,CACDe,UAAW,SACXpB,GAAIK,GACH,CACDe,UAAW,SACXvJ,QAAS,CACPylB,SAAS,EACTC,SAAS,GAEXvd,GAAI,WACsB,YAApBouB,IAAWngC,MACboS,GAEH,GACA,CACDe,UAAW,uBACXpB,GAAI,SAAYiC,GACd,IAAI2R,EAAQwa,IACK,SAAfxa,EAAM3lB,MAAkGmQ,IAAU,GAEhHwV,EAAM6a,QAAQK,0BAChBzuB,IAIF4B,EAAMI,gBACP,GACA,CACDjB,UAAW4sB,GACXhuB,GAAIK,GAEP,CAiJD,SAAS0uB,KAAW,CAEpB,IAAIC,KAAkBf,GAAkB,CAAC,GAhT1B,KAgTyD,EAAMA,GAjTjE,KAiT2F,EAAMA,GA9SnG,KA8S2H,EAAMA,GA/SlI,KA+SyJ,EAAMA,IAEzK,SAASgB,GAAoBR,EAAS/Q,GACpC,SAASrd,IACPqd,IACA+Q,EAAQpuB,QACT,CAOD,MAAO,CAAC,CACNe,UAAW,UACXpB,GAAI,SAAYiC,GACd,OAnUO,KAmUHA,EAAM8rB,SACR9rB,EAAMI,sBACNhC,KApUI,KAwUF4B,EAAM8rB,SACR9rB,EAAMI,iBAdVqb,SACA+Q,EAAQtT,QApTI,KAsUNlZ,EAAM8rB,SACR9rB,EAAMI,sBACNosB,EAAQ1T,YA1UF,KA8UJ9Y,EAAM8rB,SACR9rB,EAAMI,sBACNosB,EAAQ3T,UA/UC,KAmVP7Y,EAAM8rB,SACR9rB,EAAMI,sBACNosB,EAAQzT,aAvVA,KA2VN/Y,EAAM8rB,SACR9rB,EAAMI,sBACNosB,EAAQxT,iBAIN+T,GAAe/sB,EAAM8rB,SACvB9rB,EAAMI,iBAIRyrB,GAAyB7rB,GAC1B,GACA,CACDb,UAAW,YACXpB,GAAIK,GACH,CACDe,UAAW,UACXpB,GAAIK,GACH,CACDe,UAAW,QACXpB,GAAIK,GACH,CACDe,UAAW,aACXpB,GAAIK,GACH,CACDe,UAAW,SACXpB,GAAIK,GACH,CACDe,UAAW,QACXpB,GAAIK,EACJxI,QAAS,CACPylB,SAAS,IAEV,CACDlc,UAAW4sB,GACXhuB,GAAIK,GAEP,CAgED,IAAI6uB,GAAS,CACXjhC,KAAM,QAmSR,IAAIkhC,GAAsB,CACxBC,OAAO,EACPd,QAAQ,EACRe,UAAU,EACVC,QAAQ,EACRC,QAAQ,EACRC,UAAU,EACVC,OAAO,EACPC,OAAO,GAGT,SAASC,GAAuBC,EAAQ18B,GACtC,GAAe,MAAXA,EACF,OAAO,EAKT,GAF0BnC,QAAQo+B,GAAoBj8B,EAAQ28B,QAAQC,gBAGpE,OAAO,EAGT,IAAIrF,EAAYv3B,EAAQg5B,aAAa,mBAErC,MAAkB,SAAdzB,GAAsC,KAAdA,GAIxBv3B,IAAY08B,GAITD,GAAuBC,EAAQ18B,EAAQ68B,cAC/C,CAED,SAASC,GAA4BjoB,EAAW9F,GAC9C,IAAI/E,EAAS+E,EAAM/E,OAEnB,QAAK0uB,GAAc1uB,IAIZyyB,GAAuB5nB,EAAW7K,EAC1C,CAED,IAAI+yB,GAA8B,SAAUpwB,GAC1C,OAAOpD,GAAQoD,EAAGC,yBAAyB9C,MAC5C,EAMD,IAAIkzB,GAAuB,WACzB,IAAI1G,EAAO,UAEX,MAAwB,qBAAbp4B,SACFo4B,EAIGtiB,GADK,CAACsiB,EAAM,oBAAqB,0BAChB,SAAUr1B,GACrC,OAAOA,KAAQg8B,QAAQ34B,SACxB,KACegyB,CACjB,CAZ0B,GAc3B,SAAS4G,GAAgBvwB,EAAImqB,GAC3B,OAAU,MAANnqB,EACK,KAGLA,EAAGqwB,IAAsBlG,GACpBnqB,EAGFuwB,GAAgBvwB,EAAGkwB,cAAe/F,EAC1C,CAED,SAASqG,GAAUxwB,EAAImqB,GACrB,OAAInqB,EAAGiF,QACEjF,EAAGiF,QAAQklB,GAGboG,GAAgBvwB,EAAImqB,EAC5B,CAMD,SAASsG,GAA+B5G,EAAWznB,GACjD,IA3CiBpC,EA2Cb3C,EAAS+E,EAAM/E,OAEnB,MA7CiB2C,EA6CF3C,aA5CMuuB,GAAgB5rB,GAAIswB,SA8CvC,OAAO,KAGT,IAAInG,EAZN,SAAqBN,GACnB,MAAO,IAAMH,GAAWG,UAAY,KAAQA,EAAY,IACzD,CAUgBc,CAAYd,GACvBuC,EAASoE,GAAUnzB,EAAQ8sB,GAE/B,OAAKiC,GAIAL,GAAcK,GAKZA,EARE,IASV,CA+BD,SAAS5pB,GAAeJ,GACtBA,EAAMI,gBACP,CAED,SAASkuB,GAAUliC,GACjB,IAAImiC,EAAWniC,EAAKmiC,SAChB5c,EAAQvlB,EAAKulB,MACb6c,EAAepiC,EAAKoiC,aACPpiC,EAAKqiC,WAEtB,QAAKD,KAQDD,IAAa5c,CASlB,CAED,SAAS+c,GAAS9jC,GAChB,IAAI+jC,EAAU/jC,EAAM+jC,QAChBzgC,EAAQtD,EAAMsD,MACdozB,EAAW12B,EAAM02B,SACjB7f,EAAc7W,EAAM6W,YAExB,GAAIktB,EAAQC,YACV,OAAO,EAGT,IAAI3S,EAAQqF,EAASxb,UAAU4kB,SAASjpB,GAExC,QAAKwa,MAKAA,EAAMrmB,QAAQ0X,aAIdmV,GAAav0B,EAAM5C,WAAYmW,GAKrC,CAED,SAASotB,GAASt1B,GAChB,IAAIo1B,EAAUp1B,EAAMo1B,QAChBlH,EAAYluB,EAAMkuB,UAClBv5B,EAAQqL,EAAMrL,MACdozB,EAAW/nB,EAAM+nB,SACjB7f,EAAclI,EAAMkI,YACpBqtB,EAAkBv1B,EAAMu1B,gBACxBC,EAAcx1B,EAAMw1B,YAQxB,IAPkBL,GAAS,CACzBC,QAASA,EACTzgC,MAAOA,EACPozB,SAAUA,EACV7f,YAAaA,IAIb,OAAO,KAGT,IAAIwa,EAAQqF,EAASxb,UAAU+b,QAAQpgB,GACnC7D,EAhGN,SAAuB6pB,EAAWhmB,GAChC,IAAIsmB,EAAW,IAAMjiB,GAAU2hB,UAAY,KAAQA,EAAY,KAE3DuH,EAAc/pB,GADHC,GAAQ/V,SAAS46B,iBAAiBhC,KAChB,SAAUnqB,GACzC,OAAOA,EAAGqsB,aAAankB,GAAUvE,MAAQE,CAC1C,IAED,OAAKutB,GAIArF,GAAcqF,GAKZA,EARE,IASV,CA+EUC,CAAcxH,EAAWxL,EAAM1W,WAAWhE,IAEnD,IAAK3D,EAEH,OAAO,KAGT,GAAImxB,IAAgB9S,EAAMrmB,QAAQs5B,4BAA8BnB,GAA4BnwB,EAAImxB,GAC9F,OAAO,KAGT,IAAII,EAAOR,EAAQS,MAAMN,GAAmBtwB,IACxCmT,EAAQ,WAEZ,SAAS0d,IACP,OAAOpT,EAAMrmB,QAAQi3B,uBACtB,CAED,SAAS2B,IACP,OAAOG,EAAQnT,SAAS2T,EACzB,CAaD,IAAIG,EAXJ,SAAqBf,EAAUgB,GACzBjB,GAAU,CACZC,SAAUA,EACV5c,MAAOA,EACP6c,aAAcA,EACdC,YAAY,KAEZvgC,EAAMpC,SAASyjC,IAElB,EAEyCp7B,KAAK,KAAM,YAErD,SAASisB,EAAOliB,GACd,SAASmY,IACPsY,EAAQa,UACR7d,EAAQ,WACT,CAUD,SAASwD,EAAOpT,EAAQnM,GAStB,QARgB,IAAZA,IACFA,EAAU,CACRg3B,sBAAsB,IAI1B1uB,EAAKuxB,UAED75B,EAAQg3B,qBAAsB,CAChC,IAAIttB,EAASb,GAAWvP,OAAQ,CAAC,CAC/BiQ,UAAW,QACXpB,GAAIqC,GACJxK,QAAS,CACP8oB,MAAM,EACNrD,SAAS,EACTC,SAAS,MAGbS,WAAWzc,EACZ,CAED+W,IACAnoB,EAAMpC,SAASotB,GAAK,CAClBnX,OAAQA,IAEX,CAED,MApCc,aAAV4P,IACF0E,IACY,aAAV1E,GAAoHxV,IAAU,IAGlIjO,EAAMpC,SAlrHC,SAAcoS,GACvB,MAAO,CACLlS,KAAM,OACNqE,QAAS6N,EAEZ,CA6qHkBwxB,CAAKxxB,EAAKyxB,iBACzBhe,EAAQ,YA8BDve,EAAAA,EAAAA,GAAS,CACdooB,SAAU,WACR,OAAO8S,GAAU,CACfC,SAAU,WACV5c,MAAOA,EACP6c,aAAcA,EACdC,YAAY,GAEf,EACD5B,wBAAyBwC,EACzBnW,KAAM,SAActjB,GAClB,OAAOuf,EAAO,OAAQvf,EACvB,EACDwI,OAAQ,SAAgBxI,GACtB,OAAOuf,EAAO,SAAUvf,EACzB,GACAsI,EAAKsuB,QACT,CAiFD,MAdc,CACZhR,SAAU,WACR,OAAO8S,GAAU,CACfC,SAAU,WACV5c,MAAOA,EACP6c,aAAcA,EACdC,YAAY,GAEf,EACD5B,wBAAyBwC,EACzB1C,UA3EF,SAAmB9e,GACjB,IAAI+hB,EAASxU,IAAQ,SAAUxO,GAC7B0iB,GAAwB,WACtB,OAAO1W,GAAK,CACVhM,OAAQA,GAEX,GACF,IACGijB,EAAMzP,EAAO,CACfuP,eAAgB,CACdpuB,GAAIE,EACJoM,gBAAiBA,EACjB+G,aAAc,SAEhB6a,QAAS,WACP,OAAOG,EAAOxxB,QACf,EACDouB,QAAS,CACP5T,KAAMgX,KAGV,OAAOx8B,EAAAA,EAAAA,GAAS,CAAC,EAAGy8B,EAAK,CACvBjX,KAAMgX,GAET,EAoDCE,SAlDF,WACE,IAAItD,EAAU,CACZ3T,OAAQ,WACN,OAAOyW,EAAwBzW,GAChC,EACDE,UAAW,WACT,OAAOuW,EAAwBvW,GAChC,EACDD,SAAU,WACR,OAAOwW,EAAwBxW,GAChC,EACDE,SAAU,WACR,OAAOsW,EAAwBtW,GAChC,GAEH,OAAOoH,EAAO,CACZuP,eAAgB,CACdpuB,GAAIE,EACJoM,gBAAiBmgB,GAA2BpwB,GAC5CgX,aAAc,QAEhB6a,QAASjxB,GACTguB,QAASA,GAEZ,EA2BClO,MAzBF,WACsBgQ,GAAU,CAC5BC,SAAU,WACV5c,MAAOA,EACP6c,aAAcA,EACdC,YAAY,KAIZE,EAAQa,SAEX,EAiBF,CAED,IAAIO,GAAiB,CAv9BrB,SAAwBF,GACtB,IAAIG,GAAWx7B,EAAAA,EAAAA,QAAOy3B,IAClBgE,GAAkBz7B,EAAAA,EAAAA,QAAOgK,IACzB0xB,EAAsBxgC,IAAQ,WAChC,MAAO,CACLyP,UAAW,YACXpB,GAAI,SAAqBiC,GACvB,IAAIA,EAAMmwB,kBAxIE,IA4IRnwB,EAAMqsB,UAINrsB,EAAMowB,SAAWpwB,EAAMqwB,SAAWrwB,EAAMswB,UAAYtwB,EAAMuwB,QAA9D,CAIA,IAAI9uB,EAAcouB,EAAIW,uBAAuBxwB,GAE7C,GAAKyB,EAAL,CAIA,IAAI+qB,EAAUqD,EAAIY,WAAWhvB,EAAaga,EAAM,CAC9CsT,YAAa/uB,IAGf,GAAKwsB,EAAL,CAIAxsB,EAAMI,iBACN,IAAIkC,EAAQ,CACVvN,EAAGiL,EAAMssB,QACTt3B,EAAGgL,EAAMusB,SAEX0D,EAAgBh/B,UAChBy/B,EAAiBlE,EAASlqB,EARzB,CARA,CANA,CAuBF,EAEJ,GAAE,CAACutB,IACAc,EAA2BjhC,IAAQ,WACrC,MAAO,CACLyP,UAAW,4BACXpB,GAAI,SAAYiC,GACd,IAAIA,EAAMmwB,iBAAV,CAIA,IAAI5uB,EAAKsuB,EAAIW,uBAAuBxwB,GAEpC,GAAKuB,EAAL,CAIA,IAAI3L,EAAUi6B,EAAIe,wBAAwBrvB,GAErC3L,IAIDA,EAAQi3B,yBAIPgD,EAAIgB,WAAWtvB,IAIpBvB,EAAMI,iBAhBL,CANA,CAuBF,EAEJ,GAAE,CAACyvB,IACAiB,EAAmBv2B,IAAY,WAKjC01B,EAAgBh/B,QAAUwN,GAAWvP,OAAQ,CAACyhC,EAA0BT,GAJ1D,CACZ7U,SAAS,EACTC,SAAS,GAGZ,GAAE,CAACqV,EAA0BT,IAC1BzU,EAAOlhB,IAAY,WAGA,SAFPy1B,EAAS/+B,QAEXjF,OAIZgkC,EAAS/+B,QAAUg7B,GACnBgE,EAAgBh/B,UAChB6/B,IACD,GAAE,CAACA,IACA1yB,EAAS7D,IAAY,WACvB,IAAIoX,EAAQqe,EAAS/+B,QACrBwqB,IAEmB,aAAf9J,EAAM3lB,MACR2lB,EAAM6a,QAAQpuB,OAAO,CACnBwuB,sBAAsB,IAIP,YAAfjb,EAAM3lB,MACR2lB,EAAM6a,QAAQlO,OAEjB,GAAE,CAAC7C,IACAsV,EAAsBx2B,IAAY,WACpC,IAIImE,EAAWwtB,GAAmB,CAChC9tB,OAAQA,EACRiY,UAAWoF,EACX0Q,SAAU,WACR,OAAO6D,EAAS/+B,OACjB,EACDm7B,SAAU,SAAkBza,GAC1Bqe,EAAS/+B,QAAU0gB,CACpB,IAEHse,EAAgBh/B,QAAUwN,GAAWvP,OAAQwP,EAd/B,CACZ4c,SAAS,EACTD,SAAS,GAaZ,GAAE,CAACjd,EAAQqd,IACRiV,EAAmBn2B,IAAY,SAA0BiyB,EAASlqB,GACxC,SAA1B0tB,EAAS/+B,QAAQjF,MAA4HmQ,IAAU,GACzJ6zB,EAAS/+B,QAAU,CACjBjF,KAAM,UACNsW,MAAOA,EACPkqB,QAASA,GAEXuE,GACD,GAAE,CAACA,IACJ9hC,IAA0B,WAExB,OADA6hC,IACO,WACLb,EAAgBh/B,SACjB,CACF,GAAE,CAAC6/B,GACL,EA4FD,SAA2BjB,GACzB,IAAII,GAAkBz7B,EAAAA,EAAAA,QAAOs4B,IACzBoD,EAAsBxgC,IAAQ,WAChC,MAAO,CACLyP,UAAW,UACXpB,GAAI,SAAmBiC,GACrB,IAAIA,EAAMmwB,kBA9YN,KAkZAnwB,EAAM8rB,QAAV,CAIA,IAAIrqB,EAAcouB,EAAIW,uBAAuBxwB,GAE7C,GAAKyB,EAAL,CAIA,IAAIuvB,EAAUnB,EAAIY,WAAWhvB,EAAaga,EAAM,CAC9CsT,YAAa/uB,IAGf,GAAKgxB,EAAL,CAIAhxB,EAAMI,iBACN,IAAI6wB,GAAc,EACdzE,EAAUwE,EAAQlB,WACtBG,EAAgBh/B,UAShBg/B,EAAgBh/B,QAAUwN,GAAWvP,OAAQ89B,GAAoBR,EAAS/Q,GAAO,CAC/EH,SAAS,EACTD,SAAS,GAhBV,CARA,CANA,CAqBD,SAASI,IACNwV,GAAqI90B,IAAU,GAChJ80B,GAAc,EACdhB,EAAgBh/B,UAChB6/B,GACD,CAMF,EAEJ,GAAE,CAACjB,IACAiB,EAAmBv2B,IAAY,WAKjC01B,EAAgBh/B,QAAUwN,GAAWvP,OAAQ,CAACghC,GAJhC,CACZ7U,SAAS,EACTC,SAAS,GAGZ,GAAE,CAAC4U,IACJjhC,IAA0B,WAExB,OADA6hC,IACO,WACLb,EAAgBh/B,SACjB,CACF,GAAE,CAAC6/B,GACL,EA8ID,SAAwBjB,GACtB,IAAIG,GAAWx7B,EAAAA,EAAAA,QAAOy4B,IAClBgD,GAAkBz7B,EAAAA,EAAAA,QAAOgK,IACzB2tB,EAAW5xB,IAAY,WACzB,OAAOy1B,EAAS/+B,OACjB,GAAE,IACCm7B,EAAW7xB,IAAY,SAAkBoX,GAC3Cqe,EAAS/+B,QAAU0gB,CACpB,GAAE,IACCue,EAAsBxgC,IAAQ,WAChC,MAAO,CACLyP,UAAW,aACXpB,GAAI,SAAsBiC,GACxB,IAAIA,EAAMmwB,iBAAV,CAIA,IAAI1uB,EAAcouB,EAAIW,uBAAuBxwB,GAE7C,GAAKyB,EAAL,CAIA,IAAI+qB,EAAUqD,EAAIY,WAAWhvB,EAAaga,EAAM,CAC9CsT,YAAa/uB,IAGf,GAAKwsB,EAAL,CAIA,IAAI0E,EAAQlxB,EAAMmxB,QAAQ,GAGtB7uB,EAAQ,CACVvN,EAHYm8B,EAAM5E,QAIlBt3B,EAHYk8B,EAAM3E,SAKpB0D,EAAgBh/B,UAChBy/B,EAAiBlE,EAASlqB,EAVzB,CARA,CANA,CAyBF,EAEJ,GAAE,CAACutB,IACAiB,EAAmBv2B,IAAY,WAKjC01B,EAAgBh/B,QAAUwN,GAAWvP,OAAQ,CAACghC,GAJhC,CACZ5U,SAAS,EACTD,SAAS,GAGZ,GAAE,CAAC6U,IACAzU,EAAOlhB,IAAY,WACrB,IAAItJ,EAAU++B,EAAS/+B,QAEF,SAAjBA,EAAQjF,OAIS,YAAjBiF,EAAQjF,MACVowB,aAAanrB,EAAQmgC,kBAGvBhF,EAASa,IACTgD,EAAgBh/B,UAChB6/B,IACD,GAAE,CAACA,EAAkB1E,IAClBhuB,EAAS7D,IAAY,WACvB,IAAIoX,EAAQqe,EAAS/+B,QACrBwqB,IAEmB,aAAf9J,EAAM3lB,MACR2lB,EAAM6a,QAAQpuB,OAAO,CACnBwuB,sBAAsB,IAIP,YAAfjb,EAAM3lB,MACR2lB,EAAM6a,QAAQlO,OAEjB,GAAE,CAAC7C,IACAsV,EAAsBx2B,IAAY,WACpC,IAAI3E,EAAU,CACZ0lB,SAAS,EACTD,SAAS,GAEPnd,EAAO,CACTE,OAAQA,EACRiY,UAAWoF,EACX0Q,SAAUA,GAERkF,EAAe5yB,GAAWvP,OA7LlC,SAA2BtE,GACzB,IAAIwT,EAASxT,EAAMwT,OACfiY,EAAYzrB,EAAMyrB,UAClB8V,EAAWvhC,EAAMuhC,SACrB,MAAO,CAAC,CACNhtB,UAAW,YACXvJ,QAAS,CACP0lB,SAAS,GAEXvd,GAAI,SAAYiC,GACd,IAAI2R,EAAQwa,IAEZ,GAAmB,aAAfxa,EAAM3lB,KAAV,CAKA2lB,EAAM2f,UAAW,EACjB,IAAIC,EAAkBvxB,EAAMmxB,QAAQ,GAGhC7uB,EAAQ,CACVvN,EAHYw8B,EAAgBjF,QAI5Bt3B,EAHYu8B,EAAgBhF,SAK9BvsB,EAAMI,iBACNuR,EAAM6a,QAAQ5T,KAAKtW,EAXlB,MAFClE,GAcH,GACA,CACDe,UAAW,WACXpB,GAAI,SAAYiC,GACd,IAAI2R,EAAQwa,IAEO,aAAfxa,EAAM3lB,MAKVgU,EAAMI,iBACNuR,EAAM6a,QAAQtT,KAAK,CACjB0T,sBAAsB,IAExBvW,KAREjY,GASH,GACA,CACDe,UAAW,cACXpB,GAAI,SAAYiC,GACU,aAApBmsB,IAAWngC,MAKfgU,EAAMI,iBACNhC,KALEA,GAMH,GACA,CACDe,UAAW,mBACXpB,GAAI,SAAYiC,GACd,IAAI2R,EAAQwa,IACK,SAAfxa,EAAM3lB,MAA8EmQ,IAAU,GAChG,IAAI+0B,EAAQlxB,EAAMmxB,QAAQ,GAE1B,GAAKD,GAIcA,EAAMM,OAtGL,IAwGpB,CAIA,IAAIC,EAAgB9f,EAAM6a,QAAQK,0BAElC,GAAmB,YAAflb,EAAM3lB,KAQV,OAAIylC,EACE9f,EAAM2f,cACRtxB,EAAMI,sBAIRhC,SAIF4B,EAAMI,iBAjBAqxB,GACFrzB,GANH,CAuBF,GACA,CACDe,UAAW4sB,GACXhuB,GAAIK,GAEP,CA2FyCszB,CAAkBxzB,GAAOtI,GAC3D+7B,EAAelzB,GAAWvP,OAhOlC,SAA2B9C,GACzB,IAAIgS,EAAShS,EAAKgS,OACd+tB,EAAW//B,EAAK+/B,SACpB,MAAO,CAAC,CACNhtB,UAAW,oBACXpB,GAAIK,GACH,CACDe,UAAW,SACXpB,GAAIK,GACH,CACDe,UAAW,cACXpB,GAAI,SAAYiC,GACdA,EAAMI,gBACP,GACA,CACDjB,UAAW,UACXpB,GAAI,SAAYiC,GACU,aAApBmsB,IAAWngC,MA9dR,KAmeHgU,EAAM8rB,SACR9rB,EAAMI,iBAGRhC,KAREA,GASH,GACA,CACDe,UAAW4sB,GACXhuB,GAAIK,GAEP,CAgMyCwzB,CAAkB1zB,GAAOtI,GAE/Dq6B,EAAgBh/B,QAAU,WACxBogC,IACAM,GACD,CACF,GAAE,CAACvzB,EAAQ+tB,EAAU1Q,IAClBoW,EAAgBt3B,IAAY,WAC9B,IAAIoX,EAAQwa,IACK,YAAfxa,EAAM3lB,MAAmImQ,IAAU,GACrJ,IAAIqwB,EAAU7a,EAAM6a,QAAQG,UAAUhb,EAAMrP,OAC5C8pB,EAAS,CACPpgC,KAAM,WACNwgC,QAASA,EACT8E,UAAU,GAEb,GAAE,CAACnF,EAAUC,IACVsE,EAAmBn2B,IAAY,SAA0BiyB,EAASlqB,GAC9C,SAApB6pB,IAAWngC,MAA4HmQ,IAAU,GACnJ,IAAIi1B,EAAmBrV,WAAW8V,EAtPf,KAuPnBzF,EAAS,CACPpgC,KAAM,UACNsW,MAAOA,EACPkqB,QAASA,EACT4E,iBAAkBA,IAEpBL,GACD,GAAE,CAACA,EAAqB5E,EAAUC,EAAUyF,IAC7C5iC,IAA0B,WAExB,OADA6hC,IACO,WACLb,EAAgBh/B,UAChB,IAAI0gB,EAAQwa,IAEO,YAAfxa,EAAM3lB,OACRowB,aAAazK,EAAMyf,kBACnBhF,EAASa,IAEZ,CACF,GAAE,CAACd,EAAU2E,EAAkB1E,IAChCn9B,IAA0B,WASxB,OARawP,GAAWvP,OAAQ,CAAC,CAC/BiQ,UAAW,YACXpB,GAAI,WAAgB,EACpBnI,QAAS,CACP0lB,SAAS,EACTD,SAAS,KAId,GAAE,GACJ,GA0ZD,SAASyW,GAAiBC,GACxB,IAAItK,EAAYsK,EAAMtK,UAClBv5B,EAAQ6jC,EAAM7jC,MACdozB,EAAWyQ,EAAMzQ,SACjB0Q,EAAgBD,EAAMC,cACtBC,EAAuBF,EAAME,qBAC7BC,EAAa,GAAGryB,OAAOoyB,EAAuBlC,GAAiB,GAAIiC,GAAiB,IACpFrD,GAAUx0B,EAAAA,EAAAA,WAAS,WACrB,OA7qCJ,WACE,IAAIg1B,EAAO,KAmBX,SAASK,IACNL,GAA+GhzB,IAAU,GAC1HgzB,EAAO,IACR,CASD,MAAO,CACLP,UA9BF,WACE,OAAO9/B,QAAQqgC,EAChB,EA6BC3T,SA3BF,SAAkBxzB,GAChB,OAAOA,IAAUmnC,CAClB,EA0BCC,MAxBF,SAAe+C,GACXhD,GAAgHhzB,IAAU,GAC5H,IAAIi2B,EAAU,CACZD,QAASA,GAGX,OADAhD,EAAOiD,EACAA,CACR,EAkBC5C,QAASA,EACT6C,WAZF,WACMlD,IACFA,EAAKgD,UACL3C,IAEH,EASF,CAsoCU8C,EACR,IAAE,GACCC,EAAiBh4B,IAAY,SAAwB+K,EAAUrU,GAC7DqU,EAASpF,aAAejP,EAAQiP,YAClCyuB,EAAQ0D,YAEX,GAAE,CAAC1D,IACJ1/B,IAA0B,WACxB,IAAIqW,EAAWpX,EAAM5C,WAMrB,OALkB4C,EAAM3C,WAAU,WAChC,IAAI0F,EAAU/C,EAAM5C,WACpBinC,EAAejtB,EAAUrU,GACzBqU,EAAWrU,CACZ,GAEF,GAAE,CAAC09B,EAASzgC,EAAOqkC,IACpBtjC,IAA0B,WACxB,OAAO0/B,EAAQ0D,UAChB,GAAE,CAAC1D,EAAQ0D,aACZ,IAAIxB,EAAat2B,IAAY,SAAUkH,GACrC,OAAOitB,GAAS,CACdC,QAASA,EACTrN,SAAUA,EACVpzB,MAAOA,EACPuT,YAAaA,GAEhB,GAAE,CAACktB,EAASrN,EAAUpzB,IACnBuiC,EAAal2B,IAAY,SAAUkH,EAAa+wB,EAAW58B,GAC7D,OAAOi5B,GAAS,CACdF,QAASA,EACTrN,SAAUA,EACVmG,UAAWA,EACXv5B,MAAOA,EACPuT,YAAaA,EACbqtB,gBAAiB0D,EACjBzD,YAAan5B,GAAWA,EAAQm5B,YAAcn5B,EAAQm5B,YAAc,MAEvE,GAAE,CAACtH,EAAWkH,EAASrN,EAAUpzB,IAC9BsiC,EAAyBj2B,IAAY,SAAUyF,GACjD,OA5UJ,SAA2CynB,EAAWznB,GACpD,IAAIgqB,EAASqE,GAA+B5G,EAAWznB,GAEvD,OAAKgqB,EAIEA,EAAOC,aAAa3C,GAAW7lB,aAH7B,IAIV,CAoUUgxB,CAAkChL,EAAWznB,EACrD,GAAE,CAACynB,IACAmJ,EAA0Br2B,IAAY,SAAUgH,GAClD,IAAI0a,EAAQqF,EAASxb,UAAU4kB,SAASnpB,GACxC,OAAO0a,EAAQA,EAAMrmB,QAAU,IAChC,GAAE,CAAC0rB,EAASxb,YACT4sB,EAAiBn4B,IAAY,WAC1Bo0B,EAAQC,cAIbD,EAAQ0D,aAEuB,SAA3BnkC,EAAM5C,WAAWqmB,OACnBzjB,EAAMpC,SA/xHH,CACLE,KAAM,QACNqE,QAAS,OA+xHV,GAAE,CAACs+B,EAASzgC,IACTykC,EAAgBp4B,GAAYo0B,EAAQC,UAAW,CAACD,IAChDkB,EAAMngC,IAAQ,WAChB,MAAO,CACLmhC,WAAYA,EACZJ,WAAYA,EACZD,uBAAwBA,EACxBI,wBAAyBA,EACzB8B,eAAgBA,EAChBC,cAAeA,EAElB,GAAE,CAAC9B,EAAYJ,EAAYD,EAAwBI,EAAyB8B,EAAgBC,IAje7FrH,KAoeA,IAAK,IAAIjjC,EAAI,EAAGA,EAAI6pC,EAAW9pC,OAAQC,IACrC6pC,EAAW7pC,GAAGwnC,EAEjB,CAYD,SAAS+C,GAASC,GAEhB,OADCA,EAAQ5hC,SAA2GkL,IAAU,GACvH02B,EAAQ5hC,OAChB,CAED,SAAS6hC,GAAIt/B,GACX,IAAIi0B,EAAYj0B,EAAMi0B,UAClBpnB,EAAe7M,EAAM6M,aACrB0yB,EAAUv/B,EAAMu/B,QAChB3K,EAAQ50B,EAAM40B,MACd4K,EAA8Bx/B,EAAMw/B,4BACpCC,GAAez+B,EAAAA,EAAAA,QAAO,MAC1Bi3B,KACA,IAAIyH,EAAexH,GAAYl4B,GAC3BypB,EAAgB1iB,IAAY,WAC9B,OAzBmB,SAA0B/G,GAC/C,MAAO,CACL+pB,gBAAiB/pB,EAAM+pB,gBACvBE,kBAAmBjqB,EAAMiqB,kBACzBI,YAAarqB,EAAMqqB,YACnBR,UAAW7pB,EAAM6pB,UACjBgB,aAAc7qB,EAAM6qB,aAEvB,CAiBU8U,CAAiBD,EAAajiC,QACtC,GAAE,CAACiiC,IACAzW,EAx9CN,SAAsBgL,GACpB,IAAIlmB,EAAK7R,IAAQ,WACf,OALQ,SAAe+3B,GACzB,MAAO,oBAAsBA,CAC9B,CAGU2L,CAAM3L,EACd,GAAE,CAACA,IACA/yB,GAAMF,EAAAA,EAAAA,QAAO,MAmCjB,OAlCAlF,EAAAA,EAAAA,YAAU,WACR,IAAIsO,EAAKzO,SAASC,cAAc,OAShC,OARAsF,EAAIzD,QAAU2M,EACdA,EAAG2D,GAAKA,EACR3D,EAAGyqB,aAAa,YAAa,aAC7BzqB,EAAGyqB,aAAa,cAAe,SAE/Bj1B,EAAAA,EAAAA,GAASwK,EAAGy1B,MAAOtI,IAEnBF,KAAiBvB,YAAY1rB,GACtB,WACLme,YAAW,WACT,IAAI+O,EAAOD,KAEPC,EAAK5a,SAAStS,IAChBktB,EAAKvB,YAAY3rB,GAGfA,IAAOlJ,EAAIzD,UACbyD,EAAIzD,QAAU,KAEjB,GACF,CACF,GAAE,CAACsQ,IACWhH,IAAY,SAAUgE,GACnC,IAAIX,EAAKlJ,EAAIzD,QAET2M,IACFA,EAAGurB,YAAc5qB,EAKpB,GAAE,GAEJ,CAg7CgB+0B,CAAa7L,GACxB8L,EAz5CN,SAA8B3oC,GAC5B,IAAI68B,EAAY78B,EAAM68B,UAClB+L,EAAO5oC,EAAM4oC,KACbhJ,EAAWY,GAAY,cAAe,CACxCD,UAAW,MAET5pB,EAAK7R,IAAQ,WACf,MATK,oBAHatD,EAYE,CAClBq7B,UAAWA,EACX+C,SAAUA,IAbO/C,UAEmB,IADzBr7B,EAAKo+B,SAFtB,IAAsBp+B,CAgBnB,GAAE,CAACo+B,EAAU/C,IAed,OAdAn4B,EAAAA,EAAAA,YAAU,WACR,IAAIsO,EAAKzO,SAASC,cAAc,OAKhC,OAJAwO,EAAG2D,GAAKA,EACR3D,EAAGurB,YAAcqK,EACjB51B,EAAGy1B,MAAMI,QAAU,OACnB5I,KAAiBvB,YAAY1rB,GACtB,WACL,IAAIktB,EAAOD,KAEPC,EAAK5a,SAAStS,IAChBktB,EAAKvB,YAAY3rB,EAEpB,CACF,GAAE,CAAC2D,EAAIiyB,IACDjyB,CACR,CA83CqCmyB,CAAqB,CACvDjM,UAAWA,EACX+L,KAAMR,IAEJ/T,EAAeqJ,GAAgBb,EAAWW,GAC1CuL,EAAep5B,IAAY,SAAUxO,GACvC6mC,GAASK,GAAcnnC,SAASC,EACjC,GAAE,IACC6nC,EAAmBlkC,IAAQ,WAC7B,OAAO9C,EAAmB,CACxB2rB,qBAAsBA,GACtBE,sBAAuBA,GACvBC,yBAA0BA,GAC1BC,gCAAiCA,GACjCH,mBAAoBA,IACnBmb,EACJ,GAAE,CAACA,IACArS,EA3gDN,WACE,IAAIA,EAAW5xB,GAAQw6B,GAAgB,IAMvC,OALA56B,EAAAA,EAAAA,YAAU,WACR,OAAO,WACL6O,sBAAsBmjB,EAASqJ,MAChC,CACF,GAAE,CAACrJ,IACGA,CACR,CAmgDgBuS,GACX9U,EAAmBrvB,IAAQ,WAC7B,OAAOoyB,GAAuBR,EAAUsS,EACzC,GAAE,CAACtS,EAAUsS,IACV1U,EAAexvB,IAAQ,WACzB,OAAOk3B,IAAmBxzB,EAAAA,EAAAA,GAAS,CACjCsvB,aAAcA,GACdne,gBAAiBwa,EAAiBxa,iBACjC3X,EAAmB,CACpBgsB,KAAMA,IACL+a,IACJ,GAAE,CAAC5U,EAAiBxa,gBAAiBovB,IAClC3U,EA5vDN,SAAyByI,GACvB,IAAIqM,GAAat/B,EAAAA,EAAAA,QAAO,CAAC,GACrBu/B,GAAYv/B,EAAAA,EAAAA,QAAO,MACnBw/B,GAAuBx/B,EAAAA,EAAAA,QAAO,MAC9By/B,GAAez/B,EAAAA,EAAAA,SAAO,GACtB+1B,EAAWhwB,IAAY,SAAkBgH,EAAIof,GAC/C,IAAI1E,EAAQ,CACV1a,GAAIA,EACJof,MAAOA,GAGT,OADAmT,EAAW7iC,QAAQsQ,GAAM0a,EAClB,WACL,IAAIJ,EAAUiY,EAAW7iC,QACX4qB,EAAQta,KAEN0a,UACPJ,EAAQta,EAElB,CACF,GAAE,IACC2yB,EAAe35B,IAAY,SAAsB45B,GACnD,IAAInK,EAASH,GAAepC,EAAW0M,GAEnCnK,GAAUA,IAAW76B,SAASilC,eAChCpK,EAAOrJ,OAEV,GAAE,CAAC8G,IACA/G,EAAiBnmB,IAAY,SAAwB+K,EAAU+uB,GAC7DN,EAAU9iC,UAAYqU,IACxByuB,EAAU9iC,QAAUojC,EAEvB,GAAE,IACC5T,EAA0BlmB,IAAY,WACpCy5B,EAAqB/iC,SAIpBgjC,EAAahjC,UAIlB+iC,EAAqB/iC,QAAUkN,uBAAsB,WACnD61B,EAAqB/iC,QAAU,KAC/B,IAAIqjC,EAASP,EAAU9iC,QAEnBqjC,GACFJ,EAAaI,EAEhB,IACF,GAAE,CAACJ,IACA1T,EAAiBjmB,IAAY,SAAwBgH,GACvDwyB,EAAU9iC,QAAU,KACpB,IAAIsjC,EAAUplC,SAASilC,cAElBG,GAIDA,EAAQtK,aAAa3C,GAAW7lB,eAAiBF,IAIrDwyB,EAAU9iC,QAAUsQ,EACrB,GAAE,IAoBH,OAnBAtS,IAA0B,WAExB,OADAglC,EAAahjC,SAAU,EAChB,WACLgjC,EAAahjC,SAAU,EACvB,IAAI+M,EAAUg2B,EAAqB/iC,QAE/B+M,GACFK,qBAAqBL,EAExB,CACF,GAAE,IACWtO,IAAQ,WACpB,MAAO,CACL66B,SAAUA,EACV/J,eAAgBA,EAChBC,wBAAyBA,EACzBC,eAAgBA,EAEnB,GAAE,CAAC6J,EAAU/J,EAAgBC,EAAyBC,GAExD,CAwqDoB8T,CAAgB/M,GAC/Bv5B,EAAQwB,IAAQ,WAClB,OAAOlF,GAAY,CACjBiyB,SAAUA,EACVyC,aAAcA,EACdH,iBAAkBA,EAClBC,aAAcA,EACd/B,cAAeA,EACfgC,aAAcA,GAEjB,GAAE,CAACxC,EAAUyC,EAAcH,EAAkBC,EAAc/B,EAAegC,IAQ3EgU,EAAahiC,QAAU/C,EACvB,IAAIumC,EAAgBl6B,IAAY,WAC9B,IAAItJ,EAAU2hC,GAASK,GAGH,SAFRhiC,EAAQ3F,WAEVqmB,OACR1gB,EAAQnF,SAr4HL,CACLE,KAAM,QACNqE,QAAS,MAq4HV,GAAE,IACC6P,EAAa3F,IAAY,WAC3B,IAAIpK,EAAQyiC,GAASK,GAAc3nC,WACnC,OAAO6E,EAAM+P,YAA8B,mBAAhB/P,EAAMwhB,KAClC,GAAE,IAOHtR,EANmB3Q,IAAQ,WACzB,MAAO,CACLwQ,WAAYA,EACZC,SAAUs0B,EAEb,GAAE,CAACv0B,EAAYu0B,KAEhB,IAAIC,EAAan6B,IAAY,SAAUgH,GACrC,OAAOkhB,GAAamQ,GAASK,GAAc3nC,WAAYiW,EACxD,GAAE,IACCozB,EAAuBp6B,IAAY,WACrC,OAAOmX,GAAkBkhB,GAASK,GAAc3nC,WACjD,GAAE,IACCspC,EAAallC,IAAQ,WACvB,MAAO,CACLovB,QAASC,EACT4B,MAAO3B,EACPyI,UAAWA,EACXoN,QAASH,EACThjB,kBAAmBijB,EACnBpB,8BAA+BA,EAC/BjS,SAAUA,EAEb,GAAE,CAACmG,EAAW1I,EAAkBwU,EAA+BvU,EAAc0V,EAAYC,EAAsBrT,IAWhH,OAVAwQ,GAAiB,CACfrK,UAAWA,EACXv5B,MAAOA,EACPozB,SAAUA,EACV0Q,cAAee,EACfd,sBAAqD,IAA/Bz+B,EAAMy+B,wBAE9B3iC,EAAAA,EAAAA,YAAU,WACR,OAAOmlC,CACR,GAAE,CAACA,IACG/mC,EAAAA,cAAoB29B,GAAWx7B,SAAU,CAC9C7H,MAAO4sC,GACNlnC,EAAAA,cAAoBmC,EAAU,CAC/BN,QAASq7B,GACT18B,MAAOA,GACNsF,EAAMhE,UACV,CAED,IAAIslC,GAAU,EAcd,SAASC,GAAgBvhC,GACvB,IAAIi0B,EAVG/3B,IAAQ,WACb,MAAO,GAAKolC,IACb,GAAE,IASC9B,EAA8Bx/B,EAAMw/B,6BAA+BrxB,GACvE,OAAOjU,EAAAA,cAAoBgS,GAAe,MAAM,SAAUW,GACxD,OAAO3S,EAAAA,cAAoBolC,GAAK,CAC9B1K,MAAO50B,EAAM40B,MACbX,UAAWA,EACXpnB,aAAcA,EACd2yB,4BAA6BA,EAC7Bf,qBAAsBz+B,EAAMy+B,qBAC5Bc,QAASv/B,EAAMu/B,QACfxV,gBAAiB/pB,EAAM+pB,gBACvBE,kBAAmBjqB,EAAMiqB,kBACzBI,YAAarqB,EAAMqqB,YACnBQ,aAAc7qB,EAAM6qB,aACpBhB,UAAW7pB,EAAM6pB,WAChB7pB,EAAMhE,SACV,GACF,CAED,IAAIwlC,GAAY,SAAiBzN,GAC/B,OAAO,SAAUv/B,GACf,OAAOu/B,IAASv/B,CACjB,CACF,EAEGitC,GAAWD,GAAU,UACrBE,GAASF,GAAU,QAGnBG,IAFcH,GAAU,WAEb,SAAkBhK,EAAUjtB,GACzC,OAAOA,EAAGitB,EAASoK,YAAcr3B,EAAGitB,EAASqK,UAC9C,GAMGC,GAAsB,SAA6B13B,GACrD,IAAIy1B,EAAQnkC,OAAO4O,iBAAiBF,GAChCotB,EAAW,CACboK,UAAW/B,EAAM+B,UACjBC,UAAWhC,EAAMgC,WAEnB,OAAOF,GAASnK,EAAUiK,KAAaE,GAASnK,EAAUkK,GAC3D,EA6BGK,GAAuB,SAASA,EAAqB33B,GACvD,OAAU,MAANA,GAIAA,IAAOzO,SAAS27B,MAIhBltB,IAAOzO,SAAS8xB,gBAPX,KAWJqU,GAAoB13B,GAIlBA,EAHE23B,EAAqB33B,EAAGkwB,cAIlC,EAgBG0H,GAAe,SAAU53B,GAC3B,MAAO,CACL7I,EAAG6I,EAAG63B,WACNzgC,EAAG4I,EAAG83B,UAET,EAEGC,GAAa,SAASA,EAAW/3B,GACnC,QAAKA,IAMkB,UAFX1O,OAAO4O,iBAAiBF,GAE1BkD,UAIH60B,EAAW/3B,EAAGkwB,eACtB,EAEG8H,GAAU,SAAUh0B,GAGtB,MAAO,CACLi0B,kBAHsBN,GAAqB3zB,GAI3C8U,cAHkBif,GAAW/zB,GAKhC,EA8FG+f,GAAgB,SAAUv1B,GAC5B,IAAIsI,EAAMtI,EAAKsI,IACX6Q,EAAanZ,EAAKmZ,WAClBuwB,EAAM1pC,EAAK0pC,IACXtU,EAAep1B,EAAKo1B,aACpB5Z,EAAYxb,EAAKwb,UACjBmuB,EAAiB3pC,EAAK2pC,eACtBprB,EAAmBve,EAAKue,iBACxBtH,EAAoBjX,EAAKiX,kBACzBwyB,EAAoBC,EAAID,kBACxBjpB,EAzCU,SAAmBopB,EAAWH,GAC5C,IAAItO,EAAO5pB,GAAOq4B,GAElB,IAAKH,EACH,OAAOtO,EAGT,GAAIyO,IAAcH,EAChB,OAAOtO,EAGT,IAAI9sB,EAAM8sB,EAAKxrB,WAAWtB,IAAMo7B,EAAkBH,UAC9C96B,EAAO2sB,EAAKxrB,WAAWnB,KAAOi7B,EAAkBJ,WAChD96B,EAASF,EAAMo7B,EAAkBhV,aACjCnmB,EAAQE,EAAOi7B,EAAkB/U,YAOjCvlB,EAAYP,GANC,CACfP,IAAKA,EACLC,MAAOA,EACPC,OAAQA,EACRC,KAAMA,GAE2B2sB,EAAK5rB,QAOxC,OANaL,GAAU,CACrBC,UAAWA,EACXE,OAAQ8rB,EAAK9rB,OACbE,OAAQ4rB,EAAK5rB,OACbE,QAAS0rB,EAAK1rB,SAGjB,CAYco6B,CAAUvhC,EAAKmhC,GACxBlyB,EAAOlH,GAAWmQ,EAAQ4U,GAE1B3e,EAAU,WACZ,IAAKgzB,EACH,OAAO,KAGT,IAAIK,EAAcv4B,GAAOk4B,GACrBM,EAAa,CACftV,aAAcgV,EAAkBhV,aAChCC,YAAa+U,EAAkB/U,aAEjC,MAAO,CACLlU,OAAQspB,EACRvyB,KAAMlH,GAAWy5B,EAAa1U,GAC9B9kB,OAAQ84B,GAAYK,GACpBM,WAAYA,EACZ9yB,kBAAmBA,EAEtB,CAjBa,GAmBV+L,EA5HuB,SAAUhjB,GACrC,IAAImZ,EAAanZ,EAAKmZ,WAClB+H,EAAYlhB,EAAKkhB,UACjB3C,EAAmBve,EAAKue,iBACxB+L,EAAgBtqB,EAAKsqB,cACrB9O,EAAYxb,EAAKwb,UACjBgF,EAASxgB,EAAKwgB,OACdjJ,EAAOvX,EAAKuX,KACZd,EAAUzW,EAAKyW,QAEfO,EAAQ,WACV,IAAKP,EACH,OAAO,KAGT,IAAIszB,EAAatzB,EAAQszB,WACrBD,EAAcrzB,EAAQ+J,OACtB6C,EAAYmR,GAAa,CAC3BC,aAAcsV,EAAWtV,aACzBC,YAAaqV,EAAWrV,YACxBhmB,OAAQo7B,EAAYn6B,WAAWjB,OAC/BD,MAAOq7B,EAAYn6B,WAAWlB,QAEhC,MAAO,CACL4I,cAAeZ,EAAQc,KAAK7H,UAC5Bo6B,YAAaA,EACbC,WAAYA,EACZ9yB,kBAAmBR,EAAQQ,kBAC3B3G,OAAQ,CACNxC,QAAS2I,EAAQnG,OACjBzL,QAAS4R,EAAQnG,OACjB6G,IAAKkM,EACL1L,KAAM,CACJ/b,MAAOga,GACPgC,aAAchC,KAIrB,CA5BW,GA8BR6B,EAAqB,aAAd+D,EAA2BD,GAAWO,GAkBjD,MAXgB,CACd3C,WAAYA,EACZoF,iBAAkBA,EAClB+L,cAAeA,EACf7S,KAAMA,EACNyJ,UAAWA,EACXV,OAAQA,EACRjJ,KAAMA,EACNP,MAAOA,EACPE,QAfYI,GAAW,CACvBC,KAAMA,EACNC,gBAAiB,KACjBC,KAAMA,EACNT,MAAOA,IAcV,CAiEiBgzB,CAAsB,CACpC7wB,WAAYA,EACZ+H,WAAYyoB,EACZprB,iBAAkBA,EAClB+L,cAAeof,EAAIpf,cACnB9O,UAAWA,EACXgF,OAAQA,EACRjJ,KAAMA,EACNd,QAASA,IAEX,OAAOuM,CACR,EAEGinB,GAAY,CACdhb,SAAS,GAEPib,GAAU,CACZjb,SAAS,GAEPkb,GAAsB,SAAU3gC,GAClC,OAAOA,EAAQqqB,yBAA2BoW,GAAYC,EACvD,EAED,SAASE,GAAmB5mC,GAC1B,IAAI0I,GAASvE,EAAAA,EAAAA,YAAWnE,GAExB,OADC0I,GAAuG6D,IAAU,GAC3G7D,CACR,CAED,IAAIm+B,GAA+B,SAAsCrZ,GACvE,OAAOA,GAAYA,EAAS0Y,IAAID,mBAAqB,IACtD,EAmKD,SAASa,KAAW,CAEpB,IAAIC,GAAQ,CACV97B,MAAO,EACPC,OAAQ,EACRW,OAh7Nc,CACdhB,IAAK,EACLC,MAAO,EACPC,OAAQ,EACRC,KAAM,IAm8NJg8B,GAAW,SAAkBhsC,GAC/B,IAAIisC,EAAyBjsC,EAAMisC,uBAC/BhhB,EAAcjrB,EAAMirB,YACpBihB,EAAUlsC,EAAMksC,QAChBhvB,EAxBQ,SAAiB1b,GAC7B,IAAIyqC,EAAyBzqC,EAAKyqC,uBAC9BhhB,EAAczpB,EAAKypB,YACnBihB,EAAU1qC,EAAK0qC,QAEnB,OAAID,GAIY,UAAZC,EAHKH,GAOF,CACL77B,OAAQ+a,EAAYjJ,OAAOrR,UAAUT,OACrCD,MAAOgb,EAAYjJ,OAAOrR,UAAUV,MACpCY,OAAQoa,EAAYjJ,OAAOnR,OAE9B,CAMYs7B,CAAQ,CACjBF,uBAAwBA,EACxBhhB,YAAaA,EACbihB,QAASA,IAEX,MAAO,CACLrD,QAAS5d,EAAY4d,QACrBuD,UAAW,aACXn8B,MAAOiN,EAAKjN,MACZC,OAAQgN,EAAKhN,OACbiC,UAAW+K,EAAKrM,OAAOhB,IACvBuC,YAAa8K,EAAKrM,OAAOf,MACzBuC,aAAc6K,EAAKrM,OAAOd,OAC1BuC,WAAY4K,EAAKrM,OAAOb,KACxBq8B,WAAY,IACZC,SAAU,IACVC,cAAe,OACftO,WAAwB,SAAZiO,EAAqBvd,GAAY1D,YAAc,KAE9D,EAkED,IAAIuhB,GAAgB1pC,EAAAA,MAhEpB,SAAqB8F,GACnB,IAAI6jC,GAAsB7iC,EAAAA,EAAAA,QAAO,MAC7B8iC,EAA2B/8B,IAAY,WACpC88B,EAAoBpmC,UAIzBmrB,aAAaib,EAAoBpmC,SACjComC,EAAoBpmC,QAAU,KAC/B,GAAE,IACC6lC,EAAUtjC,EAAMsjC,QAChBS,EAAkB/jC,EAAM+jC,gBACxBC,EAAUhkC,EAAMgkC,QAChB/P,EAAYj0B,EAAMi0B,UAElBgQ,GAAYt9B,EAAAA,EAAAA,UAA2B,SAAlB3G,EAAMsjC,SAC3BD,EAAyBY,EAAU,GACnCC,EAA4BD,EAAU,IAE1CnoC,EAAAA,EAAAA,YAAU,WACR,OAAKunC,EAIW,SAAZC,GACFQ,IACAI,GAA0B,GACnBhB,IAGLW,EAAoBpmC,QACfylC,IAGTW,EAAoBpmC,QAAU8qB,YAAW,WACvCsb,EAAoBpmC,QAAU,KAC9BymC,GAA0B,EAC3B,IACMJ,GAjBEZ,EAkBV,GAAE,CAACI,EAASD,EAAwBS,IACrC,IAAIK,EAAkBp9B,IAAY,SAAUyF,GACf,WAAvBA,EAAM43B,eAIVL,IAEgB,UAAZT,GACFU,IAEH,GAAE,CAACV,EAASU,EAASD,IAClBlE,EAAQuD,GAAS,CACnBC,uBAAwBA,EACxBC,QAAStjC,EAAMsjC,QACfjhB,YAAariB,EAAMqiB,cAErB,OAAOnoB,EAAAA,cAAoB8F,EAAMqiB,YAAY+X,QAAS,CACpDyF,MAAOA,EACP,kCAAmC5L,EACnC8P,gBAAiBI,EACjBjjC,IAAKlB,EAAMqkC,UAEd,IAIGC,GAAmBpqC,EAAAA,cAAoB,MAkE3C,IAAIqqC,GAAe,SAAUC,GAG3B,SAASD,IAGP,IAFA,IAAIn4B,EAEK3S,EAAOjE,UAAUZ,OAAQ8V,EAAO,IAAI/Q,MAAMF,GAAOG,EAAO,EAAGA,EAAOH,EAAMG,IAC/E8Q,EAAK9Q,GAAQpE,UAAUoE,GAoBzB,OAjBAwS,EAAQo4B,EAAqBviC,KAAKvM,MAAM8uC,EAAsB,CAAC/uC,MAAM4W,OAAO3B,KAAUjV,MAChFkH,MAAQ,CACZgY,UAAWrZ,QAAQ8Q,EAAMpM,MAAMykC,IAC/Bzb,KAAM5c,EAAMpM,MAAMykC,GAClBnB,QAASl3B,EAAMpM,MAAM2V,eAAiBvJ,EAAMpM,MAAMykC,GAAK,OAAS,QAGlEr4B,EAAM43B,QAAU,WACc,UAAxB53B,EAAMzP,MAAM2mC,SAIhBl3B,EAAMe,SAAS,CACbwH,WAAW,GAEd,EAEMvI,CACR,CAiDD,OA5EAU,EAAAA,EAAAA,GAAey3B,EAAcC,GA6B7BD,EAAaG,yBAA2B,SAAkC1kC,EAAOrD,GAC/E,OAAKqD,EAAM2V,cAQP3V,EAAMykC,GACD,CACL9vB,WAAW,EACXqU,KAAMhpB,EAAMykC,GACZnB,QAAS,QAIT3mC,EAAMgY,UACD,CACLA,WAAW,EACXqU,KAAMrsB,EAAMqsB,KACZsa,QAAS,SAIN,CACL3uB,WAAW,EACX2uB,QAAS,QACTta,KAAM,MA1BC,CACLrU,UAAWrZ,QAAQ0E,EAAMykC,IACzBzb,KAAMhpB,EAAMykC,GACZnB,QAAS,OAyBd,EAEYiB,EAAaxiC,UAEnBsL,OAAS,WACd,IAAK5X,KAAKkH,MAAMgY,UACd,OAAO,KAGT,IAAIgwB,EAAW,CACbX,QAASvuC,KAAKuuC,QACdhb,KAAMvzB,KAAKkH,MAAMqsB,KACjBsa,QAAS7tC,KAAKkH,MAAM2mC,SAEtB,OAAO7tC,KAAKuK,MAAMhE,SAAS2oC,EAC5B,EAEMJ,CACR,CA9EkB,CA8EjBrqC,EAAAA,eAEE0qC,GACQ,IADRA,GAEa,KAGbC,GAAwB,SAA+BC,EAA2B3Y,GACpF,OAAIA,EACKpG,GAAYL,KAAKyG,EAASjG,UAG/B4e,EACK/e,GAAYE,KAGdF,GAAYC,KACpB,EAEG+e,GAAqB,SAA4Bxe,EAAaye,GAChE,OAAKze,EAIEye,EAAkBh3B,GAAgB0X,KAAO1X,GAAgB6X,UAHvD,IAIV,EA2CD,SAASof,GAAWC,GAClB,MAAuB,aAAhBA,EAAO1sC,KAlChB,SAA0BoxB,GACxB,IACI/R,EADY+R,EAAShO,UACLxC,OAChBxQ,EAASghB,EAAShhB,OAClB4O,EAAcoS,EAASpS,YACvB2U,EAAWvC,EAASuC,SACpB5F,EAAcjrB,QAAQkc,GACtB7B,EAfyB,SAAkCiU,GAC/D,OAAmC,MAA/BA,EAASpU,mBACJoU,EAASpU,mBAGO,SAAlBoU,EAASpO,IACjB,CASqB2pB,CAAyBvb,GACzCob,EAAkB1pC,QAAQ6wB,GAC1BiZ,EAAYJ,EAAkB1e,GAAgB1d,EAAQ2d,GAAeD,GAAkB1d,GAc3F,MAbY,CACV0E,SAAU,QACVrG,IAAK4Q,EAAIvP,UAAUrB,IACnBG,KAAMyQ,EAAIvP,UAAUlB,KACpBo8B,UAAW,aACXn8B,MAAOwQ,EAAI9P,UAAUV,MACrBC,OAAQuQ,EAAI9P,UAAUT,OACtB+tB,WAAYwP,GAAsBlvB,EAAewW,GACjDiZ,UAAWA,EACXC,QAASN,GAAmBxe,EAAaye,GACzCM,OAAQN,EAAkBJ,GAA8BA,GACxDjB,cAAe,OAGlB,CAUqC4B,CAAiBL,GAP9C,CACLE,UAAW9e,IAFYkf,EAQwDN,GANxCt8B,QACvCysB,WAAYmQ,EAAUC,0BAA4B,KAAO,QAH7D,IAA2BD,CAS1B,CA8BD,SAASE,GAAsBh7B,GAC7B,IAAIssB,EAAWY,GAAY,aACvB7lB,EAAarH,EAAKqH,WAClB+b,EAAWpjB,EAAKojB,SAChB6X,EAAkBj7B,EAAKi7B,gBACvBjK,EAA6BhxB,EAAKgxB,2BAClCrC,EAA0B3uB,EAAK2uB,wBAC/Bvf,EAAYpP,EAAKoP,UACjB1X,EAAUlG,IAAQ,WACpB,MAAO,CACLw/B,2BAA4BA,EAC5BrC,wBAAyBA,EACzBvf,UAAWA,EAEd,GAAE,CAAC4hB,EAA4B5hB,EAAWuf,IACvClL,EAAepnB,IAAY,SAAUinB,GACvC,IAAI5jB,EAAKu7B,IAET,OADCv7B,GAA2GzB,IAAU,GA7C1H,SAAwBoJ,EAAY3H,EAAI4jB,QACjB,IAAjBA,IACFA,EAAexf,IAGjB,IAAIo3B,EAAiBlqC,OAAO4O,iBAAiBF,GACzCrC,EAAYqC,EAAGC,wBACf+O,EAAS/P,GAAatB,EAAW69B,GACjCz1B,EAAOlH,GAAWmQ,EAAQ4U,GAiB9B,MAPgB,CACdjc,WAAYA,EACZsQ,YAXgB,CAChBjJ,OAAQA,EACRghB,QAAShwB,EAAGgwB,QAAQC,cACpB4F,QAAS2F,EAAe3F,SASxBxoB,WAPe,CACflW,EAAG6X,EAAO9Q,UAAUjB,MACpB7F,EAAG4X,EAAO9Q,UAAUhB,QAMpB8R,OAAQA,EACRjJ,KAAMA,EAGT,CAoBU01B,CAAe9zB,EAAY3H,EAAI4jB,EACvC,GAAE,CAACjc,EAAY4zB,IACZld,EAAQvsB,IAAQ,WAClB,MAAO,CACL86B,SAAUA,EACVjlB,WAAYA,EACZ3P,QAASA,EACT+rB,aAAcA,EAEjB,GAAE,CAACpc,EAAYoc,EAAc/rB,EAAS40B,IACnC8O,GAAe9kC,EAAAA,EAAAA,QAAOynB,GACtBsd,GAAoB/kC,EAAAA,EAAAA,SAAO,GAC/BvF,IAA0B,WAExB,OADAqyB,EAASxb,UAAUykB,SAAS+O,EAAaroC,SAClC,WACL,OAAOqwB,EAASxb,UAAU2kB,WAAW6O,EAAaroC,QACnD,CACF,GAAE,CAACqwB,EAASxb,YACb7W,IAA0B,WACxB,GAAIsqC,EAAkBtoC,QACpBsoC,EAAkBtoC,SAAU,MAD9B,CAKA,IAAIxC,EAAO6qC,EAAaroC,QACxBqoC,EAAaroC,QAAUgrB,EACvBqF,EAASxb,UAAUjE,OAAOoa,EAAOxtB,EAJhC,CAKF,GAAE,CAACwtB,EAAOqF,EAASxb,WACrB,CAED,SAAS0zB,GAAgBhmC,EAAOi0B,EAAWgS,GACzCjO,IAoBD,CAUD,SAASkO,GAAgB15B,GACvBA,EAAMI,gBACP,CA2GD,IAAIu5B,GAAiB,SAAUpsC,EAAGC,GAChC,OAAOD,IAAMC,CACd,EAEGosC,GAA+B,SAAUthC,GAC3C,IAAIkJ,EAAUlJ,EAAOkJ,QACjBP,EAAc3I,EAAO2I,YAEzB,OAAIA,EACKA,EAAYE,YAGjBK,EACKA,EAAQL,YAGV,IACR,EAwGD,SAAS04B,GAAqBC,GAC5B,MAAO,CACL55B,YAAY,EACZs4B,iBAAiB,EACjBuB,SAAS,EACTC,cAAe,KACfhrB,KAAM,KACNirB,aAAc,KACdH,iBAAkBA,EAClB9uB,YAAa,KAEhB,CAED,IAAIkvB,GAAS,CACXxB,OAAQ,CACN1sC,KAAM,YACNoQ,OAAQ4F,GACR83B,iBAAkB,KAClBb,2BAA2B,EAC3BkB,SAAUN,GAAqB,QAwFnC,IAaIO,GAAqBC,IAbC,WACxB,IAAIC,EA1MN,WACE,IAAIC,GAAiBn1B,EAAAA,GAAAA,IAAW,SAAUrQ,EAAGC,GAC3C,MAAO,CACLD,EAAGA,EACHC,EAAGA,EAEN,IACGwlC,GAAsBp1B,EAAAA,GAAAA,IAAW,SAAU4J,EAAM+qB,EAASE,EAAcjvB,EAAa2U,GACvF,MAAO,CACLzf,YAAY,EACZ65B,QAASA,EACTvB,gBAAiB1pC,QAAQ6wB,GACzBqa,cAAera,EACf3Q,KAAMA,EACNirB,aAAcA,EACdjvB,YAAaA,EACb8uB,iBAAkB,KAErB,IACGW,GAAmBr1B,EAAAA,GAAAA,IAAW,SAAUhJ,EAAQ4S,EAAMI,EAAW2qB,EAASE,EAAcjvB,EAAahC,GACvG,MAAO,CACL0vB,OAAQ,CACN1sC,KAAM,WACN2zB,SAAU,KACVsa,aAAcA,EACdjvB,YAAaA,EACbgE,KAAMA,EACN5S,OAAQA,EACRgT,UAAWA,EACXpG,mBAAoBA,EACpBmxB,SAAUK,EAAoBxrB,EAAM+qB,EAASE,EAAcjvB,EAAa,OAG7E,IA0DD,OAxDe,SAAkB7a,EAAOkG,GACtC,GAAIlG,EAAM+P,WAAY,CACpB,GAAI/P,EAAMkhB,SAASvL,UAAUvE,KAAOlL,EAASoL,YAC3C,OAAO,KAGT,IAAIrF,EAASjM,EAAMc,QAAQ2b,OAAOxQ,OAC9BgT,EAAYjf,EAAM+gB,WAAWzL,WAAWpP,EAASoL,aACjDw4B,EAAenpB,GAAkB3gB,EAAM8V,QACvC+E,GAhDuD/E,EAgDhB9V,EAAM8V,QA/CvCC,IAAyB,YAAnBD,EAAOC,GAAGla,KAAqBia,EAAOC,GAAG1E,QAAQC,YAAc,KAgD3EuH,EAAqB7Y,EAAM6Y,mBAC/B,OAAOyxB,EAAiBF,EAAen+B,EAAOrH,EAAGqH,EAAOpH,GAAI7E,EAAMykB,aAAcxF,EAAW/Y,EAAS0jC,QAASE,EAAcjvB,EAAahC,EACzI,CAnD0B,IAAkC/C,EAqD7D,GAAoB,mBAAhB9V,EAAMwhB,MAA4B,CACpC,IAAI0E,EAAYlmB,EAAMkmB,UAEtB,GAAIA,EAAU/d,OAAOmJ,cAAgBpL,EAASoL,YAC5C,OAAO,KAGT,IAAIs4B,EAAU1jC,EAAS0jC,QACnBjX,EAAa3yB,EAAM+gB,WAAWzL,WAAWpP,EAASoL,aAClDnJ,EAAS+d,EAAU/d,OACnB0W,EAAO1W,EAAO0W,KAEd0rB,EAAgBd,GAA4BthC,GAE5CqiC,EAvEqB,SAAkCriC,GAC/D,OAAOA,EAAOkJ,QAAUlJ,EAAOkJ,QAAQC,YAAc,IACtD,CAqEwBm5B,CAAyBtiC,GAGxCqnB,EAAW,CACbjG,SAFavpB,EAAMkoB,aAGnBwiB,MAAOzhB,GACPS,OAAQ1pB,EAAMmoB,oBACdugB,QAAS8B,EAAen5B,GAAgB0X,KAAO,KAC/C4hB,MAAOH,EAAen5B,GAAc0X,KAAO,MAE7C,MAAO,CACLwf,OAAQ,CACN1sC,KAAM,WACNoQ,OAAQjM,EAAMmoB,oBACdlJ,UAAW0T,EACXnD,SAAUA,EACVsa,aAAcS,EACd1vB,YAAa2vB,EACb3rB,KAAMA,EACNhG,mBAAoB,KACpBmxB,SAAUK,EAAoBxrB,EAAM+qB,EAASW,EAAeC,EAAchb,IAG/E,CAED,OAAO,IACR,CAGF,CA8GwBob,GACnBC,EAtFN,WACE,IAAIT,GAAiBn1B,EAAAA,GAAAA,IAAW,SAAUrQ,EAAGC,GAC3C,MAAO,CACLD,EAAGA,EACHC,EAAGA,EAEN,IACGwlC,GAAsBp1B,EAAAA,GAAAA,GAAWy0B,IACjCY,GAAmBr1B,EAAAA,GAAAA,IAAW,SAAUhJ,EAAQ09B,EAAkBb,GAKpE,YAJyB,IAArBa,IACFA,EAAmB,MAGd,CACLpB,OAAQ,CACN1sC,KAAM,YACNoQ,OAAQA,EACR09B,iBAAkBA,EAClBb,0BAA2BA,EAC3BkB,SAAUK,EAAoBV,IAGnC,IAEGmB,EAAc,SAAqBnB,GACrC,OAAOA,EAAmBW,EAAiBz4B,GAAQ83B,GAAkB,GAAQ,IAC9E,EAEGoB,EAAW,SAAkBC,EAAOC,EAAYn1B,EAAQ8D,GAC1D,IAAIsxB,EAAqBp1B,EAAOa,UAAUH,QAAQw0B,GAC9CG,EAA+BxsC,QAAQib,EAAckL,eAAiBlL,EAAcC,SAASmxB,IAC7F35B,EAAU2E,GAAcF,GACxB6zB,EAAmBt4B,GAAWA,EAAQC,cAAgB05B,EAAQC,EAAa,KAE/E,IAAKC,EAAoB,CACvB,IAAKC,EACH,OAAOL,EAAYnB,GAGrB,GAAI7zB,EAAOa,UAAUJ,UAAUy0B,GAC7B,OAAO,KAGT,IAAI7+B,EAAS+F,GAAO0H,EAAchD,YAAYzE,OAE1Ci5B,EAAUhB,EAAej+B,EAAOvH,EAAGuH,EAAOtH,GAE9C,OAAOylC,EAAiBc,EAASzB,GAAkB,EACpD,CAED,GAAIwB,EACF,OAAOL,EAAYnB,GAGrB,IAAI7uB,EAAahF,EAAOc,YAAYzE,MAChClG,EAASm+B,EAAetvB,EAAWlW,EAAGkW,EAAWjW,GACrD,OAAOylC,EAAiBr+B,EAAQ09B,EAAkBuB,EAAmBlyB,cACtE,EAwBD,OAtBe,SAAkBhZ,EAAOkG,GACtC,GAAIlG,EAAM+P,WACR,OAAI/P,EAAMkhB,SAASvL,UAAUvE,KAAOlL,EAASoL,YACpC,KAGFy5B,EAAS7kC,EAASoL,YAAatR,EAAMkhB,SAASvL,UAAUvE,GAAIpR,EAAM8V,OAAQ9V,EAAM4Z,eAGzF,GAAoB,mBAAhB5Z,EAAMwhB,MAA4B,CACpC,IAAI0E,EAAYlmB,EAAMkmB,UAEtB,OAAIA,EAAU/d,OAAOmJ,cAAgBpL,EAASoL,YACrC,KAGFy5B,EAAS7kC,EAASoL,YAAa4U,EAAU/d,OAAOmJ,YAAa4U,EAAUpQ,OAAQoQ,EAAUtM,cACjG,CAED,OAAO,IACR,CAGF,CAIyByxB,GAMxB,OAJe,SAAkBrrC,EAAOkG,GACtC,OAAOikC,EAAiBnqC,EAAOkG,IAAa2kC,EAAkB7qC,EAAOkG,IAAa6jC,EACnF,CAGF,GACwB,CACvB/gB,sBAAuBA,IAEiD,KAAM,CAC9E5pB,QAASq7B,GACTv3B,MAAM,EACNiE,mBAAoBqiC,IAHGU,EA1VzB,SAAmB7mC,GACjB,IAAIkB,GAAMF,EAAAA,EAAAA,QAAO,MACbinC,EAASlhC,IAAY,SAAUqD,GACjClJ,EAAIzD,QAAU2M,CACf,GAAE,IACC67B,EAASl/B,IAAY,WACvB,OAAO7F,EAAIzD,OACZ,GAAE,IAECyqC,EAAsBlF,GAAmBnL,IACzC5D,EAAYiU,EAAoBjU,UAChC8L,EAAgCmI,EAAoBnI,8BACpDjS,EAAWoa,EAAoBpa,SAE/Bqa,EAAuBnF,GAAmBsB,IAC1C9rC,EAAO2vC,EAAqB3vC,KAC5BmV,EAAcw6B,EAAqBx6B,YAEnCoE,EAAa7V,IAAQ,WACvB,MAAO,CACL6R,GAAI/N,EAAMiO,YACV9V,MAAO6H,EAAM7H,MACbK,KAAMA,EACNmV,YAAaA,EAEhB,GAAE,CAAC3N,EAAMiO,YAAajO,EAAM7H,MAAOK,EAAMmV,IACtC3R,EAAWgE,EAAMhE,SACjBiS,EAAcjO,EAAMiO,YACpB6L,EAAY9Z,EAAM8Z,UAClBuf,EAA0Br5B,EAAMq5B,wBAChCqC,EAA6B17B,EAAM07B,2BACnC6K,EAAUvmC,EAAMumC,QAChBrB,EAASllC,EAAMklC,OACfkD,EAA8BpoC,EAAM2lB,sBACxCqgB,KA9CAlO,KAiDKyO,GAWHb,GAVmBxpC,IAAQ,WACzB,MAAO,CACL6V,WAAYA,EACZ+b,SAAUA,EACV6X,gBAAiBM,EACjBvK,2BAA4BA,EAC5BrC,wBAAyBA,EACzBvf,UAAWA,EAEd,GAAE,CAAC/H,EAAY+b,EAAUmY,EAAQvK,EAA4BrC,EAAyBvf,KAIzF,IAAIuuB,EAAkBnsC,IAAQ,WAC5B,OAAO4d,EAAY,CACjBwuB,SAAU,EACVC,KAAM,SACN,mBAAoBxI,EACpB,oCAAqC9xB,EACrC,kCAAmCgmB,EACnC3hB,WAAW,EACX+X,YAAa6b,IACX,IACL,GAAE,CAACjS,EAAW8L,EAA+B9xB,EAAa6L,IACvD0uB,EAAYzhC,IAAY,SAAUyF,GAChB,aAAhB04B,EAAO1sC,MAIN0sC,EAAO/Y,UAIe,cAAvB3f,EAAM43B,cAIVgE,GACD,GAAE,CAACA,EAA6BlD,IAC7BP,EAAWzoC,IAAQ,WACrB,IAAI2jC,EAAQoF,GAAWC,GACnBnB,EAAkC,aAAhBmB,EAAO1sC,MAAuB0sC,EAAO/Y,SAAWqc,EAAY,KAWlF,MAVa,CACXnE,SAAU4D,EACVQ,eAAgB,CACd,gCAAiCxU,EACjC,wBAAyBhmB,EACzB4xB,MAAOA,EACPkE,gBAAiBA,GAEnBsE,gBAAiBA,EAGpB,GAAE,CAACpU,EAAWoU,EAAiBp6B,EAAai3B,EAAQsD,EAAWP,IAC5DS,EAASxsC,IAAQ,WACnB,MAAO,CACL+R,YAAa8D,EAAWhE,GACxBvV,KAAMuZ,EAAWvZ,KACjBgV,OAAQ,CACNrV,MAAO4Z,EAAW5Z,MAClBwV,YAAaoE,EAAWpE,aAG7B,GAAE,CAACoE,EAAWpE,YAAaoE,EAAWhE,GAAIgE,EAAW5Z,MAAO4Z,EAAWvZ,OACxE,OAAOwD,EAAS2oC,EAAUO,EAAOyB,SAAU+B,EAC5C,IAyPD,SAASC,GAAiB3oC,GAIxB,OAHuBgjC,GAAmBsB,IACHsE,kBAEf5oC,EAAMiO,aAAgBjO,EAAMumC,QAI7CrsC,EAAAA,cAAoB0sC,GAAoB5mC,GAHtC,IAIV,CACD,SAAS6oC,GAAgB7oC,GACvB,IAAI8Z,EAA4C,mBAAzB9Z,EAAM8oC,iBAAgC9oC,EAAM8oC,eAC/DpN,EAA6BpgC,QAAQ0E,EAAM+oC,mCAC3C1P,EAA0B/9B,QAAQ0E,EAAMq5B,yBAC5C,OAAOn/B,EAAAA,cAAoByuC,IAAkB/oC,EAAAA,EAAAA,GAAS,CAAC,EAAGI,EAAO,CAC/DumC,SAAS,EACTzsB,UAAWA,EACX4hB,2BAA4BA,EAC5BrC,wBAAyBA,IAE5B,CAmHD,IAAI2P,GAAiB,SAAwBxwC,EAAMqlB,GACjD,OAAOrlB,IAASqlB,EAAS7M,UAAUxY,IACpC,EAEGywC,GAAe,SAAsBprB,EAAUH,GACjD,OAAOA,EAAWzL,WAAW4L,EAASvL,UAAUvE,GACjD,EA4ID,IAAIm7B,GAAe,CACjB1tB,KAAM,WACNhjB,KAAM,UACN4b,UAAW,WACXmuB,gBAAgB,EAChBprB,kBAAkB,EAClBgyB,yBAAyB,EACzBC,YAAa,KACbC,qBAbF,WAEE,OADC1tC,SAAS27B,MAAgG3uB,IAAU,GAC7GhN,SAAS27B,IACjB,GAYGgS,GAAqBzC,IApJG,WAC1B,IAAI0C,EAAoB,CACtBlnB,YAAa,KACbmnB,0BAA0B,EAC1B7C,SAAU,CACR8C,gBAAgB,EAChBC,iBAAkB,KAClBC,qBAAsB,KACtBC,oBAAoB,GAEtBC,SAAU,MAGRC,GAAuBlqC,EAAAA,EAAAA,GAAS,CAAC,EAAG2pC,EAAmB,CACzDC,0BAA0B,IAGxBO,GAAqBn4B,EAAAA,GAAAA,IAAW,SAAUG,GAC5C,MAAO,CACL9D,YAAa8D,EAAWhE,GACxBvV,KAAMuZ,EAAWvZ,KACjBgV,OAAQ,CACNrV,MAAO4Z,EAAW5Z,MAClBwV,YAAaoE,EAAWpE,aAG7B,IACGq8B,GAAcp4B,EAAAA,GAAAA,IAAW,SAAU7D,EAAI+L,EAAWmwB,EAA2BC,EAAyBtgB,EAAUwf,GAClH,IAAIn7B,EAAc2b,EAAS7X,WAAWhE,GAGtC,GAFa6b,EAAS7X,WAAWpE,cAAgBI,EAErC,CACV,IAAI87B,EAAWT,EAAc,CAC3B/7B,OAAQ+7B,EACRxf,SAAUmgB,EAAmBngB,EAAS7X,aACpC,KACAo4B,EAAY,CACdV,eAAgBQ,EAChBP,iBAAkBO,EAA4Bh8B,EAAc,KAC5D07B,qBAAsB17B,EACtB27B,oBAAoB,GAEtB,MAAO,CACLvnB,YAAauH,EAASvH,YACtBmnB,0BAA0B,EAC1B7C,SAAUwD,EACVN,SAAUA,EAEb,CAED,IAAK/vB,EACH,OAAOgwB,EAGT,IAAKI,EACH,OAAOX,EAGT,IAAI5C,EAAW,CACb8C,eAAgBQ,EAChBP,iBAAkBz7B,EAClB07B,qBAAsB,KACtBC,oBAAoB,GAEtB,MAAO,CACLvnB,YAAauH,EAASvH,YACtBmnB,0BAA0B,EAC1B7C,SAAUA,EACVkD,SAAU,KAEb,IAyDD,OAvDe,SAAkBltC,EAAOkG,GACtC,IAAIkL,EAAKlL,EAAS8K,YACdnV,EAAOqK,EAASrK,KAChBshB,GAAajX,EAAS0/B,eACtB6G,EAAcvmC,EAASumC,YAE3B,GAAIzsC,EAAM+P,WAAY,CACpB,IAAImR,EAAWlhB,EAAMkhB,SAErB,IAAKmrB,GAAexwC,EAAMqlB,GACxB,OAAOisB,EAGT,IAAIlgB,EAAWqf,GAAaprB,EAAUlhB,EAAM+gB,YACxC+rB,EAAiBnsB,GAAkB3gB,EAAM8V,UAAY1E,EACzD,OAAOi8B,EAAYj8B,EAAI+L,EAAW2vB,EAAgBA,EAAgB7f,EAAUwf,EAC7E,CAED,GAAoB,mBAAhBzsC,EAAMwhB,MAA4B,CACpC,IAAI0E,EAAYlmB,EAAMkmB,UAEtB,IAAKmmB,GAAexwC,EAAMqqB,EAAUhF,UAClC,OAAOisB,EAGT,IAAIrW,EAAYwV,GAAapmB,EAAUhF,SAAUlhB,EAAM+gB,YAEvD,OAAOssB,EAAYj8B,EAAI+L,EAAWssB,GAA4BvjB,EAAU/d,UAAYiJ,EAAIuP,GAAkBuF,EAAUpQ,UAAY1E,EAAI0lB,EAAW2V,EAChJ,CAED,GAAoB,SAAhBzsC,EAAMwhB,OAAoBxhB,EAAMkmB,YAAclmB,EAAMmmB,YAAa,CACnE,IAAIsnB,EAAaztC,EAAMkmB,UAEvB,IAAKmmB,GAAexwC,EAAM4xC,EAAWvsB,UACnC,OAAOisB,EAGT,IAAIlmB,EAAUtG,GAAkB8sB,EAAW33B,UAAY1E,EACnDs8B,EAAe/uC,QAAQ8uC,EAAW33B,OAAOC,IAAoC,YAA9B03B,EAAW33B,OAAOC,GAAGla,MACpE8xC,EAASF,EAAWvsB,SAAS7M,UAAUjD,KAAOA,EAElD,OAAI6V,EACKymB,EAAed,EAAoBO,EAGxCQ,EACKf,EAGFO,CACR,CAED,OAAOA,CACR,CAGF,GAC0B,CACzBS,wBA19K4B,SAAiC7/B,GAC7D,MAAO,CACLlS,KAAM,6BACNqE,QAAS6N,EAEZ,GAu+K6E,KAAM,CAClF3O,QAASq7B,GACTv3B,MAAM,EACNiE,mBAAoBqiC,IAHGU,EA7QzB,SAAmB7mC,GACjB,IAAIohC,GAAa7gC,EAAAA,EAAAA,YAAWs3B,IAC3BuJ,GAAsGz4B,IAAU,GACjH,IAAIsrB,EAAYmN,EAAWnN,UACvB/V,EAAoBkjB,EAAWljB,kBAC/BssB,GAAexpC,EAAAA,EAAAA,QAAO,MACtBypC,GAAiBzpC,EAAAA,EAAAA,QAAO,MACxBhF,EAAWgE,EAAMhE,SACjB2R,EAAc3N,EAAM2N,YACpBnV,EAAOwH,EAAMxH,KACbgjB,EAAOxb,EAAMwb,KACbpH,EAAYpU,EAAMoU,UAClB+0B,EAA0BnpC,EAAMmpC,wBAChC5G,EAAiBviC,EAAMuiC,eACvBprB,EAAmBnX,EAAMmX,iBACzBwvB,EAAW3mC,EAAM2mC,SACjBkD,EAAW7pC,EAAM6pC,SACjBU,EAA0BvqC,EAAMuqC,wBAChClB,EAAuBrpC,EAAMqpC,qBAC7BqB,EAAkB3jC,IAAY,WAChC,OAAOyjC,EAAa/sC,OACrB,GAAE,IACCktC,EAAkB5jC,IAAY,SAAUvS,GAC1Cg2C,EAAa/sC,QAAUjJ,CACxB,GAAE,IAICo2C,GAHoB7jC,IAAY,WAClC,OAAO0jC,EAAehtC,OACvB,GAAE,IACqBsJ,IAAY,SAAUvS,GAC5Ci2C,EAAehtC,QAAUjJ,CAC1B,GAAE,KAtqBHwjC,KA4qBA,IAAI6S,EAA6B9jC,IAAY,WACvCmX,KACFqsB,EAAwB,CACtBtuB,UAAWyR,MAGhB,GAAE,CAACxP,EAAmBqsB,KA//BzB,SAA+B7/B,GAC7B,IAAIogC,GAAmB9pC,EAAAA,EAAAA,QAAO,MAC1BogC,EAAa4B,GAAmBnL,IAChCb,EAAWY,GAAY,aACvB9J,EAAWsT,EAAWtT,SACtBxC,EAAU8V,EAAW9V,QACrByf,EAAc7S,GAAYxtB,GAC1BqH,EAAa7V,IAAQ,WACvB,MAAO,CACL6R,GAAIrD,EAAKiD,YACTnV,KAAMkS,EAAKlS,KACXgjB,KAAM9Q,EAAK8Q,KAEd,GAAE,CAAC9Q,EAAKiD,YAAajD,EAAK8Q,KAAM9Q,EAAKlS,OAClCwyC,GAAyBhqC,EAAAA,EAAAA,QAAO+Q,GAChCk5B,EAAuB/uC,IAAQ,WACjC,OAAO0V,EAAAA,GAAAA,IAAW,SAAUrQ,EAAGC,GAC5BspC,EAAiBrtC,SAA6GkL,IAAU,GACzI,IAAIO,EAAS,CACX3H,EAAGA,EACHC,EAAGA,GAEL8pB,EAAQrG,sBAAsBlT,EAAWhE,GAAI7E,EAC9C,GACF,GAAE,CAAC6I,EAAWhE,GAAIud,IACf4f,EAAmBnkC,IAAY,WACjC,IAAI6iB,EAAWkhB,EAAiBrtC,QAEhC,OAAKmsB,GAAaA,EAAS0Y,IAAID,kBAIxBL,GAAYpY,EAAS0Y,IAAID,mBAHvB7zB,EAIV,GAAE,IACC28B,EAAepkC,IAAY,WAC7B,IAAImC,EAASgiC,IACbD,EAAqB/hC,EAAO3H,EAAG2H,EAAO1H,EACvC,GAAE,CAAC0pC,EAAkBD,IAClBG,EAAuBlvC,IAAQ,WACjC,OAAO0rB,GAAQujB,EAChB,GAAE,CAACA,IACAE,EAAkBtkC,IAAY,WAChC,IAAI6iB,EAAWkhB,EAAiBrtC,QAC5B4R,EAAU4zB,GAA6BrZ,GACzCA,GAAYva,GAAuH1G,IAAU,GACjIihB,EAAS4C,cAEXC,yBACV0e,IAIFC,GACD,GAAE,CAACA,EAAsBD,IACtBjd,EAA6BnnB,IAAY,SAAUinB,EAAc5rB,GACjE0oC,EAAiBrtC,SAA6HkL,IAAU,GAC1J,IAAImJ,EAAWi5B,EAAYttC,QACvByD,EAAM4Q,EAAS44B,kBAClBxpC,GAA2GyH,IAAU,GACtH,IAAI25B,EAAMF,GAAOlhC,GACb0oB,EAAW,CACb1oB,IAAKA,EACL6Q,WAAYA,EACZuwB,IAAKA,EACL9V,cAAepqB,GAEjB0oC,EAAiBrtC,QAAUmsB,EAC3B,IAAIhO,EAAYuS,GAAa,CAC3BjtB,IAAKA,EACL6Q,WAAYA,EACZuwB,IAAKA,EACLtU,aAAcA,EACd5Z,UAAWtC,EAASsC,UACpBmuB,eAAgBzwB,EAASywB,eACzBprB,iBAAkBrF,EAASqF,iBAC3BtH,mBAAoBiC,EAASq3B,0BAE3Bj4B,EAAaoxB,EAAID,kBAWrB,OATInxB,IACFA,EAAW2jB,aAAaX,GAAgBD,UAAWmN,EAAWnN,WAC9D/iB,EAAWxF,iBAAiB,SAAU2/B,EAAiBtI,GAAmBnZ,EAAS4C,iBAO9E5Q,CACR,GAAE,CAACwlB,EAAWnN,UAAWliB,EAAYs5B,EAAiBN,IACnDpc,EAAyB5nB,IAAY,WACvC,IAAI6iB,EAAWkhB,EAAiBrtC,QAC5B4R,EAAU4zB,GAA6BrZ,GAE3C,OADEA,GAAYva,GAAyJ1G,IAAU,GAC1Kq5B,GAAY3yB,EACpB,GAAE,IACC2f,EAAcjoB,IAAY,WAC5B,IAAI6iB,EAAWkhB,EAAiBrtC,QAC/BmsB,GAA8GjhB,IAAU,GACzH,IAAI0G,EAAU4zB,GAA6BrZ,GAC3CkhB,EAAiBrtC,QAAU,KAEtB4R,IAIL+7B,EAAqBxgC,SACrByE,EAAQi8B,gBAAgBpX,GAAgBD,WACxC5kB,EAAQzD,oBAAoB,SAAUy/B,EAAiBtI,GAAmBnZ,EAAS4C,gBACpF,GAAE,CAAC6e,EAAiBD,IACjBliC,EAASnC,IAAY,SAAU+B,GACjC,IAAI8gB,EAAWkhB,EAAiBrtC,QAC/BmsB,GAA6GjhB,IAAU,GACxH,IAAI0G,EAAU4zB,GAA6BrZ,GAC1Cva,GAA6H1G,IAAU,GACxI0G,EAAQ6yB,WAAap5B,EAAOtH,EAC5B6N,EAAQ4yB,YAAcn5B,EAAOvH,CAC9B,GAAE,IACC+K,EAAYpQ,IAAQ,WACtB,MAAO,CACLgyB,2BAA4BA,EAC5BS,uBAAwBA,EACxBK,YAAaA,EACb9lB,OAAQA,EAEX,GAAE,CAAC8lB,EAAad,EAA4BS,EAAwBzlB,IACjEuf,EAAQvsB,IAAQ,WAClB,MAAO,CACL86B,SAAUA,EACVjlB,WAAYA,EACZzF,UAAWA,EAEd,GAAE,CAACA,EAAWyF,EAAYilB,IAC3Bv7B,IAA0B,WAGxB,OAFAuvC,EAAuBvtC,QAAUgrB,EAAM1W,WACvC+b,EAAS9c,UAAU+lB,SAAStO,GACrB,WACDqiB,EAAiBrtC,SAEnBuxB,IAGFlB,EAAS9c,UAAUimB,WAAWxO,EAC/B,CACF,GAAE,CAACnc,EAAWyF,EAAYid,EAAavG,EAAO6C,EAASwC,EAAS9c,YACjEvV,IAA0B,WACnBqvC,EAAiBrtC,SAItB6tB,EAAQpG,yBAAyB8lB,EAAuBvtC,QAAQsQ,IAAKrD,EAAK63B,eAC3E,GAAE,CAAC73B,EAAK63B,eAAgBjX,IACzB7vB,IAA0B,WACnBqvC,EAAiBrtC,SAItB6tB,EAAQnG,gCAAgC6lB,EAAuBvtC,QAAQsQ,GAAIrD,EAAKyM,iBACjF,GAAE,CAACzM,EAAKyM,iBAAkBmU,GAC5B,CAi2BCigB,CAAsB,CACpB59B,YAAaA,EACbnV,KAAMA,EACNgjB,KAAMA,EACNpH,UAAWA,EACXmuB,eAAgBA,EAChBprB,iBAAkBA,EAClBgyB,wBAAyBA,EACzBuB,gBAAiBA,IAEnB,IAAIroB,EAAcnoB,EAAAA,cAAoBqqC,GAAc,CAClDE,GAAIzkC,EAAMqiB,YACV1M,cAAe3V,EAAMwpC,2BACpB,SAAU5wC,GACX,IAAIorC,EAAUprC,EAAKorC,QACfhb,EAAOpwB,EAAKowB,KACZsa,EAAU1qC,EAAK0qC,QACnB,OAAOppC,EAAAA,cAAoB0pC,GAAe,CACxCvhB,YAAa2G,EACbgb,QAASA,EACTK,SAAUuG,EACVtH,QAASA,EACTrP,UAAWA,EACX8P,gBAAiB8G,GAEpB,IACGlG,EAAWzoC,IAAQ,WACrB,MAAO,CACLmoC,SAAUsG,EACVtoB,YAAaA,EACbmpB,eAAgB,CACd,wBAAyB79B,EACzB,gCAAiCsmB,GAGtC,GAAE,CAACA,EAAWtmB,EAAa0U,EAAasoB,IACrC/B,EAAkBiB,EAAWA,EAASjgB,SAAS3b,YAAc,KAC7Dw9B,EAAmBvvC,IAAQ,WAC7B,MAAO,CACLyR,YAAaA,EACbnV,KAAMA,EACNowC,gBAAiBA,EAEpB,GAAE,CAACj7B,EAAai7B,EAAiBpwC,IAsBlC,OAAO0B,EAAAA,cAAoBoqC,GAAiBjoC,SAAU,CACpD7H,MAAOi3C,GACNzvC,EAAS2oC,EAAUgC,GAtBtB,WACE,IAAKkD,EACH,OAAO,KAGT,IAAIjgB,EAAWigB,EAASjgB,SACpBvc,EAASw8B,EAASx8B,OAClBq+B,EAAOxxC,EAAAA,cAAoByuC,GAAkB,CAC/C16B,YAAa2b,EAAS3b,YACtB9V,MAAOyxB,EAASpc,OAAOrV,MACvBouC,SAAS,EACTzsB,WAAW,EACXuf,yBAAyB,EACzBqC,4BAA4B,IAC3B,SAAUiQ,EAAmBC,GAC9B,OAAOv+B,EAAOs+B,EAAmBC,EAAmBhiB,EACrD,IACD,OAAOiiB,GAAAA,aAAsBH,EAAMrC,IACpC,CAIgCyC,GAClC,IAmKDxC,GAAmBJ,aAAeA,4BCvzQjBlvC,EAAE,MAAM+xC,EAAE,MAAM5xB,EAAE,MAAM/b,EAAE,MAAM4tC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MAAMC,EAAE,MACnJ,GAAG,oBAAoB92C,QAAQA,OAAO+2C,IAAI,CAAC,IAAItrC,EAAEzL,OAAO+2C,IAAI7yC,EAAEuH,EAAE,iBAAiBwqC,EAAExqC,EAAE,gBAAgB4Y,EAAE5Y,EAAE,kBAAkBnD,EAAEmD,EAAE,qBAAqByqC,EAAEzqC,EAAE,kBAAkB0qC,EAAE1qC,EAAE,kBAAkB2qC,EAAE3qC,EAAE,iBAAiB4qC,EAAE5qC,EAAE,qBAAqB6qC,EAAE7qC,EAAE,kBAAkB8qC,EAAE9qC,EAAE,uBAAuB+qC,EAAE/qC,EAAE,cAAcgrC,EAAEhrC,EAAE,cAAcirC,EAAEjrC,EAAE,eAAekrC,EAAElrC,EAAE,sBAAsBmrC,EAAEnrC,EAAE,qBAAqBorC,EAAEprC,EAAE,0BAA0BqrC,EAAErrC,EAAE,sBAAuB,CAClc,SAASC,EAAEzH,GAAG,GAAG,kBAAkBA,GAAG,OAAOA,EAAE,CAAC,IAAI+yC,EAAE/yC,EAAEgzC,SAAS,OAAOD,GAAG,KAAK9yC,EAAE,OAAOD,EAAEA,EAAEvB,MAAQ,KAAK2hB,EAAE,KAAK6xB,EAAE,KAAK5tC,EAAE,KAAKguC,EAAE,KAAKC,EAAE,OAAOtyC,EAAE,QAAQ,OAAOA,EAAEA,GAAGA,EAAEgzC,UAAY,KAAKb,EAAE,KAAKC,EAAE,KAAKI,EAAE,KAAKD,EAAE,KAAKL,EAAE,OAAOlyC,EAAE,QAAQ,OAAO+yC,GAAG,KAAKf,EAAE,OAAOe,EAAG,CAAC,CACzJE,EAAQ1sC,kBAAkB,SAASvG,GAAG,OAAOyH,EAAEzH,KAAKmyC,CAAE,yBCR9Je,EAAOD,QAAU,EAAjBC", "sources": ["../node_modules/memoize-one/dist/memoize-one.esm.js", "../node_modules/redux/es/redux.js", "../node_modules/react-redux/es/components/Context.js", "../node_modules/react-redux/es/utils/batch.js", "../node_modules/react-redux/es/utils/Subscription.js", "../node_modules/react-redux/es/utils/useIsomorphicLayoutEffect.js", "../node_modules/react-redux/es/components/Provider.js", "../node_modules/react-redux/es/components/connectAdvanced.js", "../node_modules/react-redux/es/utils/shallowEqual.js", "../node_modules/react-redux/es/connect/wrapMapToProps.js", "../node_modules/react-redux/es/connect/mapDispatchToProps.js", "../node_modules/react-redux/es/utils/bindActionCreators.js", "../node_modules/react-redux/es/connect/mapStateToProps.js", "../node_modules/react-redux/es/connect/mergeProps.js", "../node_modules/react-redux/es/connect/selectorFactory.js", "../node_modules/react-redux/es/connect/connect.js", "../node_modules/react-redux/es/hooks/useSelector.js", "../node_modules/use-memo-one/dist/use-memo-one.esm.js", "../node_modules/react-redux/es/index.js", "../node_modules/css-box-model/dist/css-box-model.esm.js", "../node_modules/raf-schd/dist/raf-schd.esm.js", "../node_modules/react-beautiful-dnd/dist/react-beautiful-dnd.esm.js", "../node_modules/react-redux/node_modules/react-is/cjs/react-is.production.min.js", "../node_modules/react-redux/node_modules/react-is/index.js"], "sourcesContent": ["var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + 'use the non-minified dev environment for full errors. ';\n}\n\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = (function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n})();\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\n\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  var type = typeof val;\n\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  var constructorName = ctorName(val);\n\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  } // other\n\n\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\n\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\n\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\n\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\n\nfunction kindOf(val) {\n  var typeOfVal = typeof val;\n\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n\n  return typeOfVal;\n}\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n    }\n\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n  }\n\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n      currentListeners = null;\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n    }\n\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : 'Reducers may not dispatch actions.');\n    }\n\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n\n    var listeners = currentListeners = nextListeners;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n    }\n\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n\n  function observable() {\n    var _ref;\n\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nvar legacy_createStore = createStore;\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + kindOf(inputState) + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle '\" + ActionTypes.INIT + \"' or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n\n  var shapeAssertionError;\n\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n\n    var hasChanged = false;\n    var nextState = {};\n\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n\n      if (typeof nextStateForKey === 'undefined') {\n        var actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"When called with an action of type \" + (actionType ? \"\\\"\" + String(actionType) + \"\\\"\" : '(unknown type)') + \", the slice reducer for key \\\"\" + _key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n      }\n\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n\n  var boundActionCreators = {};\n\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n\n  return boundActionCreators;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n\n      var _dispatch = function dispatch() {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return _objectSpread(_objectSpread({}, store), {}, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\n\n/*\n * This is a dummy function to check if the function name has been altered by minification.\n * If the function has been minified and NODE_ENV !== 'production', warn the user.\n */\n\nfunction isCrushed() {}\n\nif (process.env.NODE_ENV !== 'production' && typeof isCrushed.name === 'string' && isCrushed.name !== 'isCrushed') {\n  warning('You are currently using minified code outside of NODE_ENV === \"production\". ' + 'This means that you are running a slower development build of Redux. ' + 'You can use loose-envify (https://github.com/zertosh/loose-envify) for browserify ' + 'or setting mode to production in webpack (https://webpack.js.org/concepts/mode/) ' + 'to ensure you have the correct code for your production build.');\n}\n\nexport { ActionTypes as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore, legacy_createStore };\n", "import React from 'react';\nexport var ReactReduxContext = /*#__PURE__*/React.createContext(null);\n\nif (process.env.NODE_ENV !== 'production') {\n  ReactReduxContext.displayName = 'ReactRedux';\n}\n\nexport default ReactReduxContext;", "// Default to a dummy \"batch\" implementation that just runs the callback\nfunction defaultNoopBatch(callback) {\n  callback();\n}\n\nvar batch = defaultNoopBatch; // Allow injecting another batching function later\n\nexport var setBatch = function setBatch(newBatch) {\n  return batch = newBatch;\n}; // Supply a getter just to skip dealing with ESM bindings\n\nexport var getBatch = function getBatch() {\n  return batch;\n};", "import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  var batch = getBatch();\n  var first = null;\n  var last = null;\n  return {\n    clear: function clear() {\n      first = null;\n      last = null;\n    },\n    notify: function notify() {\n      batch(function () {\n        var listener = first;\n\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get: function get() {\n      var listeners = [];\n      var listener = first;\n\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n\n      return listeners;\n    },\n    subscribe: function subscribe(callback) {\n      var isSubscribed = true;\n      var listener = last = {\n        callback: callback,\n        next: null,\n        prev: last\n      };\n\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\n\nvar nullListeners = {\n  notify: function notify() {},\n  get: function get() {\n    return [];\n  }\n};\nexport function createSubscription(store, parentSub) {\n  var unsubscribe;\n  var listeners = nullListeners;\n\n  function addNestedSub(listener) {\n    trySubscribe();\n    return listeners.subscribe(listener);\n  }\n\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n\n  function isSubscribed() {\n    return Boolean(unsubscribe);\n  }\n\n  function trySubscribe() {\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n\n  function tryUnsubscribe() {\n    if (unsubscribe) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n\n  var subscription = {\n    addNestedSub: addNestedSub,\n    notifyNestedSubs: notifyNestedSubs,\n    handleChangeWrapper: handleChangeWrapper,\n    isSubscribed: isSubscribed,\n    trySubscribe: trySubscribe,\n    tryUnsubscribe: tryUnsubscribe,\n    getListeners: function getListeners() {\n      return listeners;\n    }\n  };\n  return subscription;\n}", "import { useEffect, useLayoutEffect } from 'react'; // <PERSON><PERSON> currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser. We need useLayoutEffect to ensure the store\n// subscription callback always has the selector from the latest render commit\n// available, otherwise a store update may happen between render and the effect,\n// which may cause missed updates; we also must ensure the store subscription\n// is created synchronously, otherwise a store update may occur before the\n// subscription is created and an inconsistent state may be observed\n\nexport var useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;", "import React, { useMemo } from 'react';\nimport PropTypes from 'prop-types';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\n\nfunction Provider(_ref) {\n  var store = _ref.store,\n      context = _ref.context,\n      children = _ref.children;\n  var contextValue = useMemo(function () {\n    var subscription = createSubscription(store);\n    return {\n      store: store,\n      subscription: subscription\n    };\n  }, [store]);\n  var previousState = useMemo(function () {\n    return store.getState();\n  }, [store]);\n  useIsomorphicLayoutEffect(function () {\n    var subscription = contextValue.subscription;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n\n    return function () {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = null;\n    };\n  }, [contextValue, previousState]);\n  var Context = context || ReactReduxContext;\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nif (process.env.NODE_ENV !== 'production') {\n  Provider.propTypes = {\n    store: PropTypes.shape({\n      subscribe: PropTypes.func.isRequired,\n      dispatch: PropTypes.func.isRequired,\n      getState: PropTypes.func.isRequired\n    }),\n    context: PropTypes.object,\n    children: PropTypes.any\n  };\n}\n\nexport default Provider;", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"getDisplayName\", \"methodName\", \"renderCountProp\", \"shouldHandleStateChanges\", \"storeKey\", \"withRef\", \"forwardRef\", \"context\"],\n    _excluded2 = [\"reactReduxForwardedRef\"];\nimport hoistStatics from 'hoist-non-react-statics';\nimport React, { useContext, useMemo, useRef, useReducer } from 'react';\nimport { isValidElementType, isContextConsumer } from 'react-is';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from './Context'; // Define some constant arrays just to avoid re-creating these\n\nvar EMPTY_ARRAY = [];\nvar NO_SUBSCRIPTION_ARRAY = [null, null];\n\nvar stringifyComponent = function stringifyComponent(Comp) {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\n\nfunction storeStateUpdatesReducer(state, action) {\n  var updateCount = state[1];\n  return [action.payload, updateCount + 1];\n}\n\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(function () {\n    return effectFunc.apply(void 0, effectArgs);\n  }, dependencies);\n}\n\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps;\n  lastChildProps.current = actualChildProps;\n  renderIsScheduled.current = false; // If the render was from a store update, clear out that reference and cascade the subscriber update\n\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n}\n\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return; // Capture values for checking if and when this component unmounts\n\n  var didUnsubscribe = false;\n  var lastThrownError = null; // We'll run this callback every time a store subscription update propagates to this component\n\n  var checkForUpdates = function checkForUpdates() {\n    if (didUnsubscribe) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return;\n    }\n\n    var latestStoreState = store.getState();\n    var newChildProps, error;\n\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n\n    if (!error) {\n      lastThrownError = null;\n    } // If the child props haven't changed, nothing to do here - cascade the subscription update\n\n\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true; // If the child props _did_ change (or we caught an error), this wrapper component needs to re-render\n\n      forceComponentUpdateDispatch({\n        type: 'STORE_UPDATED',\n        payload: {\n          error: error\n        }\n      });\n    }\n  }; // Actually subscribe to the nearest connected ancestor (or store)\n\n\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe(); // Pull data from the store after first render in case the store has\n  // changed since we began.\n\n  checkForUpdates();\n\n  var unsubscribeWrapper = function unsubscribeWrapper() {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError;\n    }\n  };\n\n  return unsubscribeWrapper;\n}\n\nvar initStateUpdates = function initStateUpdates() {\n  return [null, 0];\n};\n\nexport default function connectAdvanced(\n/*\r\n  selectorFactory is a func that is responsible for returning the selector function used to\r\n  compute new props from state, props, and dispatch. For example:\r\n      export default connectAdvanced((dispatch, options) => (state, props) => ({\r\n      thing: state.things[props.thingId],\r\n      saveThing: fields => dispatch(actionCreators.saveThing(props.thingId, fields)),\r\n    }))(YourComponent)\r\n    Access to dispatch is provided to the factory so selectorFactories can bind actionCreators\r\n  outside of their selector as an optimization. Options passed to connectAdvanced are passed to\r\n  the selectorFactory, along with displayName and WrappedComponent, as the second argument.\r\n    Note that selectorFactory is responsible for all caching/memoization of inbound and outbound\r\n  props. Do not use connectAdvanced directly without memoizing results between calls to your\r\n  selector, otherwise the Connect component will re-render on every state or props change.\r\n*/\nselectorFactory, // options object:\n_ref) {\n  if (_ref === void 0) {\n    _ref = {};\n  }\n\n  var _ref2 = _ref,\n      _ref2$getDisplayName = _ref2.getDisplayName,\n      getDisplayName = _ref2$getDisplayName === void 0 ? function (name) {\n    return \"ConnectAdvanced(\" + name + \")\";\n  } : _ref2$getDisplayName,\n      _ref2$methodName = _ref2.methodName,\n      methodName = _ref2$methodName === void 0 ? 'connectAdvanced' : _ref2$methodName,\n      _ref2$renderCountProp = _ref2.renderCountProp,\n      renderCountProp = _ref2$renderCountProp === void 0 ? undefined : _ref2$renderCountProp,\n      _ref2$shouldHandleSta = _ref2.shouldHandleStateChanges,\n      shouldHandleStateChanges = _ref2$shouldHandleSta === void 0 ? true : _ref2$shouldHandleSta,\n      _ref2$storeKey = _ref2.storeKey,\n      storeKey = _ref2$storeKey === void 0 ? 'store' : _ref2$storeKey,\n      _ref2$withRef = _ref2.withRef,\n      withRef = _ref2$withRef === void 0 ? false : _ref2$withRef,\n      _ref2$forwardRef = _ref2.forwardRef,\n      forwardRef = _ref2$forwardRef === void 0 ? false : _ref2$forwardRef,\n      _ref2$context = _ref2.context,\n      context = _ref2$context === void 0 ? ReactReduxContext : _ref2$context,\n      connectOptions = _objectWithoutPropertiesLoose(_ref2, _excluded);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (renderCountProp !== undefined) {\n      throw new Error(\"renderCountProp is removed. render counting is built into the latest React Dev Tools profiling extension\");\n    }\n\n    if (withRef) {\n      throw new Error('withRef is removed. To access the wrapped instance, use a ref on the connected component');\n    }\n\n    var customStoreWarningMessage = 'To use a custom Redux store for specific components, create a custom React context with ' + \"React.createContext(), and pass the context object to React Redux's Provider and specific components\" + ' like: <Provider context={MyContext}><ConnectedComponent context={MyContext} /></Provider>. ' + 'You may also pass a {context : MyContext} option to connect';\n\n    if (storeKey !== 'store') {\n      throw new Error('storeKey has been removed and does not do anything. ' + customStoreWarningMessage);\n    }\n  }\n\n  var Context = context;\n  return function wrapWithConnect(WrappedComponent) {\n    if (process.env.NODE_ENV !== 'production' && !isValidElementType(WrappedComponent)) {\n      throw new Error(\"You must pass a component to the function returned by \" + (methodName + \". Instead received \" + stringifyComponent(WrappedComponent)));\n    }\n\n    var wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n    var displayName = getDisplayName(wrappedComponentName);\n\n    var selectorFactoryOptions = _extends({}, connectOptions, {\n      getDisplayName: getDisplayName,\n      methodName: methodName,\n      renderCountProp: renderCountProp,\n      shouldHandleStateChanges: shouldHandleStateChanges,\n      storeKey: storeKey,\n      displayName: displayName,\n      wrappedComponentName: wrappedComponentName,\n      WrappedComponent: WrappedComponent\n    });\n\n    var pure = connectOptions.pure;\n\n    function createChildSelector(store) {\n      return selectorFactory(store.dispatch, selectorFactoryOptions);\n    } // If we aren't running in \"pure\" mode, we don't want to memoize values.\n    // To avoid conditionally calling hooks, we fall back to a tiny wrapper\n    // that just executes the given callback immediately.\n\n\n    var usePureOnlyMemo = pure ? useMemo : function (callback) {\n      return callback();\n    };\n\n    function ConnectFunction(props) {\n      var _useMemo = useMemo(function () {\n        // Distinguish between actual \"data\" props that were passed to the wrapper component,\n        // and values needed to control behavior (forwarded refs, alternate context instances).\n        // To maintain the wrapperProps object reference, memoize this destructuring.\n        var reactReduxForwardedRef = props.reactReduxForwardedRef,\n            wrapperProps = _objectWithoutPropertiesLoose(props, _excluded2);\n\n        return [props.context, reactReduxForwardedRef, wrapperProps];\n      }, [props]),\n          propsContext = _useMemo[0],\n          reactReduxForwardedRef = _useMemo[1],\n          wrapperProps = _useMemo[2];\n\n      var ContextToUse = useMemo(function () {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        return propsContext && propsContext.Consumer && isContextConsumer( /*#__PURE__*/React.createElement(propsContext.Consumer, null)) ? propsContext : Context;\n      }, [propsContext, Context]); // Retrieve the store and ancestor subscription via context, if available\n\n      var contextValue = useContext(ContextToUse); // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n\n      var didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      var didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n\n      if (process.env.NODE_ENV !== 'production' && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(\"Could not find \\\"store\\\" in the context of \" + (\"\\\"\" + displayName + \"\\\". Either wrap the root component in a <Provider>, \") + \"or pass a custom React context provider to <Provider> and the corresponding \" + (\"React context consumer to \" + displayName + \" in connect options.\"));\n      } // Based on the previous check, one of these must be true\n\n\n      var store = didStoreComeFromProps ? props.store : contextValue.store;\n      var childPropsSelector = useMemo(function () {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return createChildSelector(store);\n      }, [store]);\n\n      var _useMemo2 = useMemo(function () {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY; // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n\n        // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n        var subscription = createSubscription(store, didStoreComeFromProps ? null : contextValue.subscription); // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n\n        // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n        var notifyNestedSubs = subscription.notifyNestedSubs.bind(subscription);\n        return [subscription, notifyNestedSubs];\n      }, [store, didStoreComeFromProps, contextValue]),\n          subscription = _useMemo2[0],\n          notifyNestedSubs = _useMemo2[1]; // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n\n\n      var overriddenContextValue = useMemo(function () {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue;\n        } // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n\n\n        return _extends({}, contextValue, {\n          subscription: subscription\n        });\n      }, [didStoreComeFromProps, contextValue, subscription]); // We need to force this wrapper component to re-render whenever a Redux store update\n      // causes a change to the calculated child component props (or we caught an error in mapState)\n\n      var _useReducer = useReducer(storeStateUpdatesReducer, EMPTY_ARRAY, initStateUpdates),\n          _useReducer$ = _useReducer[0],\n          previousStateUpdateResult = _useReducer$[0],\n          forceComponentUpdateDispatch = _useReducer[1]; // Propagate any mapState/mapDispatch errors upwards\n\n\n      if (previousStateUpdateResult && previousStateUpdateResult.error) {\n        throw previousStateUpdateResult.error;\n      } // Set up refs to coordinate values between the subscription effect and the render logic\n\n\n      var lastChildProps = useRef();\n      var lastWrapperProps = useRef(wrapperProps);\n      var childPropsFromStoreUpdate = useRef();\n      var renderIsScheduled = useRef(false);\n      var actualChildProps = usePureOnlyMemo(function () {\n        // Tricky logic here:\n        // - This render may have been triggered by a Redux store update that produced new child props\n        // - However, we may have gotten new wrapper props after that\n        // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n        // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n        // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n        if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n          return childPropsFromStoreUpdate.current;\n        } // TODO We're reading the store directly in render() here. Bad idea?\n        // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n        // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n        // to determine what the child props should be.\n\n\n        return childPropsSelector(store.getState(), wrapperProps);\n      }, [store, previousStateUpdateResult, wrapperProps]); // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs]); // Our re-subscribe logic only runs when the store/subscription setup changes\n\n      useIsomorphicLayoutEffectWithArgs(subscribeUpdates, [shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch], [store, subscription, childPropsSelector]); // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n\n      var renderedWrappedComponent = useMemo(function () {\n        return /*#__PURE__*/React.createElement(WrappedComponent, _extends({}, actualChildProps, {\n          ref: reactReduxForwardedRef\n        }));\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]); // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n\n      var renderedChild = useMemo(function () {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return /*#__PURE__*/React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    } // If we're in \"pure\" mode, ensure our wrapper component only re-renders when incoming props have changed.\n\n\n    var Connect = pure ? React.memo(ConnectFunction) : ConnectFunction;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n\n    if (forwardRef) {\n      var forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        return /*#__PURE__*/React.createElement(Connect, _extends({}, props, {\n          reactReduxForwardedRef: ref\n        }));\n      });\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return hoistStatics(forwarded, WrappedComponent);\n    }\n\n    return hoistStatics(Connect, WrappedComponent);\n  };\n}", "function is(x, y) {\n  if (x === y) {\n    return x !== 0 || y !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\n\nexport default function shallowEqual(objA, objB) {\n  if (is(objA, objB)) return true;\n\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n}", "import verifyPlainObject from '../utils/verifyPlainObject';\nexport function wrapMapToPropsConstant(getConstant) {\n  return function initConstantSelector(dispatch, options) {\n    var constant = getConstant(dispatch, options);\n\n    function constantSelector() {\n      return constant;\n    }\n\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n} // dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n\nexport function getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps !== null && mapToProps.dependsOnOwnProps !== undefined ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n} // Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\n\nexport function wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, _ref) {\n    var displayName = _ref.displayName;\n\n    var proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch);\n    }; // allow detectFactoryAndVerify to get ownProps\n\n\n    proxy.dependsOnOwnProps = true;\n\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      var props = proxy(stateOrDispatch, ownProps);\n\n      if (typeof props === 'function') {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n\n      if (process.env.NODE_ENV !== 'production') verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n\n    return proxy;\n  };\n}", "import bindActionCreators from '../utils/bindActionCreators';\nimport { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapDispatchToPropsIsFunction(mapDispatchToProps) {\n  return typeof mapDispatchToProps === 'function' ? wrapMapToPropsFunc(mapDispatchToProps, 'mapDispatchToProps') : undefined;\n}\nexport function whenMapDispatchToPropsIsMissing(mapDispatchToProps) {\n  return !mapDispatchToProps ? wrapMapToPropsConstant(function (dispatch) {\n    return {\n      dispatch: dispatch\n    };\n  }) : undefined;\n}\nexport function whenMapDispatchToPropsIsObject(mapDispatchToProps) {\n  return mapDispatchToProps && typeof mapDispatchToProps === 'object' ? wrapMapToPropsConstant(function (dispatch) {\n    return bindActionCreators(mapDispatchToProps, dispatch);\n  }) : undefined;\n}\nexport default [whenMapDispatchToPropsIsFunction, whenMapDispatchToPropsIsMissing, whenMapDispatchToPropsIsObject];", "export default function bindActionCreators(actionCreators, dispatch) {\n  var boundActionCreators = {};\n\n  var _loop = function _loop(key) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = function () {\n        return dispatch(actionCreator.apply(void 0, arguments));\n      };\n    }\n  };\n\n  for (var key in actionCreators) {\n    _loop(key);\n  }\n\n  return boundActionCreators;\n}", "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapStateToPropsIsFunction(mapStateToProps) {\n  return typeof mapStateToProps === 'function' ? wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps') : undefined;\n}\nexport function whenMapStateToPropsIsMissing(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(function () {\n    return {};\n  }) : undefined;\n}\nexport default [whenMapStateToPropsIsFunction, whenMapStateToPropsIsMissing];", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport verifyPlainObject from '../utils/verifyPlainObject';\nexport function defaultMergeProps(stateProps, dispatchProps, ownProps) {\n  return _extends({}, ownProps, stateProps, dispatchProps);\n}\nexport function wrapMergePropsFunc(mergeProps) {\n  return function initMergePropsProxy(dispatch, _ref) {\n    var displayName = _ref.displayName,\n        pure = _ref.pure,\n        areMergedPropsEqual = _ref.areMergedPropsEqual;\n    var hasRunOnce = false;\n    var mergedProps;\n    return function mergePropsProxy(stateProps, dispatchProps, ownProps) {\n      var nextMergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n\n      if (hasRunOnce) {\n        if (!pure || !areMergedPropsEqual(nextMergedProps, mergedProps)) mergedProps = nextMergedProps;\n      } else {\n        hasRunOnce = true;\n        mergedProps = nextMergedProps;\n        if (process.env.NODE_ENV !== 'production') verifyPlainObject(mergedProps, displayName, 'mergeProps');\n      }\n\n      return mergedProps;\n    };\n  };\n}\nexport function whenMergePropsIsFunction(mergeProps) {\n  return typeof mergeProps === 'function' ? wrapMergePropsFunc(mergeProps) : undefined;\n}\nexport function whenMergePropsIsOmitted(mergeProps) {\n  return !mergeProps ? function () {\n    return defaultMergeProps;\n  } : undefined;\n}\nexport default [whenMergePropsIsFunction, whenMergePropsIsOmitted];", "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"initMapStateToProps\", \"initMapDispatchToProps\", \"initMergeProps\"];\nimport verifySubselectors from './verifySubselectors';\nexport function impureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch) {\n  return function impureFinalPropsSelector(state, ownProps) {\n    return mergeProps(mapStateToProps(state, ownProps), mapDispatchToProps(dispatch, ownProps), ownProps);\n  };\n}\nexport function pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, _ref) {\n  var areStatesEqual = _ref.areStatesEqual,\n      areOwnPropsEqual = _ref.areOwnPropsEqual,\n      areStatePropsEqual = _ref.areStatePropsEqual;\n  var hasRunAtLeastOnce = false;\n  var state;\n  var ownProps;\n  var stateProps;\n  var dispatchProps;\n  var mergedProps;\n\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewState() {\n    var nextStateProps = mapStateToProps(state, ownProps);\n    var statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    var propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    var stateChanged = !areStatesEqual(nextState, state);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n} // TODO: Add more comments\n// If pure is true, the selector returned by selectorFactory will memoize its results,\n// allowing connectAdvanced's shouldComponentUpdate to return false if final\n// props have not changed. If false, the selector will always return a new\n// object and shouldComponentUpdate will always return true.\n\nexport default function finalPropsSelectorFactory(dispatch, _ref2) {\n  var initMapStateToProps = _ref2.initMapStateToProps,\n      initMapDispatchToProps = _ref2.initMapDispatchToProps,\n      initMergeProps = _ref2.initMergeProps,\n      options = _objectWithoutPropertiesLoose(_ref2, _excluded);\n\n  var mapStateToProps = initMapStateToProps(dispatch, options);\n  var mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  var mergeProps = initMergeProps(dispatch, options);\n\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, options.displayName);\n  }\n\n  var selectorFactory = options.pure ? pureFinalPropsSelectorFactory : impureFinalPropsSelectorFactory;\n  return selectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}", "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"pure\", \"areStatesEqual\", \"areOwnPropsEqual\", \"areStatePropsEqual\", \"areMergedPropsEqual\"];\nimport connectAdvanced from '../components/connectAdvanced';\nimport shallowEqual from '../utils/shallowEqual';\nimport defaultMapDispatchToPropsFactories from './mapDispatchToProps';\nimport defaultMapStateToPropsFactories from './mapStateToProps';\nimport defaultMergePropsFactories from './mergeProps';\nimport defaultSelectorFactory from './selectorFactory';\n/*\r\n  connect is a facade over connectAdvanced. It turns its args into a compatible\r\n  selectorFactory, which has the signature:\r\n\r\n    (dispatch, options) => (nextState, nextOwnProps) => nextFinalProps\r\n  \r\n  connect passes its args to connectAdvanced as options, which will in turn pass them to\r\n  selectorFactory each time a Connect component instance is instantiated or hot reloaded.\r\n\r\n  selectorFactory returns a final props selector from its mapStateToProps,\r\n  mapStateToPropsFactories, mapDispatchToProps, mapDispatchToPropsFactories, mergeProps,\r\n  mergePropsFactories, and pure args.\r\n\r\n  The resulting final props selector is called by the Connect component instance whenever\r\n  it receives new props or store state.\r\n */\n\nfunction match(arg, factories, name) {\n  for (var i = factories.length - 1; i >= 0; i--) {\n    var result = factories[i](arg);\n    if (result) return result;\n  }\n\n  return function (dispatch, options) {\n    throw new Error(\"Invalid value of type \" + typeof arg + \" for \" + name + \" argument when connecting component \" + options.wrappedComponentName + \".\");\n  };\n}\n\nfunction strictEqual(a, b) {\n  return a === b;\n} // createConnect with default args builds the 'official' connect behavior. Calling it with\n// different options opens up some testing and extensibility scenarios\n\n\nexport function createConnect(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      _ref$connectHOC = _ref.connectHOC,\n      connectHOC = _ref$connectHOC === void 0 ? connectAdvanced : _ref$connectHOC,\n      _ref$mapStateToPropsF = _ref.mapStateToPropsFactories,\n      mapStateToPropsFactories = _ref$mapStateToPropsF === void 0 ? defaultMapStateToPropsFactories : _ref$mapStateToPropsF,\n      _ref$mapDispatchToPro = _ref.mapDispatchToPropsFactories,\n      mapDispatchToPropsFactories = _ref$mapDispatchToPro === void 0 ? defaultMapDispatchToPropsFactories : _ref$mapDispatchToPro,\n      _ref$mergePropsFactor = _ref.mergePropsFactories,\n      mergePropsFactories = _ref$mergePropsFactor === void 0 ? defaultMergePropsFactories : _ref$mergePropsFactor,\n      _ref$selectorFactory = _ref.selectorFactory,\n      selectorFactory = _ref$selectorFactory === void 0 ? defaultSelectorFactory : _ref$selectorFactory;\n\n  return function connect(mapStateToProps, mapDispatchToProps, mergeProps, _ref2) {\n    if (_ref2 === void 0) {\n      _ref2 = {};\n    }\n\n    var _ref3 = _ref2,\n        _ref3$pure = _ref3.pure,\n        pure = _ref3$pure === void 0 ? true : _ref3$pure,\n        _ref3$areStatesEqual = _ref3.areStatesEqual,\n        areStatesEqual = _ref3$areStatesEqual === void 0 ? strictEqual : _ref3$areStatesEqual,\n        _ref3$areOwnPropsEqua = _ref3.areOwnPropsEqual,\n        areOwnPropsEqual = _ref3$areOwnPropsEqua === void 0 ? shallowEqual : _ref3$areOwnPropsEqua,\n        _ref3$areStatePropsEq = _ref3.areStatePropsEqual,\n        areStatePropsEqual = _ref3$areStatePropsEq === void 0 ? shallowEqual : _ref3$areStatePropsEq,\n        _ref3$areMergedPropsE = _ref3.areMergedPropsEqual,\n        areMergedPropsEqual = _ref3$areMergedPropsE === void 0 ? shallowEqual : _ref3$areMergedPropsE,\n        extraOptions = _objectWithoutPropertiesLoose(_ref3, _excluded);\n\n    var initMapStateToProps = match(mapStateToProps, mapStateToPropsFactories, 'mapStateToProps');\n    var initMapDispatchToProps = match(mapDispatchToProps, mapDispatchToPropsFactories, 'mapDispatchToProps');\n    var initMergeProps = match(mergeProps, mergePropsFactories, 'mergeProps');\n    return connectHOC(selectorFactory, _extends({\n      // used in error messages\n      methodName: 'connect',\n      // used to compute Connect's displayName from the wrapped component's displayName.\n      getDisplayName: function getDisplayName(name) {\n        return \"Connect(\" + name + \")\";\n      },\n      // if mapStateToProps is falsy, the Connect component doesn't subscribe to store state changes\n      shouldHandleStateChanges: Boolean(mapStateToProps),\n      // passed through to selectorFactory\n      initMapStateToProps: initMapStateToProps,\n      initMapDispatchToProps: initMapDispatchToProps,\n      initMergeProps: initMergeProps,\n      pure: pure,\n      areStatesEqual: areStatesEqual,\n      areOwnPropsEqual: areOwnPropsEqual,\n      areStatePropsEqual: areStatePropsEqual,\n      areMergedPropsEqual: areMergedPropsEqual\n    }, extraOptions));\n  };\n}\nexport default /*#__PURE__*/createConnect();", "import { useReducer, useRef, useMemo, useContext, useDebugValue } from 'react';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from '../components/Context';\n\nvar refEquality = function refEquality(a, b) {\n  return a === b;\n};\n\nfunction useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub) {\n  var _useReducer = useReducer(function (s) {\n    return s + 1;\n  }, 0),\n      forceRender = _useReducer[1];\n\n  var subscription = useMemo(function () {\n    return createSubscription(store, contextSub);\n  }, [store, contextSub]);\n  var latestSubscriptionCallbackError = useRef();\n  var latestSelector = useRef();\n  var latestStoreState = useRef();\n  var latestSelectedState = useRef();\n  var storeState = store.getState();\n  var selectedState;\n\n  try {\n    if (selector !== latestSelector.current || storeState !== latestStoreState.current || latestSubscriptionCallbackError.current) {\n      var newSelectedState = selector(storeState); // ensure latest selected state is reused so that a custom equality function can result in identical references\n\n      if (latestSelectedState.current === undefined || !equalityFn(newSelectedState, latestSelectedState.current)) {\n        selectedState = newSelectedState;\n      } else {\n        selectedState = latestSelectedState.current;\n      }\n    } else {\n      selectedState = latestSelectedState.current;\n    }\n  } catch (err) {\n    if (latestSubscriptionCallbackError.current) {\n      err.message += \"\\nThe error may be correlated with this previous error:\\n\" + latestSubscriptionCallbackError.current.stack + \"\\n\\n\";\n    }\n\n    throw err;\n  }\n\n  useIsomorphicLayoutEffect(function () {\n    latestSelector.current = selector;\n    latestStoreState.current = storeState;\n    latestSelectedState.current = selectedState;\n    latestSubscriptionCallbackError.current = undefined;\n  });\n  useIsomorphicLayoutEffect(function () {\n    function checkForUpdates() {\n      try {\n        var newStoreState = store.getState(); // Avoid calling selector multiple times if the store's state has not changed\n\n        if (newStoreState === latestStoreState.current) {\n          return;\n        }\n\n        var _newSelectedState = latestSelector.current(newStoreState);\n\n        if (equalityFn(_newSelectedState, latestSelectedState.current)) {\n          return;\n        }\n\n        latestSelectedState.current = _newSelectedState;\n        latestStoreState.current = newStoreState;\n      } catch (err) {\n        // we ignore all errors here, since when the component\n        // is re-rendered, the selectors are called again, and\n        // will throw again, if neither props nor store state\n        // changed\n        latestSubscriptionCallbackError.current = err;\n      }\n\n      forceRender();\n    }\n\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    return function () {\n      return subscription.tryUnsubscribe();\n    };\n  }, [store, subscription]);\n  return selectedState;\n}\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\n\nexport function createSelectorHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useSelector(selector, equalityFn) {\n    if (equalityFn === void 0) {\n      equalityFn = refEquality;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(\"You must pass a selector to useSelector\");\n      }\n\n      if (typeof selector !== 'function') {\n        throw new Error(\"You must pass a function as a selector to useSelector\");\n      }\n\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\"You must pass a function as an equality function to useSelector\");\n      }\n    }\n\n    var _useReduxContext = useReduxContext(),\n        store = _useReduxContext.store,\n        contextSub = _useReduxContext.subscription;\n\n    var selectedState = useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport var useSelector = /*#__PURE__*/createSelectorHook();", "import { useState, useRef, useEffect } from 'react';\n\nfunction areInputsEqual(newInputs, lastInputs) {\n  if (newInputs.length !== lastInputs.length) {\n    return false;\n  }\n\n  for (var i = 0; i < newInputs.length; i++) {\n    if (newInputs[i] !== lastInputs[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction useMemoOne(getResult, inputs) {\n  var initial = useState(function () {\n    return {\n      inputs: inputs,\n      result: getResult()\n    };\n  })[0];\n  var isFirstRun = useRef(true);\n  var committed = useRef(initial);\n  var useCache = isFirstRun.current || Boolean(inputs && committed.current.inputs && areInputsEqual(inputs, committed.current.inputs));\n  var cache = useCache ? committed.current : {\n    inputs: inputs,\n    result: getResult()\n  };\n  useEffect(function () {\n    isFirstRun.current = false;\n    committed.current = cache;\n  }, [cache]);\n  return cache.result;\n}\nfunction useCallbackOne(callback, inputs) {\n  return useMemoOne(function () {\n    return callback;\n  }, inputs);\n}\nvar useMemo = useMemoOne;\nvar useCallback = useCallbackOne;\n\nexport { useCallback, useCallbackOne, useMemo, useMemoOne };\n", "export * from './exports';\nimport { unstable_batchedUpdates as batch } from './utils/reactBatchedUpdates';\nimport { setBatch } from './utils/batch'; // Enable batched updates in our subscriptions for use\n// with standard React renderers (ReactDOM, React Native)\n\nsetBatch(batch);\nexport { batch };", "import invariant from 'tiny-invariant';\n\nvar getRect = function getRect(_ref) {\n  var top = _ref.top,\n      right = _ref.right,\n      bottom = _ref.bottom,\n      left = _ref.left;\n  var width = right - left;\n  var height = bottom - top;\n  var rect = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left,\n    width: width,\n    height: height,\n    x: left,\n    y: top,\n    center: {\n      x: (right + left) / 2,\n      y: (bottom + top) / 2\n    }\n  };\n  return rect;\n};\nvar expand = function expand(target, expandBy) {\n  return {\n    top: target.top - expandBy.top,\n    left: target.left - expandBy.left,\n    bottom: target.bottom + expandBy.bottom,\n    right: target.right + expandBy.right\n  };\n};\nvar shrink = function shrink(target, shrinkBy) {\n  return {\n    top: target.top + shrinkBy.top,\n    left: target.left + shrinkBy.left,\n    bottom: target.bottom - shrinkBy.bottom,\n    right: target.right - shrinkBy.right\n  };\n};\n\nvar shift = function shift(target, shiftBy) {\n  return {\n    top: target.top + shiftBy.y,\n    left: target.left + shiftBy.x,\n    bottom: target.bottom + shiftBy.y,\n    right: target.right + shiftBy.x\n  };\n};\n\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\nvar createBox = function createBox(_ref2) {\n  var borderBox = _ref2.borderBox,\n      _ref2$margin = _ref2.margin,\n      margin = _ref2$margin === void 0 ? noSpacing : _ref2$margin,\n      _ref2$border = _ref2.border,\n      border = _ref2$border === void 0 ? noSpacing : _ref2$border,\n      _ref2$padding = _ref2.padding,\n      padding = _ref2$padding === void 0 ? noSpacing : _ref2$padding;\n  var marginBox = getRect(expand(borderBox, margin));\n  var paddingBox = getRect(shrink(borderBox, border));\n  var contentBox = getRect(shrink(paddingBox, padding));\n  return {\n    marginBox: marginBox,\n    borderBox: getRect(borderBox),\n    paddingBox: paddingBox,\n    contentBox: contentBox,\n    margin: margin,\n    border: border,\n    padding: padding\n  };\n};\n\nvar parse = function parse(raw) {\n  var value = raw.slice(0, -2);\n  var suffix = raw.slice(-2);\n\n  if (suffix !== 'px') {\n    return 0;\n  }\n\n  var result = Number(value);\n  !!isNaN(result) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Could not parse value [raw: \" + raw + \", without suffix: \" + value + \"]\") : invariant(false) : void 0;\n  return result;\n};\n\nvar getWindowScroll = function getWindowScroll() {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n};\n\nvar offset = function offset(original, change) {\n  var borderBox = original.borderBox,\n      border = original.border,\n      margin = original.margin,\n      padding = original.padding;\n  var shifted = shift(borderBox, change);\n  return createBox({\n    borderBox: shifted,\n    border: border,\n    margin: margin,\n    padding: padding\n  });\n};\nvar withScroll = function withScroll(original, scroll) {\n  if (scroll === void 0) {\n    scroll = getWindowScroll();\n  }\n\n  return offset(original, scroll);\n};\nvar calculateBox = function calculateBox(borderBox, styles) {\n  var margin = {\n    top: parse(styles.marginTop),\n    right: parse(styles.marginRight),\n    bottom: parse(styles.marginBottom),\n    left: parse(styles.marginLeft)\n  };\n  var padding = {\n    top: parse(styles.paddingTop),\n    right: parse(styles.paddingRight),\n    bottom: parse(styles.paddingBottom),\n    left: parse(styles.paddingLeft)\n  };\n  var border = {\n    top: parse(styles.borderTopWidth),\n    right: parse(styles.borderRightWidth),\n    bottom: parse(styles.borderBottomWidth),\n    left: parse(styles.borderLeftWidth)\n  };\n  return createBox({\n    borderBox: borderBox,\n    margin: margin,\n    padding: padding,\n    border: border\n  });\n};\nvar getBox = function getBox(el) {\n  var borderBox = el.getBoundingClientRect();\n  var styles = window.getComputedStyle(el);\n  return calculateBox(borderBox, styles);\n};\n\nexport { calculateBox, createBox, expand, getBox, getRect, offset, shrink, withScroll };\n", "var rafSchd = function rafSchd(fn) {\n  var lastArgs = [];\n  var frameId = null;\n\n  var wrapperFn = function wrapperFn() {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    lastArgs = args;\n\n    if (frameId) {\n      return;\n    }\n\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      fn.apply(void 0, lastArgs);\n    });\n  };\n\n  wrapperFn.cancel = function () {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n  };\n\n  return wrapperFn;\n};\n\nexport default rafSchd;\n", "import React, { useLayoutEffect, useEffect, useRef, useState, useContext } from 'react';\nimport _inheritsLoose from '@babel/runtime/helpers/esm/inheritsLoose';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport { createStore as createStore$1, applyMiddleware, compose, bindActionCreators } from 'redux';\nimport { Provider, connect } from 'react-redux';\nimport { useMemo, useCallback } from 'use-memo-one';\nimport { getRect, expand, offset, withScroll, getBox, createBox, calculateBox } from 'css-box-model';\nimport memoizeOne from 'memoize-one';\nimport rafSchd from 'raf-schd';\nimport ReactDOM from 'react-dom';\n\nvar isProduction = process.env.NODE_ENV === 'production';\nvar spacesAndTabs = /[ \\t]{2,}/g;\nvar lineStartWithSpaces = /^[ \\t]*/gm;\n\nvar clean = function clean(value) {\n  return value.replace(spacesAndTabs, ' ').replace(lineStartWithSpaces, '').trim();\n};\n\nvar getDevMessage = function getDevMessage(message) {\n  return clean(\"\\n  %creact-beautiful-dnd\\n\\n  %c\" + clean(message) + \"\\n\\n  %c\\uD83D\\uDC77\\u200D This is a development only message. It will be removed in production builds.\\n\");\n};\n\nvar getFormattedMessage = function getFormattedMessage(message) {\n  return [getDevMessage(message), 'color: #00C584; font-size: 1.2em; font-weight: bold;', 'line-height: 1.5', 'color: #723874;'];\n};\nvar isDisabledFlag = '__react-beautiful-dnd-disable-dev-warnings';\nfunction log(type, message) {\n  var _console;\n\n  if (isProduction) {\n    return;\n  }\n\n  if (typeof window !== 'undefined' && window[isDisabledFlag]) {\n    return;\n  }\n\n  (_console = console)[type].apply(_console, getFormattedMessage(message));\n}\nvar warning = log.bind(null, 'warn');\nvar error = log.bind(null, 'error');\n\nfunction noop() {}\n\nfunction getOptions(shared, fromBinding) {\n  return _extends({}, shared, {}, fromBinding);\n}\n\nfunction bindEvents(el, bindings, sharedOptions) {\n  var unbindings = bindings.map(function (binding) {\n    var options = getOptions(sharedOptions, binding.options);\n    el.addEventListener(binding.eventName, binding.fn, options);\n    return function unbind() {\n      el.removeEventListener(binding.eventName, binding.fn, options);\n    };\n  });\n  return function unbindAll() {\n    unbindings.forEach(function (unbind) {\n      unbind();\n    });\n  };\n}\n\nvar isProduction$1 = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction RbdInvariant(message) {\n  this.message = message;\n}\n\nRbdInvariant.prototype.toString = function toString() {\n  return this.message;\n};\n\nfunction invariant(condition, message) {\n  if (condition) {\n    return;\n  }\n\n  if (isProduction$1) {\n    throw new RbdInvariant(prefix);\n  } else {\n    throw new RbdInvariant(prefix + \": \" + (message || ''));\n  }\n}\n\nvar ErrorBoundary = function (_React$Component) {\n  _inheritsLoose(ErrorBoundary, _React$Component);\n\n  function ErrorBoundary() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _this.callbacks = null;\n    _this.unbind = noop;\n\n    _this.onWindowError = function (event) {\n      var callbacks = _this.getCallbacks();\n\n      if (callbacks.isDragging()) {\n        callbacks.tryAbort();\n        process.env.NODE_ENV !== \"production\" ? warning(\"\\n        An error was caught by our window 'error' event listener while a drag was occurring.\\n        The active drag has been aborted.\\n      \") : void 0;\n      }\n\n      var err = event.error;\n\n      if (err instanceof RbdInvariant) {\n        event.preventDefault();\n\n        if (process.env.NODE_ENV !== 'production') {\n          error(err.message);\n        }\n      }\n    };\n\n    _this.getCallbacks = function () {\n      if (!_this.callbacks) {\n        throw new Error('Unable to find AppCallbacks in <ErrorBoundary/>');\n      }\n\n      return _this.callbacks;\n    };\n\n    _this.setCallbacks = function (callbacks) {\n      _this.callbacks = callbacks;\n    };\n\n    return _this;\n  }\n\n  var _proto = ErrorBoundary.prototype;\n\n  _proto.componentDidMount = function componentDidMount() {\n    this.unbind = bindEvents(window, [{\n      eventName: 'error',\n      fn: this.onWindowError\n    }]);\n  };\n\n  _proto.componentDidCatch = function componentDidCatch(err) {\n    if (err instanceof RbdInvariant) {\n      if (process.env.NODE_ENV !== 'production') {\n        error(err.message);\n      }\n\n      this.setState({});\n      return;\n    }\n\n    throw err;\n  };\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    this.unbind();\n  };\n\n  _proto.render = function render() {\n    return this.props.children(this.setCallbacks);\n  };\n\n  return ErrorBoundary;\n}(React.Component);\n\nvar dragHandleUsageInstructions = \"\\n  Press space bar to start a drag.\\n  When dragging you can use the arrow keys to move the item around and escape to cancel.\\n  Some screen readers may require you to be in focus mode or to use your pass through key\\n\";\n\nvar position = function position(index) {\n  return index + 1;\n};\n\nvar onDragStart = function onDragStart(start) {\n  return \"\\n  You have lifted an item in position \" + position(start.source.index) + \"\\n\";\n};\n\nvar withLocation = function withLocation(source, destination) {\n  var isInHomeList = source.droppableId === destination.droppableId;\n  var startPosition = position(source.index);\n  var endPosition = position(destination.index);\n\n  if (isInHomeList) {\n    return \"\\n      You have moved the item from position \" + startPosition + \"\\n      to position \" + endPosition + \"\\n    \";\n  }\n\n  return \"\\n    You have moved the item from position \" + startPosition + \"\\n    in list \" + source.droppableId + \"\\n    to list \" + destination.droppableId + \"\\n    in position \" + endPosition + \"\\n  \";\n};\n\nvar withCombine = function withCombine(id, source, combine) {\n  var inHomeList = source.droppableId === combine.droppableId;\n\n  if (inHomeList) {\n    return \"\\n      The item \" + id + \"\\n      has been combined with \" + combine.draggableId;\n  }\n\n  return \"\\n      The item \" + id + \"\\n      in list \" + source.droppableId + \"\\n      has been combined with \" + combine.draggableId + \"\\n      in list \" + combine.droppableId + \"\\n    \";\n};\n\nvar onDragUpdate = function onDragUpdate(update) {\n  var location = update.destination;\n\n  if (location) {\n    return withLocation(update.source, location);\n  }\n\n  var combine = update.combine;\n\n  if (combine) {\n    return withCombine(update.draggableId, update.source, combine);\n  }\n\n  return 'You are over an area that cannot be dropped on';\n};\n\nvar returnedToStart = function returnedToStart(source) {\n  return \"\\n  The item has returned to its starting position\\n  of \" + position(source.index) + \"\\n\";\n};\n\nvar onDragEnd = function onDragEnd(result) {\n  if (result.reason === 'CANCEL') {\n    return \"\\n      Movement cancelled.\\n      \" + returnedToStart(result.source) + \"\\n    \";\n  }\n\n  var location = result.destination;\n  var combine = result.combine;\n\n  if (location) {\n    return \"\\n      You have dropped the item.\\n      \" + withLocation(result.source, location) + \"\\n    \";\n  }\n\n  if (combine) {\n    return \"\\n      You have dropped the item.\\n      \" + withCombine(result.draggableId, result.source, combine) + \"\\n    \";\n  }\n\n  return \"\\n    The item has been dropped while not over a drop area.\\n    \" + returnedToStart(result.source) + \"\\n  \";\n};\n\nvar preset = {\n  dragHandleUsageInstructions: dragHandleUsageInstructions,\n  onDragStart: onDragStart,\n  onDragUpdate: onDragUpdate,\n  onDragEnd: onDragEnd\n};\n\nvar origin = {\n  x: 0,\n  y: 0\n};\nvar add = function add(point1, point2) {\n  return {\n    x: point1.x + point2.x,\n    y: point1.y + point2.y\n  };\n};\nvar subtract = function subtract(point1, point2) {\n  return {\n    x: point1.x - point2.x,\n    y: point1.y - point2.y\n  };\n};\nvar isEqual = function isEqual(point1, point2) {\n  return point1.x === point2.x && point1.y === point2.y;\n};\nvar negate = function negate(point) {\n  return {\n    x: point.x !== 0 ? -point.x : 0,\n    y: point.y !== 0 ? -point.y : 0\n  };\n};\nvar patch = function patch(line, value, otherValue) {\n  var _ref;\n\n  if (otherValue === void 0) {\n    otherValue = 0;\n  }\n\n  return _ref = {}, _ref[line] = value, _ref[line === 'x' ? 'y' : 'x'] = otherValue, _ref;\n};\nvar distance = function distance(point1, point2) {\n  return Math.sqrt(Math.pow(point2.x - point1.x, 2) + Math.pow(point2.y - point1.y, 2));\n};\nvar closest = function closest(target, points) {\n  return Math.min.apply(Math, points.map(function (point) {\n    return distance(target, point);\n  }));\n};\nvar apply = function apply(fn) {\n  return function (point) {\n    return {\n      x: fn(point.x),\n      y: fn(point.y)\n    };\n  };\n};\n\nvar executeClip = (function (frame, subject) {\n  var result = getRect({\n    top: Math.max(subject.top, frame.top),\n    right: Math.min(subject.right, frame.right),\n    bottom: Math.min(subject.bottom, frame.bottom),\n    left: Math.max(subject.left, frame.left)\n  });\n\n  if (result.width <= 0 || result.height <= 0) {\n    return null;\n  }\n\n  return result;\n});\n\nvar offsetByPosition = function offsetByPosition(spacing, point) {\n  return {\n    top: spacing.top + point.y,\n    left: spacing.left + point.x,\n    bottom: spacing.bottom + point.y,\n    right: spacing.right + point.x\n  };\n};\nvar getCorners = function getCorners(spacing) {\n  return [{\n    x: spacing.left,\n    y: spacing.top\n  }, {\n    x: spacing.right,\n    y: spacing.top\n  }, {\n    x: spacing.left,\n    y: spacing.bottom\n  }, {\n    x: spacing.right,\n    y: spacing.bottom\n  }];\n};\nvar noSpacing = {\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0\n};\n\nvar scroll = function scroll(target, frame) {\n  if (!frame) {\n    return target;\n  }\n\n  return offsetByPosition(target, frame.scroll.diff.displacement);\n};\n\nvar increase = function increase(target, axis, withPlaceholder) {\n  if (withPlaceholder && withPlaceholder.increasedBy) {\n    var _extends2;\n\n    return _extends({}, target, (_extends2 = {}, _extends2[axis.end] = target[axis.end] + withPlaceholder.increasedBy[axis.line], _extends2));\n  }\n\n  return target;\n};\n\nvar clip = function clip(target, frame) {\n  if (frame && frame.shouldClipSubject) {\n    return executeClip(frame.pageMarginBox, target);\n  }\n\n  return getRect(target);\n};\n\nvar getSubject = (function (_ref) {\n  var page = _ref.page,\n      withPlaceholder = _ref.withPlaceholder,\n      axis = _ref.axis,\n      frame = _ref.frame;\n  var scrolled = scroll(page.marginBox, frame);\n  var increased = increase(scrolled, axis, withPlaceholder);\n  var clipped = clip(increased, frame);\n  return {\n    page: page,\n    withPlaceholder: withPlaceholder,\n    active: clipped\n  };\n});\n\nvar scrollDroppable = (function (droppable, newScroll) {\n  !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var scrollable = droppable.frame;\n  var scrollDiff = subtract(newScroll, scrollable.scroll.initial);\n  var scrollDisplacement = negate(scrollDiff);\n\n  var frame = _extends({}, scrollable, {\n    scroll: {\n      initial: scrollable.scroll.initial,\n      current: newScroll,\n      diff: {\n        value: scrollDiff,\n        displacement: scrollDisplacement\n      },\n      max: scrollable.scroll.max\n    }\n  });\n\n  var subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: droppable.subject.withPlaceholder,\n    axis: droppable.axis,\n    frame: frame\n  });\n\n  var result = _extends({}, droppable, {\n    frame: frame,\n    subject: subject\n  });\n\n  return result;\n});\n\nfunction isInteger(value) {\n  if (Number.isInteger) {\n    return Number.isInteger(value);\n  }\n\n  return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n}\nfunction values(map) {\n  if (Object.values) {\n    return Object.values(map);\n  }\n\n  return Object.keys(map).map(function (key) {\n    return map[key];\n  });\n}\nfunction findIndex(list, predicate) {\n  if (list.findIndex) {\n    return list.findIndex(predicate);\n  }\n\n  for (var i = 0; i < list.length; i++) {\n    if (predicate(list[i])) {\n      return i;\n    }\n  }\n\n  return -1;\n}\nfunction find(list, predicate) {\n  if (list.find) {\n    return list.find(predicate);\n  }\n\n  var index = findIndex(list, predicate);\n\n  if (index !== -1) {\n    return list[index];\n  }\n\n  return undefined;\n}\nfunction toArray(list) {\n  return Array.prototype.slice.call(list);\n}\n\nvar toDroppableMap = memoizeOne(function (droppables) {\n  return droppables.reduce(function (previous, current) {\n    previous[current.descriptor.id] = current;\n    return previous;\n  }, {});\n});\nvar toDraggableMap = memoizeOne(function (draggables) {\n  return draggables.reduce(function (previous, current) {\n    previous[current.descriptor.id] = current;\n    return previous;\n  }, {});\n});\nvar toDroppableList = memoizeOne(function (droppables) {\n  return values(droppables);\n});\nvar toDraggableList = memoizeOne(function (draggables) {\n  return values(draggables);\n});\n\nvar getDraggablesInsideDroppable = memoizeOne(function (droppableId, draggables) {\n  var result = toDraggableList(draggables).filter(function (draggable) {\n    return droppableId === draggable.descriptor.droppableId;\n  }).sort(function (a, b) {\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return result;\n});\n\nfunction tryGetDestination(impact) {\n  if (impact.at && impact.at.type === 'REORDER') {\n    return impact.at.destination;\n  }\n\n  return null;\n}\nfunction tryGetCombine(impact) {\n  if (impact.at && impact.at.type === 'COMBINE') {\n    return impact.at.combine;\n  }\n\n  return null;\n}\n\nvar removeDraggableFromList = memoizeOne(function (remove, list) {\n  return list.filter(function (item) {\n    return item.descriptor.id !== remove.descriptor.id;\n  });\n});\n\nvar moveToNextCombine = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      draggable = _ref.draggable,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      previousImpact = _ref.previousImpact;\n\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n\n  var location = tryGetDestination(previousImpact);\n\n  if (!location) {\n    return null;\n  }\n\n  function getImpact(target) {\n    var at = {\n      type: 'COMBINE',\n      combine: {\n        draggableId: target,\n        droppableId: destination.descriptor.id\n      }\n    };\n    return _extends({}, previousImpact, {\n      at: at\n    });\n  }\n\n  var all = previousImpact.displaced.all;\n  var closestId = all.length ? all[0] : null;\n\n  if (isMovingForward) {\n    return closestId ? getImpact(closestId) : null;\n  }\n\n  var withoutDraggable = removeDraggableFromList(draggable, insideDestination);\n\n  if (!closestId) {\n    if (!withoutDraggable.length) {\n      return null;\n    }\n\n    var last = withoutDraggable[withoutDraggable.length - 1];\n    return getImpact(last.descriptor.id);\n  }\n\n  var indexOfClosest = findIndex(withoutDraggable, function (d) {\n    return d.descriptor.id === closestId;\n  });\n  !(indexOfClosest !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find displaced item in set') : invariant(false) : void 0;\n  var proposedIndex = indexOfClosest - 1;\n\n  if (proposedIndex < 0) {\n    return null;\n  }\n\n  var before = withoutDraggable[proposedIndex];\n  return getImpact(before.descriptor.id);\n});\n\nvar isHomeOf = (function (draggable, destination) {\n  return draggable.descriptor.droppableId === destination.descriptor.id;\n});\n\nvar noDisplacedBy = {\n  point: origin,\n  value: 0\n};\nvar emptyGroups = {\n  invisible: {},\n  visible: {},\n  all: []\n};\nvar noImpact = {\n  displaced: emptyGroups,\n  displacedBy: noDisplacedBy,\n  at: null\n};\n\nvar isWithin = (function (lowerBound, upperBound) {\n  return function (value) {\n    return lowerBound <= value && value <= upperBound;\n  };\n});\n\nvar isPartiallyVisibleThroughFrame = (function (frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function (subject) {\n    var isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n\n    if (isContained) {\n      return true;\n    }\n\n    var isPartiallyVisibleVertically = isWithinVertical(subject.top) || isWithinVertical(subject.bottom);\n    var isPartiallyVisibleHorizontally = isWithinHorizontal(subject.left) || isWithinHorizontal(subject.right);\n    var isPartiallyContained = isPartiallyVisibleVertically && isPartiallyVisibleHorizontally;\n\n    if (isPartiallyContained) {\n      return true;\n    }\n\n    var isBiggerVertically = subject.top < frame.top && subject.bottom > frame.bottom;\n    var isBiggerHorizontally = subject.left < frame.left && subject.right > frame.right;\n    var isTargetBiggerThanFrame = isBiggerVertically && isBiggerHorizontally;\n\n    if (isTargetBiggerThanFrame) {\n      return true;\n    }\n\n    var isTargetBiggerOnOneAxis = isBiggerVertically && isPartiallyVisibleHorizontally || isBiggerHorizontally && isPartiallyVisibleVertically;\n    return isTargetBiggerOnOneAxis;\n  };\n});\n\nvar isTotallyVisibleThroughFrame = (function (frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function (subject) {\n    var isContained = isWithinVertical(subject.top) && isWithinVertical(subject.bottom) && isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    return isContained;\n  };\n});\n\nvar vertical = {\n  direction: 'vertical',\n  line: 'y',\n  crossAxisLine: 'x',\n  start: 'top',\n  end: 'bottom',\n  size: 'height',\n  crossAxisStart: 'left',\n  crossAxisEnd: 'right',\n  crossAxisSize: 'width'\n};\nvar horizontal = {\n  direction: 'horizontal',\n  line: 'x',\n  crossAxisLine: 'y',\n  start: 'left',\n  end: 'right',\n  size: 'width',\n  crossAxisStart: 'top',\n  crossAxisEnd: 'bottom',\n  crossAxisSize: 'height'\n};\n\nvar isTotallyVisibleThroughFrameOnAxis = (function (axis) {\n  return function (frame) {\n    var isWithinVertical = isWithin(frame.top, frame.bottom);\n    var isWithinHorizontal = isWithin(frame.left, frame.right);\n    return function (subject) {\n      if (axis === vertical) {\n        return isWithinVertical(subject.top) && isWithinVertical(subject.bottom);\n      }\n\n      return isWithinHorizontal(subject.left) && isWithinHorizontal(subject.right);\n    };\n  };\n});\n\nvar getDroppableDisplaced = function getDroppableDisplaced(target, destination) {\n  var displacement = destination.frame ? destination.frame.scroll.diff.displacement : origin;\n  return offsetByPosition(target, displacement);\n};\n\nvar isVisibleInDroppable = function isVisibleInDroppable(target, destination, isVisibleThroughFrameFn) {\n  if (!destination.subject.active) {\n    return false;\n  }\n\n  return isVisibleThroughFrameFn(destination.subject.active)(target);\n};\n\nvar isVisibleInViewport = function isVisibleInViewport(target, viewport, isVisibleThroughFrameFn) {\n  return isVisibleThroughFrameFn(viewport)(target);\n};\n\nvar isVisible = function isVisible(_ref) {\n  var toBeDisplaced = _ref.target,\n      destination = _ref.destination,\n      viewport = _ref.viewport,\n      withDroppableDisplacement = _ref.withDroppableDisplacement,\n      isVisibleThroughFrameFn = _ref.isVisibleThroughFrameFn;\n  var displacedTarget = withDroppableDisplacement ? getDroppableDisplaced(toBeDisplaced, destination) : toBeDisplaced;\n  return isVisibleInDroppable(displacedTarget, destination, isVisibleThroughFrameFn) && isVisibleInViewport(displacedTarget, viewport, isVisibleThroughFrameFn);\n};\n\nvar isPartiallyVisible = function isPartiallyVisible(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isPartiallyVisibleThroughFrame\n  }));\n};\nvar isTotallyVisible = function isTotallyVisible(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isTotallyVisibleThroughFrame\n  }));\n};\nvar isTotallyVisibleOnAxis = function isTotallyVisibleOnAxis(args) {\n  return isVisible(_extends({}, args, {\n    isVisibleThroughFrameFn: isTotallyVisibleThroughFrameOnAxis(args.destination.axis)\n  }));\n};\n\nvar getShouldAnimate = function getShouldAnimate(id, last, forceShouldAnimate) {\n  if (typeof forceShouldAnimate === 'boolean') {\n    return forceShouldAnimate;\n  }\n\n  if (!last) {\n    return true;\n  }\n\n  var invisible = last.invisible,\n      visible = last.visible;\n\n  if (invisible[id]) {\n    return false;\n  }\n\n  var previous = visible[id];\n  return previous ? previous.shouldAnimate : true;\n};\n\nfunction getTarget(draggable, displacedBy) {\n  var marginBox = draggable.page.marginBox;\n  var expandBy = {\n    top: displacedBy.point.y,\n    right: 0,\n    bottom: 0,\n    left: displacedBy.point.x\n  };\n  return getRect(expand(marginBox, expandBy));\n}\n\nfunction getDisplacementGroups(_ref) {\n  var afterDragging = _ref.afterDragging,\n      destination = _ref.destination,\n      displacedBy = _ref.displacedBy,\n      viewport = _ref.viewport,\n      forceShouldAnimate = _ref.forceShouldAnimate,\n      last = _ref.last;\n  return afterDragging.reduce(function process(groups, draggable) {\n    var target = getTarget(draggable, displacedBy);\n    var id = draggable.descriptor.id;\n    groups.all.push(id);\n    var isVisible = isPartiallyVisible({\n      target: target,\n      destination: destination,\n      viewport: viewport,\n      withDroppableDisplacement: true\n    });\n\n    if (!isVisible) {\n      groups.invisible[draggable.descriptor.id] = true;\n      return groups;\n    }\n\n    var shouldAnimate = getShouldAnimate(id, last, forceShouldAnimate);\n    var displacement = {\n      draggableId: id,\n      shouldAnimate: shouldAnimate\n    };\n    groups.visible[id] = displacement;\n    return groups;\n  }, {\n    all: [],\n    visible: {},\n    invisible: {}\n  });\n}\n\nfunction getIndexOfLastItem(draggables, options) {\n  if (!draggables.length) {\n    return 0;\n  }\n\n  var indexOfLastItem = draggables[draggables.length - 1].descriptor.index;\n  return options.inHomeList ? indexOfLastItem : indexOfLastItem + 1;\n}\n\nfunction goAtEnd(_ref) {\n  var insideDestination = _ref.insideDestination,\n      inHomeList = _ref.inHomeList,\n      displacedBy = _ref.displacedBy,\n      destination = _ref.destination;\n  var newIndex = getIndexOfLastItem(insideDestination, {\n    inHomeList: inHomeList\n  });\n  return {\n    displaced: emptyGroups,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: newIndex\n      }\n    }\n  };\n}\n\nfunction calculateReorderImpact(_ref2) {\n  var draggable = _ref2.draggable,\n      insideDestination = _ref2.insideDestination,\n      destination = _ref2.destination,\n      viewport = _ref2.viewport,\n      displacedBy = _ref2.displacedBy,\n      last = _ref2.last,\n      index = _ref2.index,\n      forceShouldAnimate = _ref2.forceShouldAnimate;\n  var inHomeList = isHomeOf(draggable, destination);\n\n  if (index == null) {\n    return goAtEnd({\n      insideDestination: insideDestination,\n      inHomeList: inHomeList,\n      displacedBy: displacedBy,\n      destination: destination\n    });\n  }\n\n  var match = find(insideDestination, function (item) {\n    return item.descriptor.index === index;\n  });\n\n  if (!match) {\n    return goAtEnd({\n      insideDestination: insideDestination,\n      inHomeList: inHomeList,\n      displacedBy: displacedBy,\n      destination: destination\n    });\n  }\n\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var sliceFrom = insideDestination.indexOf(match);\n  var impacted = withoutDragging.slice(sliceFrom);\n  var displaced = getDisplacementGroups({\n    afterDragging: impacted,\n    destination: destination,\n    displacedBy: displacedBy,\n    last: last,\n    viewport: viewport.frame,\n    forceShouldAnimate: forceShouldAnimate\n  });\n  return {\n    displaced: displaced,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: {\n        droppableId: destination.descriptor.id,\n        index: index\n      }\n    }\n  };\n}\n\nfunction didStartAfterCritical(draggableId, afterCritical) {\n  return Boolean(afterCritical.effected[draggableId]);\n}\n\nvar fromCombine = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      destination = _ref.destination,\n      draggables = _ref.draggables,\n      combine = _ref.combine,\n      afterCritical = _ref.afterCritical;\n\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n\n  var combineId = combine.draggableId;\n  var combineWith = draggables[combineId];\n  var combineWithIndex = combineWith.descriptor.index;\n  var didCombineWithStartAfterCritical = didStartAfterCritical(combineId, afterCritical);\n\n  if (didCombineWithStartAfterCritical) {\n    if (isMovingForward) {\n      return combineWithIndex;\n    }\n\n    return combineWithIndex - 1;\n  }\n\n  if (isMovingForward) {\n    return combineWithIndex + 1;\n  }\n\n  return combineWithIndex;\n});\n\nvar fromReorder = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      isInHomeList = _ref.isInHomeList,\n      insideDestination = _ref.insideDestination,\n      location = _ref.location;\n\n  if (!insideDestination.length) {\n    return null;\n  }\n\n  var currentIndex = location.index;\n  var proposedIndex = isMovingForward ? currentIndex + 1 : currentIndex - 1;\n  var firstIndex = insideDestination[0].descriptor.index;\n  var lastIndex = insideDestination[insideDestination.length - 1].descriptor.index;\n  var upperBound = isInHomeList ? lastIndex : lastIndex + 1;\n\n  if (proposedIndex < firstIndex) {\n    return null;\n  }\n\n  if (proposedIndex > upperBound) {\n    return null;\n  }\n\n  return proposedIndex;\n});\n\nvar moveToNextIndex = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      isInHomeList = _ref.isInHomeList,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      previousImpact = _ref.previousImpact,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var wasAt = previousImpact.at;\n  !wasAt ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot move in direction without previous impact location') : invariant(false) : void 0;\n\n  if (wasAt.type === 'REORDER') {\n    var _newIndex = fromReorder({\n      isMovingForward: isMovingForward,\n      isInHomeList: isInHomeList,\n      location: wasAt.destination,\n      insideDestination: insideDestination\n    });\n\n    if (_newIndex == null) {\n      return null;\n    }\n\n    return calculateReorderImpact({\n      draggable: draggable,\n      insideDestination: insideDestination,\n      destination: destination,\n      viewport: viewport,\n      last: previousImpact.displaced,\n      displacedBy: previousImpact.displacedBy,\n      index: _newIndex\n    });\n  }\n\n  var newIndex = fromCombine({\n    isMovingForward: isMovingForward,\n    destination: destination,\n    displaced: previousImpact.displaced,\n    draggables: draggables,\n    combine: wasAt.combine,\n    afterCritical: afterCritical\n  });\n\n  if (newIndex == null) {\n    return null;\n  }\n\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    last: previousImpact.displaced,\n    displacedBy: previousImpact.displacedBy,\n    index: newIndex\n  });\n});\n\nvar getCombinedItemDisplacement = (function (_ref) {\n  var displaced = _ref.displaced,\n      afterCritical = _ref.afterCritical,\n      combineWith = _ref.combineWith,\n      displacedBy = _ref.displacedBy;\n  var isDisplaced = Boolean(displaced.visible[combineWith] || displaced.invisible[combineWith]);\n\n  if (didStartAfterCritical(combineWith, afterCritical)) {\n    return isDisplaced ? origin : negate(displacedBy.point);\n  }\n\n  return isDisplaced ? displacedBy.point : origin;\n});\n\nvar whenCombining = (function (_ref) {\n  var afterCritical = _ref.afterCritical,\n      impact = _ref.impact,\n      draggables = _ref.draggables;\n  var combine = tryGetCombine(impact);\n  !combine ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var combineWith = combine.draggableId;\n  var center = draggables[combineWith].page.borderBox.center;\n  var displaceBy = getCombinedItemDisplacement({\n    displaced: impact.displaced,\n    afterCritical: afterCritical,\n    combineWith: combineWith,\n    displacedBy: impact.displacedBy\n  });\n  return add(center, displaceBy);\n});\n\nvar distanceFromStartToBorderBoxCenter = function distanceFromStartToBorderBoxCenter(axis, box) {\n  return box.margin[axis.start] + box.borderBox[axis.size] / 2;\n};\n\nvar distanceFromEndToBorderBoxCenter = function distanceFromEndToBorderBoxCenter(axis, box) {\n  return box.margin[axis.end] + box.borderBox[axis.size] / 2;\n};\n\nvar getCrossAxisBorderBoxCenter = function getCrossAxisBorderBoxCenter(axis, target, isMoving) {\n  return target[axis.crossAxisStart] + isMoving.margin[axis.crossAxisStart] + isMoving.borderBox[axis.crossAxisSize] / 2;\n};\n\nvar goAfter = function goAfter(_ref) {\n  var axis = _ref.axis,\n      moveRelativeTo = _ref.moveRelativeTo,\n      isMoving = _ref.isMoving;\n  return patch(axis.line, moveRelativeTo.marginBox[axis.end] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\n};\nvar goBefore = function goBefore(_ref2) {\n  var axis = _ref2.axis,\n      moveRelativeTo = _ref2.moveRelativeTo,\n      isMoving = _ref2.isMoving;\n  return patch(axis.line, moveRelativeTo.marginBox[axis.start] - distanceFromEndToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveRelativeTo.marginBox, isMoving));\n};\nvar goIntoStart = function goIntoStart(_ref3) {\n  var axis = _ref3.axis,\n      moveInto = _ref3.moveInto,\n      isMoving = _ref3.isMoving;\n  return patch(axis.line, moveInto.contentBox[axis.start] + distanceFromStartToBorderBoxCenter(axis, isMoving), getCrossAxisBorderBoxCenter(axis, moveInto.contentBox, isMoving));\n};\n\nvar whenReordering = (function (_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      droppable = _ref.droppable,\n      afterCritical = _ref.afterCritical;\n  var insideDestination = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  var draggablePage = draggable.page;\n  var axis = droppable.axis;\n\n  if (!insideDestination.length) {\n    return goIntoStart({\n      axis: axis,\n      moveInto: droppable.page,\n      isMoving: draggablePage\n    });\n  }\n\n  var displaced = impact.displaced,\n      displacedBy = impact.displacedBy;\n  var closestAfter = displaced.all[0];\n\n  if (closestAfter) {\n    var closest = draggables[closestAfter];\n\n    if (didStartAfterCritical(closestAfter, afterCritical)) {\n      return goBefore({\n        axis: axis,\n        moveRelativeTo: closest.page,\n        isMoving: draggablePage\n      });\n    }\n\n    var withDisplacement = offset(closest.page, displacedBy.point);\n    return goBefore({\n      axis: axis,\n      moveRelativeTo: withDisplacement,\n      isMoving: draggablePage\n    });\n  }\n\n  var last = insideDestination[insideDestination.length - 1];\n\n  if (last.descriptor.id === draggable.descriptor.id) {\n    return draggablePage.borderBox.center;\n  }\n\n  if (didStartAfterCritical(last.descriptor.id, afterCritical)) {\n    var page = offset(last.page, negate(afterCritical.displacedBy.point));\n    return goAfter({\n      axis: axis,\n      moveRelativeTo: page,\n      isMoving: draggablePage\n    });\n  }\n\n  return goAfter({\n    axis: axis,\n    moveRelativeTo: last.page,\n    isMoving: draggablePage\n  });\n});\n\nvar withDroppableDisplacement = (function (droppable, point) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return point;\n  }\n\n  return add(point, frame.scroll.diff.displacement);\n});\n\nvar getResultWithoutDroppableDisplacement = function getResultWithoutDroppableDisplacement(_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      droppable = _ref.droppable,\n      draggables = _ref.draggables,\n      afterCritical = _ref.afterCritical;\n  var original = draggable.page.borderBox.center;\n  var at = impact.at;\n\n  if (!droppable) {\n    return original;\n  }\n\n  if (!at) {\n    return original;\n  }\n\n  if (at.type === 'REORDER') {\n    return whenReordering({\n      impact: impact,\n      draggable: draggable,\n      draggables: draggables,\n      droppable: droppable,\n      afterCritical: afterCritical\n    });\n  }\n\n  return whenCombining({\n    impact: impact,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n};\n\nvar getPageBorderBoxCenterFromImpact = (function (args) {\n  var withoutDisplacement = getResultWithoutDroppableDisplacement(args);\n  var droppable = args.droppable;\n  var withDisplacement = droppable ? withDroppableDisplacement(droppable, withoutDisplacement) : withoutDisplacement;\n  return withDisplacement;\n});\n\nvar scrollViewport = (function (viewport, newScroll) {\n  var diff = subtract(newScroll, viewport.scroll.initial);\n  var displacement = negate(diff);\n  var frame = getRect({\n    top: newScroll.y,\n    bottom: newScroll.y + viewport.frame.height,\n    left: newScroll.x,\n    right: newScroll.x + viewport.frame.width\n  });\n  var updated = {\n    frame: frame,\n    scroll: {\n      initial: viewport.scroll.initial,\n      max: viewport.scroll.max,\n      current: newScroll,\n      diff: {\n        value: diff,\n        displacement: displacement\n      }\n    }\n  };\n  return updated;\n});\n\nfunction getDraggables(ids, draggables) {\n  return ids.map(function (id) {\n    return draggables[id];\n  });\n}\n\nfunction tryGetVisible(id, groups) {\n  for (var i = 0; i < groups.length; i++) {\n    var displacement = groups[i].visible[id];\n\n    if (displacement) {\n      return displacement;\n    }\n  }\n\n  return null;\n}\n\nvar speculativelyIncrease = (function (_ref) {\n  var impact = _ref.impact,\n      viewport = _ref.viewport,\n      destination = _ref.destination,\n      draggables = _ref.draggables,\n      maxScrollChange = _ref.maxScrollChange;\n  var scrolledViewport = scrollViewport(viewport, add(viewport.scroll.current, maxScrollChange));\n  var scrolledDroppable = destination.frame ? scrollDroppable(destination, add(destination.frame.scroll.current, maxScrollChange)) : destination;\n  var last = impact.displaced;\n  var withViewportScroll = getDisplacementGroups({\n    afterDragging: getDraggables(last.all, draggables),\n    destination: destination,\n    displacedBy: impact.displacedBy,\n    viewport: scrolledViewport.frame,\n    last: last,\n    forceShouldAnimate: false\n  });\n  var withDroppableScroll = getDisplacementGroups({\n    afterDragging: getDraggables(last.all, draggables),\n    destination: scrolledDroppable,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    last: last,\n    forceShouldAnimate: false\n  });\n  var invisible = {};\n  var visible = {};\n  var groups = [last, withViewportScroll, withDroppableScroll];\n  last.all.forEach(function (id) {\n    var displacement = tryGetVisible(id, groups);\n\n    if (displacement) {\n      visible[id] = displacement;\n      return;\n    }\n\n    invisible[id] = true;\n  });\n\n  var newImpact = _extends({}, impact, {\n    displaced: {\n      all: last.all,\n      invisible: invisible,\n      visible: visible\n    }\n  });\n\n  return newImpact;\n});\n\nvar withViewportDisplacement = (function (viewport, point) {\n  return add(viewport.scroll.diff.displacement, point);\n});\n\nvar getClientFromPageBorderBoxCenter = (function (_ref) {\n  var pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n      draggable = _ref.draggable,\n      viewport = _ref.viewport;\n  var withoutPageScrollChange = withViewportDisplacement(viewport, pageBorderBoxCenter);\n  var offset = subtract(withoutPageScrollChange, draggable.page.borderBox.center);\n  return add(draggable.client.borderBox.center, offset);\n});\n\nvar isTotallyVisibleInNewLocation = (function (_ref) {\n  var draggable = _ref.draggable,\n      destination = _ref.destination,\n      newPageBorderBoxCenter = _ref.newPageBorderBoxCenter,\n      viewport = _ref.viewport,\n      withDroppableDisplacement = _ref.withDroppableDisplacement,\n      _ref$onlyOnMainAxis = _ref.onlyOnMainAxis,\n      onlyOnMainAxis = _ref$onlyOnMainAxis === void 0 ? false : _ref$onlyOnMainAxis;\n  var changeNeeded = subtract(newPageBorderBoxCenter, draggable.page.borderBox.center);\n  var shifted = offsetByPosition(draggable.page.borderBox, changeNeeded);\n  var args = {\n    target: shifted,\n    destination: destination,\n    withDroppableDisplacement: withDroppableDisplacement,\n    viewport: viewport\n  };\n  return onlyOnMainAxis ? isTotallyVisibleOnAxis(args) : isTotallyVisible(args);\n});\n\nvar moveToNextPlace = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      draggable = _ref.draggable,\n      destination = _ref.destination,\n      draggables = _ref.draggables,\n      previousImpact = _ref.previousImpact,\n      viewport = _ref.viewport,\n      previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n      previousClientSelection = _ref.previousClientSelection,\n      afterCritical = _ref.afterCritical;\n\n  if (!destination.isEnabled) {\n    return null;\n  }\n\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var isInHomeList = isHomeOf(draggable, destination);\n  var impact = moveToNextCombine({\n    isMovingForward: isMovingForward,\n    draggable: draggable,\n    destination: destination,\n    insideDestination: insideDestination,\n    previousImpact: previousImpact\n  }) || moveToNextIndex({\n    isMovingForward: isMovingForward,\n    isInHomeList: isInHomeList,\n    draggable: draggable,\n    draggables: draggables,\n    destination: destination,\n    insideDestination: insideDestination,\n    previousImpact: previousImpact,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n\n  if (!impact) {\n    return null;\n  }\n\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n  var isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n    draggable: draggable,\n    destination: destination,\n    newPageBorderBoxCenter: pageBorderBoxCenter,\n    viewport: viewport.frame,\n    withDroppableDisplacement: false,\n    onlyOnMainAxis: true\n  });\n\n  if (isVisibleInNewLocation) {\n    var clientSelection = getClientFromPageBorderBoxCenter({\n      pageBorderBoxCenter: pageBorderBoxCenter,\n      draggable: draggable,\n      viewport: viewport\n    });\n    return {\n      clientSelection: clientSelection,\n      impact: impact,\n      scrollJumpRequest: null\n    };\n  }\n\n  var distance = subtract(pageBorderBoxCenter, previousPageBorderBoxCenter);\n  var cautious = speculativelyIncrease({\n    impact: impact,\n    viewport: viewport,\n    destination: destination,\n    draggables: draggables,\n    maxScrollChange: distance\n  });\n  return {\n    clientSelection: previousClientSelection,\n    impact: cautious,\n    scrollJumpRequest: distance\n  };\n});\n\nvar getKnownActive = function getKnownActive(droppable) {\n  var rect = droppable.subject.active;\n  !rect ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get clipped area from droppable') : invariant(false) : void 0;\n  return rect;\n};\n\nvar getBestCrossAxisDroppable = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n      source = _ref.source,\n      droppables = _ref.droppables,\n      viewport = _ref.viewport;\n  var active = source.subject.active;\n\n  if (!active) {\n    return null;\n  }\n\n  var axis = source.axis;\n  var isBetweenSourceClipped = isWithin(active[axis.start], active[axis.end]);\n  var candidates = toDroppableList(droppables).filter(function (droppable) {\n    return droppable !== source;\n  }).filter(function (droppable) {\n    return droppable.isEnabled;\n  }).filter(function (droppable) {\n    return Boolean(droppable.subject.active);\n  }).filter(function (droppable) {\n    return isPartiallyVisibleThroughFrame(viewport.frame)(getKnownActive(droppable));\n  }).filter(function (droppable) {\n    var activeOfTarget = getKnownActive(droppable);\n\n    if (isMovingForward) {\n      return active[axis.crossAxisEnd] < activeOfTarget[axis.crossAxisEnd];\n    }\n\n    return activeOfTarget[axis.crossAxisStart] < active[axis.crossAxisStart];\n  }).filter(function (droppable) {\n    var activeOfTarget = getKnownActive(droppable);\n    var isBetweenDestinationClipped = isWithin(activeOfTarget[axis.start], activeOfTarget[axis.end]);\n    return isBetweenSourceClipped(activeOfTarget[axis.start]) || isBetweenSourceClipped(activeOfTarget[axis.end]) || isBetweenDestinationClipped(active[axis.start]) || isBetweenDestinationClipped(active[axis.end]);\n  }).sort(function (a, b) {\n    var first = getKnownActive(a)[axis.crossAxisStart];\n    var second = getKnownActive(b)[axis.crossAxisStart];\n\n    if (isMovingForward) {\n      return first - second;\n    }\n\n    return second - first;\n  }).filter(function (droppable, index, array) {\n    return getKnownActive(droppable)[axis.crossAxisStart] === getKnownActive(array[0])[axis.crossAxisStart];\n  });\n\n  if (!candidates.length) {\n    return null;\n  }\n\n  if (candidates.length === 1) {\n    return candidates[0];\n  }\n\n  var contains = candidates.filter(function (droppable) {\n    var isWithinDroppable = isWithin(getKnownActive(droppable)[axis.start], getKnownActive(droppable)[axis.end]);\n    return isWithinDroppable(pageBorderBoxCenter[axis.line]);\n  });\n\n  if (contains.length === 1) {\n    return contains[0];\n  }\n\n  if (contains.length > 1) {\n    return contains.sort(function (a, b) {\n      return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n    })[0];\n  }\n\n  return candidates.sort(function (a, b) {\n    var first = closest(pageBorderBoxCenter, getCorners(getKnownActive(a)));\n    var second = closest(pageBorderBoxCenter, getCorners(getKnownActive(b)));\n\n    if (first !== second) {\n      return first - second;\n    }\n\n    return getKnownActive(a)[axis.start] - getKnownActive(b)[axis.start];\n  })[0];\n});\n\nvar getCurrentPageBorderBoxCenter = function getCurrentPageBorderBoxCenter(draggable, afterCritical) {\n  var original = draggable.page.borderBox.center;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? subtract(original, afterCritical.displacedBy.point) : original;\n};\nvar getCurrentPageBorderBox = function getCurrentPageBorderBox(draggable, afterCritical) {\n  var original = draggable.page.borderBox;\n  return didStartAfterCritical(draggable.descriptor.id, afterCritical) ? offsetByPosition(original, negate(afterCritical.displacedBy.point)) : original;\n};\n\nvar getClosestDraggable = (function (_ref) {\n  var pageBorderBoxCenter = _ref.pageBorderBoxCenter,\n      viewport = _ref.viewport,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      afterCritical = _ref.afterCritical;\n  var sorted = insideDestination.filter(function (draggable) {\n    return isTotallyVisible({\n      target: getCurrentPageBorderBox(draggable, afterCritical),\n      destination: destination,\n      viewport: viewport.frame,\n      withDroppableDisplacement: true\n    });\n  }).sort(function (a, b) {\n    var distanceToA = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(a, afterCritical)));\n    var distanceToB = distance(pageBorderBoxCenter, withDroppableDisplacement(destination, getCurrentPageBorderBoxCenter(b, afterCritical)));\n\n    if (distanceToA < distanceToB) {\n      return -1;\n    }\n\n    if (distanceToB < distanceToA) {\n      return 1;\n    }\n\n    return a.descriptor.index - b.descriptor.index;\n  });\n  return sorted[0] || null;\n});\n\nvar getDisplacedBy = memoizeOne(function getDisplacedBy(axis, displaceBy) {\n  var displacement = displaceBy[axis.line];\n  return {\n    value: displacement,\n    point: patch(axis.line, displacement)\n  };\n});\n\nvar getRequiredGrowthForPlaceholder = function getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables) {\n  var axis = droppable.axis;\n\n  if (droppable.descriptor.mode === 'virtual') {\n    return patch(axis.line, placeholderSize[axis.line]);\n  }\n\n  var availableSpace = droppable.subject.page.contentBox[axis.size];\n  var insideDroppable = getDraggablesInsideDroppable(droppable.descriptor.id, draggables);\n  var spaceUsed = insideDroppable.reduce(function (sum, dimension) {\n    return sum + dimension.client.marginBox[axis.size];\n  }, 0);\n  var requiredSpace = spaceUsed + placeholderSize[axis.line];\n  var needsToGrowBy = requiredSpace - availableSpace;\n\n  if (needsToGrowBy <= 0) {\n    return null;\n  }\n\n  return patch(axis.line, needsToGrowBy);\n};\n\nvar withMaxScroll = function withMaxScroll(frame, max) {\n  return _extends({}, frame, {\n    scroll: _extends({}, frame.scroll, {\n      max: max\n    })\n  });\n};\n\nvar addPlaceholder = function addPlaceholder(droppable, draggable, draggables) {\n  var frame = droppable.frame;\n  !!isHomeOf(draggable, droppable) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should not add placeholder space to home list') : invariant(false) : void 0;\n  !!droppable.subject.withPlaceholder ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot add placeholder size to a subject when it already has one') : invariant(false) : void 0;\n  var placeholderSize = getDisplacedBy(droppable.axis, draggable.displaceBy).point;\n  var requiredGrowth = getRequiredGrowthForPlaceholder(droppable, placeholderSize, draggables);\n  var added = {\n    placeholderSize: placeholderSize,\n    increasedBy: requiredGrowth,\n    oldFrameMaxScroll: droppable.frame ? droppable.frame.scroll.max : null\n  };\n\n  if (!frame) {\n    var _subject = getSubject({\n      page: droppable.subject.page,\n      withPlaceholder: added,\n      axis: droppable.axis,\n      frame: droppable.frame\n    });\n\n    return _extends({}, droppable, {\n      subject: _subject\n    });\n  }\n\n  var maxScroll = requiredGrowth ? add(frame.scroll.max, requiredGrowth) : frame.scroll.max;\n  var newFrame = withMaxScroll(frame, maxScroll);\n  var subject = getSubject({\n    page: droppable.subject.page,\n    withPlaceholder: added,\n    axis: droppable.axis,\n    frame: newFrame\n  });\n  return _extends({}, droppable, {\n    subject: subject,\n    frame: newFrame\n  });\n};\nvar removePlaceholder = function removePlaceholder(droppable) {\n  var added = droppable.subject.withPlaceholder;\n  !added ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot remove placeholder form subject when there was none') : invariant(false) : void 0;\n  var frame = droppable.frame;\n\n  if (!frame) {\n    var _subject2 = getSubject({\n      page: droppable.subject.page,\n      axis: droppable.axis,\n      frame: null,\n      withPlaceholder: null\n    });\n\n    return _extends({}, droppable, {\n      subject: _subject2\n    });\n  }\n\n  var oldMaxScroll = added.oldFrameMaxScroll;\n  !oldMaxScroll ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected droppable with frame to have old max frame scroll when removing placeholder') : invariant(false) : void 0;\n  var newFrame = withMaxScroll(frame, oldMaxScroll);\n  var subject = getSubject({\n    page: droppable.subject.page,\n    axis: droppable.axis,\n    frame: newFrame,\n    withPlaceholder: null\n  });\n  return _extends({}, droppable, {\n    subject: subject,\n    frame: newFrame\n  });\n};\n\nvar moveToNewDroppable = (function (_ref) {\n  var previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n      moveRelativeTo = _ref.moveRelativeTo,\n      insideDestination = _ref.insideDestination,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      destination = _ref.destination,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n\n  if (!moveRelativeTo) {\n    if (insideDestination.length) {\n      return null;\n    }\n\n    var proposed = {\n      displaced: emptyGroups,\n      displacedBy: noDisplacedBy,\n      at: {\n        type: 'REORDER',\n        destination: {\n          droppableId: destination.descriptor.id,\n          index: 0\n        }\n      }\n    };\n    var proposedPageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n      impact: proposed,\n      draggable: draggable,\n      droppable: destination,\n      draggables: draggables,\n      afterCritical: afterCritical\n    });\n    var withPlaceholder = isHomeOf(draggable, destination) ? destination : addPlaceholder(destination, draggable, draggables);\n    var isVisibleInNewLocation = isTotallyVisibleInNewLocation({\n      draggable: draggable,\n      destination: withPlaceholder,\n      newPageBorderBoxCenter: proposedPageBorderBoxCenter,\n      viewport: viewport.frame,\n      withDroppableDisplacement: false,\n      onlyOnMainAxis: true\n    });\n    return isVisibleInNewLocation ? proposed : null;\n  }\n\n  var isGoingBeforeTarget = Boolean(previousPageBorderBoxCenter[destination.axis.line] <= moveRelativeTo.page.borderBox.center[destination.axis.line]);\n\n  var proposedIndex = function () {\n    var relativeTo = moveRelativeTo.descriptor.index;\n\n    if (moveRelativeTo.descriptor.id === draggable.descriptor.id) {\n      return relativeTo;\n    }\n\n    if (isGoingBeforeTarget) {\n      return relativeTo;\n    }\n\n    return relativeTo + 1;\n  }();\n\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    displacedBy: displacedBy,\n    last: emptyGroups,\n    index: proposedIndex\n  });\n});\n\nvar moveCrossAxis = (function (_ref) {\n  var isMovingForward = _ref.isMovingForward,\n      previousPageBorderBoxCenter = _ref.previousPageBorderBoxCenter,\n      draggable = _ref.draggable,\n      isOver = _ref.isOver,\n      draggables = _ref.draggables,\n      droppables = _ref.droppables,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var destination = getBestCrossAxisDroppable({\n    isMovingForward: isMovingForward,\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    source: isOver,\n    droppables: droppables,\n    viewport: viewport\n  });\n\n  if (!destination) {\n    return null;\n  }\n\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var moveRelativeTo = getClosestDraggable({\n    pageBorderBoxCenter: previousPageBorderBoxCenter,\n    viewport: viewport,\n    destination: destination,\n    insideDestination: insideDestination,\n    afterCritical: afterCritical\n  });\n  var impact = moveToNewDroppable({\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    destination: destination,\n    draggable: draggable,\n    draggables: draggables,\n    moveRelativeTo: moveRelativeTo,\n    insideDestination: insideDestination,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n\n  if (!impact) {\n    return null;\n  }\n\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    afterCritical: afterCritical\n  });\n  var clientSelection = getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter: pageBorderBoxCenter,\n    draggable: draggable,\n    viewport: viewport\n  });\n  return {\n    clientSelection: clientSelection,\n    impact: impact,\n    scrollJumpRequest: null\n  };\n});\n\nvar whatIsDraggedOver = (function (impact) {\n  var at = impact.at;\n\n  if (!at) {\n    return null;\n  }\n\n  if (at.type === 'REORDER') {\n    return at.destination.droppableId;\n  }\n\n  return at.combine.droppableId;\n});\n\nvar getDroppableOver = function getDroppableOver(impact, droppables) {\n  var id = whatIsDraggedOver(impact);\n  return id ? droppables[id] : null;\n};\n\nvar moveInDirection = (function (_ref) {\n  var state = _ref.state,\n      type = _ref.type;\n  var isActuallyOver = getDroppableOver(state.impact, state.dimensions.droppables);\n  var isMainAxisMovementAllowed = Boolean(isActuallyOver);\n  var home = state.dimensions.droppables[state.critical.droppable.id];\n  var isOver = isActuallyOver || home;\n  var direction = isOver.axis.direction;\n  var isMovingOnMainAxis = direction === 'vertical' && (type === 'MOVE_UP' || type === 'MOVE_DOWN') || direction === 'horizontal' && (type === 'MOVE_LEFT' || type === 'MOVE_RIGHT');\n\n  if (isMovingOnMainAxis && !isMainAxisMovementAllowed) {\n    return null;\n  }\n\n  var isMovingForward = type === 'MOVE_DOWN' || type === 'MOVE_RIGHT';\n  var draggable = state.dimensions.draggables[state.critical.draggable.id];\n  var previousPageBorderBoxCenter = state.current.page.borderBoxCenter;\n  var _state$dimensions = state.dimensions,\n      draggables = _state$dimensions.draggables,\n      droppables = _state$dimensions.droppables;\n  return isMovingOnMainAxis ? moveToNextPlace({\n    isMovingForward: isMovingForward,\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    draggable: draggable,\n    destination: isOver,\n    draggables: draggables,\n    viewport: state.viewport,\n    previousClientSelection: state.current.client.selection,\n    previousImpact: state.impact,\n    afterCritical: state.afterCritical\n  }) : moveCrossAxis({\n    isMovingForward: isMovingForward,\n    previousPageBorderBoxCenter: previousPageBorderBoxCenter,\n    draggable: draggable,\n    isOver: isOver,\n    draggables: draggables,\n    droppables: droppables,\n    viewport: state.viewport,\n    afterCritical: state.afterCritical\n  });\n});\n\nfunction isMovementAllowed(state) {\n  return state.phase === 'DRAGGING' || state.phase === 'COLLECTING';\n}\n\nfunction isPositionInFrame(frame) {\n  var isWithinVertical = isWithin(frame.top, frame.bottom);\n  var isWithinHorizontal = isWithin(frame.left, frame.right);\n  return function run(point) {\n    return isWithinVertical(point.y) && isWithinHorizontal(point.x);\n  };\n}\n\nfunction getHasOverlap(first, second) {\n  return first.left < second.right && first.right > second.left && first.top < second.bottom && first.bottom > second.top;\n}\n\nfunction getFurthestAway(_ref) {\n  var pageBorderBox = _ref.pageBorderBox,\n      draggable = _ref.draggable,\n      candidates = _ref.candidates;\n  var startCenter = draggable.page.borderBox.center;\n  var sorted = candidates.map(function (candidate) {\n    var axis = candidate.axis;\n    var target = patch(candidate.axis.line, pageBorderBox.center[axis.line], candidate.page.borderBox.center[axis.crossAxisLine]);\n    return {\n      id: candidate.descriptor.id,\n      distance: distance(startCenter, target)\n    };\n  }).sort(function (a, b) {\n    return b.distance - a.distance;\n  });\n  return sorted[0] ? sorted[0].id : null;\n}\n\nfunction getDroppableOver$1(_ref2) {\n  var pageBorderBox = _ref2.pageBorderBox,\n      draggable = _ref2.draggable,\n      droppables = _ref2.droppables;\n  var candidates = toDroppableList(droppables).filter(function (item) {\n    if (!item.isEnabled) {\n      return false;\n    }\n\n    var active = item.subject.active;\n\n    if (!active) {\n      return false;\n    }\n\n    if (!getHasOverlap(pageBorderBox, active)) {\n      return false;\n    }\n\n    if (isPositionInFrame(active)(pageBorderBox.center)) {\n      return true;\n    }\n\n    var axis = item.axis;\n    var childCenter = active.center[axis.crossAxisLine];\n    var crossAxisStart = pageBorderBox[axis.crossAxisStart];\n    var crossAxisEnd = pageBorderBox[axis.crossAxisEnd];\n    var isContained = isWithin(active[axis.crossAxisStart], active[axis.crossAxisEnd]);\n    var isStartContained = isContained(crossAxisStart);\n    var isEndContained = isContained(crossAxisEnd);\n\n    if (!isStartContained && !isEndContained) {\n      return true;\n    }\n\n    if (isStartContained) {\n      return crossAxisStart < childCenter;\n    }\n\n    return crossAxisEnd > childCenter;\n  });\n\n  if (!candidates.length) {\n    return null;\n  }\n\n  if (candidates.length === 1) {\n    return candidates[0].descriptor.id;\n  }\n\n  return getFurthestAway({\n    pageBorderBox: pageBorderBox,\n    draggable: draggable,\n    candidates: candidates\n  });\n}\n\nvar offsetRectByPosition = function offsetRectByPosition(rect, point) {\n  return getRect(offsetByPosition(rect, point));\n};\n\nvar withDroppableScroll = (function (droppable, area) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return area;\n  }\n\n  return offsetRectByPosition(area, frame.scroll.diff.value);\n});\n\nfunction getIsDisplaced(_ref) {\n  var displaced = _ref.displaced,\n      id = _ref.id;\n  return Boolean(displaced.visible[id] || displaced.invisible[id]);\n}\n\nfunction atIndex(_ref) {\n  var draggable = _ref.draggable,\n      closest = _ref.closest,\n      inHomeList = _ref.inHomeList;\n\n  if (!closest) {\n    return null;\n  }\n\n  if (!inHomeList) {\n    return closest.descriptor.index;\n  }\n\n  if (closest.descriptor.index > draggable.descriptor.index) {\n    return closest.descriptor.index - 1;\n  }\n\n  return closest.descriptor.index;\n}\n\nvar getReorderImpact = (function (_ref2) {\n  var targetRect = _ref2.pageBorderBoxWithDroppableScroll,\n      draggable = _ref2.draggable,\n      destination = _ref2.destination,\n      insideDestination = _ref2.insideDestination,\n      last = _ref2.last,\n      viewport = _ref2.viewport,\n      afterCritical = _ref2.afterCritical;\n  var axis = destination.axis;\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  var displacement = displacedBy.value;\n  var targetStart = targetRect[axis.start];\n  var targetEnd = targetRect[axis.end];\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var closest = find(withoutDragging, function (child) {\n    var id = child.descriptor.id;\n    var childCenter = child.page.borderBox.center[axis.line];\n    var didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    var isDisplaced = getIsDisplaced({\n      displaced: last,\n      id: id\n    });\n\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd <= childCenter;\n      }\n\n      return targetStart < childCenter - displacement;\n    }\n\n    if (isDisplaced) {\n      return targetEnd <= childCenter + displacement;\n    }\n\n    return targetStart < childCenter;\n  });\n  var newIndex = atIndex({\n    draggable: draggable,\n    closest: closest,\n    inHomeList: isHomeOf(draggable, destination)\n  });\n  return calculateReorderImpact({\n    draggable: draggable,\n    insideDestination: insideDestination,\n    destination: destination,\n    viewport: viewport,\n    last: last,\n    displacedBy: displacedBy,\n    index: newIndex\n  });\n});\n\nvar combineThresholdDivisor = 4;\nvar getCombineImpact = (function (_ref) {\n  var draggable = _ref.draggable,\n      targetRect = _ref.pageBorderBoxWithDroppableScroll,\n      previousImpact = _ref.previousImpact,\n      destination = _ref.destination,\n      insideDestination = _ref.insideDestination,\n      afterCritical = _ref.afterCritical;\n\n  if (!destination.isCombineEnabled) {\n    return null;\n  }\n\n  var axis = destination.axis;\n  var displacedBy = getDisplacedBy(destination.axis, draggable.displaceBy);\n  var displacement = displacedBy.value;\n  var targetStart = targetRect[axis.start];\n  var targetEnd = targetRect[axis.end];\n  var withoutDragging = removeDraggableFromList(draggable, insideDestination);\n  var combineWith = find(withoutDragging, function (child) {\n    var id = child.descriptor.id;\n    var childRect = child.page.borderBox;\n    var childSize = childRect[axis.size];\n    var threshold = childSize / combineThresholdDivisor;\n    var didStartAfterCritical$1 = didStartAfterCritical(id, afterCritical);\n    var isDisplaced = getIsDisplaced({\n      displaced: previousImpact.displaced,\n      id: id\n    });\n\n    if (didStartAfterCritical$1) {\n      if (isDisplaced) {\n        return targetEnd > childRect[axis.start] + threshold && targetEnd < childRect[axis.end] - threshold;\n      }\n\n      return targetStart > childRect[axis.start] - displacement + threshold && targetStart < childRect[axis.end] - displacement - threshold;\n    }\n\n    if (isDisplaced) {\n      return targetEnd > childRect[axis.start] + displacement + threshold && targetEnd < childRect[axis.end] + displacement - threshold;\n    }\n\n    return targetStart > childRect[axis.start] + threshold && targetStart < childRect[axis.end] - threshold;\n  });\n\n  if (!combineWith) {\n    return null;\n  }\n\n  var impact = {\n    displacedBy: displacedBy,\n    displaced: previousImpact.displaced,\n    at: {\n      type: 'COMBINE',\n      combine: {\n        draggableId: combineWith.descriptor.id,\n        droppableId: destination.descriptor.id\n      }\n    }\n  };\n  return impact;\n});\n\nvar getDragImpact = (function (_ref) {\n  var pageOffset = _ref.pageOffset,\n      draggable = _ref.draggable,\n      draggables = _ref.draggables,\n      droppables = _ref.droppables,\n      previousImpact = _ref.previousImpact,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var pageBorderBox = offsetRectByPosition(draggable.page.borderBox, pageOffset);\n  var destinationId = getDroppableOver$1({\n    pageBorderBox: pageBorderBox,\n    draggable: draggable,\n    droppables: droppables\n  });\n\n  if (!destinationId) {\n    return noImpact;\n  }\n\n  var destination = droppables[destinationId];\n  var insideDestination = getDraggablesInsideDroppable(destination.descriptor.id, draggables);\n  var pageBorderBoxWithDroppableScroll = withDroppableScroll(destination, pageBorderBox);\n  return getCombineImpact({\n    pageBorderBoxWithDroppableScroll: pageBorderBoxWithDroppableScroll,\n    draggable: draggable,\n    previousImpact: previousImpact,\n    destination: destination,\n    insideDestination: insideDestination,\n    afterCritical: afterCritical\n  }) || getReorderImpact({\n    pageBorderBoxWithDroppableScroll: pageBorderBoxWithDroppableScroll,\n    draggable: draggable,\n    destination: destination,\n    insideDestination: insideDestination,\n    last: previousImpact.displaced,\n    viewport: viewport,\n    afterCritical: afterCritical\n  });\n});\n\nvar patchDroppableMap = (function (droppables, updated) {\n  var _extends2;\n\n  return _extends({}, droppables, (_extends2 = {}, _extends2[updated.descriptor.id] = updated, _extends2));\n});\n\nvar clearUnusedPlaceholder = function clearUnusedPlaceholder(_ref) {\n  var previousImpact = _ref.previousImpact,\n      impact = _ref.impact,\n      droppables = _ref.droppables;\n  var last = whatIsDraggedOver(previousImpact);\n  var now = whatIsDraggedOver(impact);\n\n  if (!last) {\n    return droppables;\n  }\n\n  if (last === now) {\n    return droppables;\n  }\n\n  var lastDroppable = droppables[last];\n\n  if (!lastDroppable.subject.withPlaceholder) {\n    return droppables;\n  }\n\n  var updated = removePlaceholder(lastDroppable);\n  return patchDroppableMap(droppables, updated);\n};\n\nvar recomputePlaceholders = (function (_ref2) {\n  var draggable = _ref2.draggable,\n      draggables = _ref2.draggables,\n      droppables = _ref2.droppables,\n      previousImpact = _ref2.previousImpact,\n      impact = _ref2.impact;\n  var cleaned = clearUnusedPlaceholder({\n    previousImpact: previousImpact,\n    impact: impact,\n    droppables: droppables\n  });\n  var isOver = whatIsDraggedOver(impact);\n\n  if (!isOver) {\n    return cleaned;\n  }\n\n  var droppable = droppables[isOver];\n\n  if (isHomeOf(draggable, droppable)) {\n    return cleaned;\n  }\n\n  if (droppable.subject.withPlaceholder) {\n    return cleaned;\n  }\n\n  var patched = addPlaceholder(droppable, draggable, draggables);\n  return patchDroppableMap(cleaned, patched);\n});\n\nvar update = (function (_ref) {\n  var state = _ref.state,\n      forcedClientSelection = _ref.clientSelection,\n      forcedDimensions = _ref.dimensions,\n      forcedViewport = _ref.viewport,\n      forcedImpact = _ref.impact,\n      scrollJumpRequest = _ref.scrollJumpRequest;\n  var viewport = forcedViewport || state.viewport;\n  var dimensions = forcedDimensions || state.dimensions;\n  var clientSelection = forcedClientSelection || state.current.client.selection;\n  var offset = subtract(clientSelection, state.initial.client.selection);\n  var client = {\n    offset: offset,\n    selection: clientSelection,\n    borderBoxCenter: add(state.initial.client.borderBoxCenter, offset)\n  };\n  var page = {\n    selection: add(client.selection, viewport.scroll.current),\n    borderBoxCenter: add(client.borderBoxCenter, viewport.scroll.current),\n    offset: add(client.offset, viewport.scroll.diff.value)\n  };\n  var current = {\n    client: client,\n    page: page\n  };\n\n  if (state.phase === 'COLLECTING') {\n    return _extends({\n      phase: 'COLLECTING'\n    }, state, {\n      dimensions: dimensions,\n      viewport: viewport,\n      current: current\n    });\n  }\n\n  var draggable = dimensions.draggables[state.critical.draggable.id];\n  var newImpact = forcedImpact || getDragImpact({\n    pageOffset: page.offset,\n    draggable: draggable,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: state.impact,\n    viewport: viewport,\n    afterCritical: state.afterCritical\n  });\n  var withUpdatedPlaceholders = recomputePlaceholders({\n    draggable: draggable,\n    impact: newImpact,\n    previousImpact: state.impact,\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables\n  });\n\n  var result = _extends({}, state, {\n    current: current,\n    dimensions: {\n      draggables: dimensions.draggables,\n      droppables: withUpdatedPlaceholders\n    },\n    impact: newImpact,\n    viewport: viewport,\n    scrollJumpRequest: scrollJumpRequest || null,\n    forceShouldAnimate: scrollJumpRequest ? false : null\n  });\n\n  return result;\n});\n\nfunction getDraggables$1(ids, draggables) {\n  return ids.map(function (id) {\n    return draggables[id];\n  });\n}\n\nvar recompute = (function (_ref) {\n  var impact = _ref.impact,\n      viewport = _ref.viewport,\n      draggables = _ref.draggables,\n      destination = _ref.destination,\n      forceShouldAnimate = _ref.forceShouldAnimate;\n  var last = impact.displaced;\n  var afterDragging = getDraggables$1(last.all, draggables);\n  var displaced = getDisplacementGroups({\n    afterDragging: afterDragging,\n    destination: destination,\n    displacedBy: impact.displacedBy,\n    viewport: viewport.frame,\n    forceShouldAnimate: forceShouldAnimate,\n    last: last\n  });\n  return _extends({}, impact, {\n    displaced: displaced\n  });\n});\n\nvar getClientBorderBoxCenter = (function (_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      droppable = _ref.droppable,\n      draggables = _ref.draggables,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var pageBorderBoxCenter = getPageBorderBoxCenterFromImpact({\n    impact: impact,\n    draggable: draggable,\n    draggables: draggables,\n    droppable: droppable,\n    afterCritical: afterCritical\n  });\n  return getClientFromPageBorderBoxCenter({\n    pageBorderBoxCenter: pageBorderBoxCenter,\n    draggable: draggable,\n    viewport: viewport\n  });\n});\n\nvar refreshSnap = (function (_ref) {\n  var state = _ref.state,\n      forcedDimensions = _ref.dimensions,\n      forcedViewport = _ref.viewport;\n  !(state.movementMode === 'SNAP') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n  var needsVisibilityCheck = state.impact;\n  var viewport = forcedViewport || state.viewport;\n  var dimensions = forcedDimensions || state.dimensions;\n  var draggables = dimensions.draggables,\n      droppables = dimensions.droppables;\n  var draggable = draggables[state.critical.draggable.id];\n  var isOver = whatIsDraggedOver(needsVisibilityCheck);\n  !isOver ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must be over a destination in SNAP movement mode') : invariant(false) : void 0;\n  var destination = droppables[isOver];\n  var impact = recompute({\n    impact: needsVisibilityCheck,\n    viewport: viewport,\n    destination: destination,\n    draggables: draggables\n  });\n  var clientSelection = getClientBorderBoxCenter({\n    impact: impact,\n    draggable: draggable,\n    droppable: destination,\n    draggables: draggables,\n    viewport: viewport,\n    afterCritical: state.afterCritical\n  });\n  return update({\n    impact: impact,\n    clientSelection: clientSelection,\n    state: state,\n    dimensions: dimensions,\n    viewport: viewport\n  });\n});\n\nvar getHomeLocation = (function (descriptor) {\n  return {\n    index: descriptor.index,\n    droppableId: descriptor.droppableId\n  };\n});\n\nvar getLiftEffect = (function (_ref) {\n  var draggable = _ref.draggable,\n      home = _ref.home,\n      draggables = _ref.draggables,\n      viewport = _ref.viewport;\n  var displacedBy = getDisplacedBy(home.axis, draggable.displaceBy);\n  var insideHome = getDraggablesInsideDroppable(home.descriptor.id, draggables);\n  var rawIndex = insideHome.indexOf(draggable);\n  !(rawIndex !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected draggable to be inside home list') : invariant(false) : void 0;\n  var afterDragging = insideHome.slice(rawIndex + 1);\n  var effected = afterDragging.reduce(function (previous, item) {\n    previous[item.descriptor.id] = true;\n    return previous;\n  }, {});\n  var afterCritical = {\n    inVirtualList: home.descriptor.mode === 'virtual',\n    displacedBy: displacedBy,\n    effected: effected\n  };\n  var displaced = getDisplacementGroups({\n    afterDragging: afterDragging,\n    destination: home,\n    displacedBy: displacedBy,\n    last: null,\n    viewport: viewport.frame,\n    forceShouldAnimate: false\n  });\n  var impact = {\n    displaced: displaced,\n    displacedBy: displacedBy,\n    at: {\n      type: 'REORDER',\n      destination: getHomeLocation(draggable.descriptor)\n    }\n  };\n  return {\n    impact: impact,\n    afterCritical: afterCritical\n  };\n});\n\nvar patchDimensionMap = (function (dimensions, updated) {\n  return {\n    draggables: dimensions.draggables,\n    droppables: patchDroppableMap(dimensions.droppables, updated)\n  };\n});\n\nvar start = function start(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\nvar finish = function finish(key) {\n  if (process.env.NODE_ENV !== 'production') {\n    {\n      return;\n    }\n  }\n};\n\nvar offsetDraggable = (function (_ref) {\n  var draggable = _ref.draggable,\n      offset$1 = _ref.offset,\n      initialWindowScroll = _ref.initialWindowScroll;\n  var client = offset(draggable.client, offset$1);\n  var page = withScroll(client, initialWindowScroll);\n\n  var moved = _extends({}, draggable, {\n    placeholder: _extends({}, draggable.placeholder, {\n      client: client\n    }),\n    client: client,\n    page: page\n  });\n\n  return moved;\n});\n\nvar getFrame = (function (droppable) {\n  var frame = droppable.frame;\n  !frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected Droppable to have a frame') : invariant(false) : void 0;\n  return frame;\n});\n\nvar adjustAdditionsForScrollChanges = (function (_ref) {\n  var additions = _ref.additions,\n      updatedDroppables = _ref.updatedDroppables,\n      viewport = _ref.viewport;\n  var windowScrollChange = viewport.scroll.diff.value;\n  return additions.map(function (draggable) {\n    var droppableId = draggable.descriptor.droppableId;\n    var modified = updatedDroppables[droppableId];\n    var frame = getFrame(modified);\n    var droppableScrollChange = frame.scroll.diff.value;\n    var totalChange = add(windowScrollChange, droppableScrollChange);\n    var moved = offsetDraggable({\n      draggable: draggable,\n      offset: totalChange,\n      initialWindowScroll: viewport.scroll.initial\n    });\n    return moved;\n  });\n});\n\nvar publishWhileDraggingInVirtual = (function (_ref) {\n  var state = _ref.state,\n      published = _ref.published;\n  start();\n  var withScrollChange = published.modified.map(function (update) {\n    var existing = state.dimensions.droppables[update.droppableId];\n    var scrolled = scrollDroppable(existing, update.scroll);\n    return scrolled;\n  });\n\n  var droppables = _extends({}, state.dimensions.droppables, {}, toDroppableMap(withScrollChange));\n\n  var updatedAdditions = toDraggableMap(adjustAdditionsForScrollChanges({\n    additions: published.additions,\n    updatedDroppables: droppables,\n    viewport: state.viewport\n  }));\n\n  var draggables = _extends({}, state.dimensions.draggables, {}, updatedAdditions);\n\n  published.removals.forEach(function (id) {\n    delete draggables[id];\n  });\n  var dimensions = {\n    droppables: droppables,\n    draggables: draggables\n  };\n  var wasOverId = whatIsDraggedOver(state.impact);\n  var wasOver = wasOverId ? dimensions.droppables[wasOverId] : null;\n  var draggable = dimensions.draggables[state.critical.draggable.id];\n  var home = dimensions.droppables[state.critical.droppable.id];\n\n  var _getLiftEffect = getLiftEffect({\n    draggable: draggable,\n    home: home,\n    draggables: draggables,\n    viewport: state.viewport\n  }),\n      onLiftImpact = _getLiftEffect.impact,\n      afterCritical = _getLiftEffect.afterCritical;\n\n  var previousImpact = wasOver && wasOver.isCombineEnabled ? state.impact : onLiftImpact;\n  var impact = getDragImpact({\n    pageOffset: state.current.page.offset,\n    draggable: dimensions.draggables[state.critical.draggable.id],\n    draggables: dimensions.draggables,\n    droppables: dimensions.droppables,\n    previousImpact: previousImpact,\n    viewport: state.viewport,\n    afterCritical: afterCritical\n  });\n  finish();\n\n  var draggingState = _extends({\n    phase: 'DRAGGING'\n  }, state, {\n    phase: 'DRAGGING',\n    impact: impact,\n    onLiftImpact: onLiftImpact,\n    dimensions: dimensions,\n    afterCritical: afterCritical,\n    forceShouldAnimate: false\n  });\n\n  if (state.phase === 'COLLECTING') {\n    return draggingState;\n  }\n\n  var dropPending = _extends({\n    phase: 'DROP_PENDING'\n  }, draggingState, {\n    phase: 'DROP_PENDING',\n    reason: state.reason,\n    isWaiting: false\n  });\n\n  return dropPending;\n});\n\nvar isSnapping = function isSnapping(state) {\n  return state.movementMode === 'SNAP';\n};\n\nvar postDroppableChange = function postDroppableChange(state, updated, isEnabledChanging) {\n  var dimensions = patchDimensionMap(state.dimensions, updated);\n\n  if (!isSnapping(state) || isEnabledChanging) {\n    return update({\n      state: state,\n      dimensions: dimensions\n    });\n  }\n\n  return refreshSnap({\n    state: state,\n    dimensions: dimensions\n  });\n};\n\nfunction removeScrollJumpRequest(state) {\n  if (state.isDragging && state.movementMode === 'SNAP') {\n    return _extends({\n      phase: 'DRAGGING'\n    }, state, {\n      scrollJumpRequest: null\n    });\n  }\n\n  return state;\n}\n\nvar idle = {\n  phase: 'IDLE',\n  completed: null,\n  shouldFlush: false\n};\nvar reducer = (function (state, action) {\n  if (state === void 0) {\n    state = idle;\n  }\n\n  if (action.type === 'FLUSH') {\n    return _extends({}, idle, {\n      shouldFlush: true\n    });\n  }\n\n  if (action.type === 'INITIAL_PUBLISH') {\n    !(state.phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'INITIAL_PUBLISH must come after a IDLE phase') : invariant(false) : void 0;\n    var _action$payload = action.payload,\n        critical = _action$payload.critical,\n        clientSelection = _action$payload.clientSelection,\n        viewport = _action$payload.viewport,\n        dimensions = _action$payload.dimensions,\n        movementMode = _action$payload.movementMode;\n    var draggable = dimensions.draggables[critical.draggable.id];\n    var home = dimensions.droppables[critical.droppable.id];\n    var client = {\n      selection: clientSelection,\n      borderBoxCenter: draggable.client.borderBox.center,\n      offset: origin\n    };\n    var initial = {\n      client: client,\n      page: {\n        selection: add(client.selection, viewport.scroll.initial),\n        borderBoxCenter: add(client.selection, viewport.scroll.initial),\n        offset: add(client.selection, viewport.scroll.diff.value)\n      }\n    };\n    var isWindowScrollAllowed = toDroppableList(dimensions.droppables).every(function (item) {\n      return !item.isFixedOnPage;\n    });\n\n    var _getLiftEffect = getLiftEffect({\n      draggable: draggable,\n      home: home,\n      draggables: dimensions.draggables,\n      viewport: viewport\n    }),\n        impact = _getLiftEffect.impact,\n        afterCritical = _getLiftEffect.afterCritical;\n\n    var result = {\n      phase: 'DRAGGING',\n      isDragging: true,\n      critical: critical,\n      movementMode: movementMode,\n      dimensions: dimensions,\n      initial: initial,\n      current: initial,\n      isWindowScrollAllowed: isWindowScrollAllowed,\n      impact: impact,\n      afterCritical: afterCritical,\n      onLiftImpact: impact,\n      viewport: viewport,\n      scrollJumpRequest: null,\n      forceShouldAnimate: null\n    };\n    return result;\n  }\n\n  if (action.type === 'COLLECTION_STARTING') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Collection cannot start from phase \" + state.phase) : invariant(false) : void 0;\n\n    var _result = _extends({\n      phase: 'COLLECTING'\n    }, state, {\n      phase: 'COLLECTING'\n    });\n\n    return _result;\n  }\n\n  if (action.type === 'PUBLISH_WHILE_DRAGGING') {\n    !(state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Unexpected \" + action.type + \" received in phase \" + state.phase) : invariant(false) : void 0;\n    return publishWhileDraggingInVirtual({\n      state: state,\n      published: action.payload\n    });\n  }\n\n  if (action.type === 'MOVE') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" not permitted in phase \" + state.phase) : invariant(false) : void 0;\n    var _clientSelection = action.payload.client;\n\n    if (isEqual(_clientSelection, state.current.client.selection)) {\n      return state;\n    }\n\n    return update({\n      state: state,\n      clientSelection: _clientSelection,\n      impact: isSnapping(state) ? state.impact : null\n    });\n  }\n\n  if (action.type === 'UPDATE_DROPPABLE_SCROLL') {\n    if (state.phase === 'DROP_PENDING') {\n      return removeScrollJumpRequest(state);\n    }\n\n    if (state.phase === 'COLLECTING') {\n      return removeScrollJumpRequest(state);\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" not permitted in phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload2 = action.payload,\n        id = _action$payload2.id,\n        newScroll = _action$payload2.newScroll;\n    var target = state.dimensions.droppables[id];\n\n    if (!target) {\n      return state;\n    }\n\n    var scrolled = scrollDroppable(target, newScroll);\n    return postDroppableChange(state, scrolled, false);\n  }\n\n  if (action.type === 'UPDATE_DROPPABLE_IS_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Attempting to move in an unsupported phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload3 = action.payload,\n        _id = _action$payload3.id,\n        isEnabled = _action$payload3.isEnabled;\n    var _target = state.dimensions.droppables[_id];\n    !_target ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find Droppable[id: \" + _id + \"] to toggle its enabled state\") : invariant(false) : void 0;\n    !(_target.isEnabled !== isEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Trying to set droppable isEnabled to \" + String(isEnabled) + \"\\n      but it is already \" + String(_target.isEnabled)) : invariant(false) : void 0;\n\n    var updated = _extends({}, _target, {\n      isEnabled: isEnabled\n    });\n\n    return postDroppableChange(state, updated, true);\n  }\n\n  if (action.type === 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED') {\n    if (state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Attempting to move in an unsupported phase \" + state.phase) : invariant(false) : void 0;\n    var _action$payload4 = action.payload,\n        _id2 = _action$payload4.id,\n        isCombineEnabled = _action$payload4.isCombineEnabled;\n    var _target2 = state.dimensions.droppables[_id2];\n    !_target2 ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find Droppable[id: \" + _id2 + \"] to toggle its isCombineEnabled state\") : invariant(false) : void 0;\n    !(_target2.isCombineEnabled !== isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Trying to set droppable isCombineEnabled to \" + String(isCombineEnabled) + \"\\n      but it is already \" + String(_target2.isCombineEnabled)) : invariant(false) : void 0;\n\n    var _updated = _extends({}, _target2, {\n      isCombineEnabled: isCombineEnabled\n    });\n\n    return postDroppableChange(state, _updated, true);\n  }\n\n  if (action.type === 'MOVE_BY_WINDOW_SCROLL') {\n    if (state.phase === 'DROP_PENDING' || state.phase === 'DROP_ANIMATING') {\n      return state;\n    }\n\n    !isMovementAllowed(state) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot move by window in phase \" + state.phase) : invariant(false) : void 0;\n    !state.isWindowScrollAllowed ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Window scrolling is currently not supported for fixed lists') : invariant(false) : void 0;\n    var _newScroll = action.payload.newScroll;\n\n    if (isEqual(state.viewport.scroll.current, _newScroll)) {\n      return removeScrollJumpRequest(state);\n    }\n\n    var _viewport = scrollViewport(state.viewport, _newScroll);\n\n    if (isSnapping(state)) {\n      return refreshSnap({\n        state: state,\n        viewport: _viewport\n      });\n    }\n\n    return update({\n      state: state,\n      viewport: _viewport\n    });\n  }\n\n  if (action.type === 'UPDATE_VIEWPORT_MAX_SCROLL') {\n    if (!isMovementAllowed(state)) {\n      return state;\n    }\n\n    var maxScroll = action.payload.maxScroll;\n\n    if (isEqual(maxScroll, state.viewport.scroll.max)) {\n      return state;\n    }\n\n    var withMaxScroll = _extends({}, state.viewport, {\n      scroll: _extends({}, state.viewport.scroll, {\n        max: maxScroll\n      })\n    });\n\n    return _extends({\n      phase: 'DRAGGING'\n    }, state, {\n      viewport: withMaxScroll\n    });\n  }\n\n  if (action.type === 'MOVE_UP' || action.type === 'MOVE_DOWN' || action.type === 'MOVE_LEFT' || action.type === 'MOVE_RIGHT') {\n    if (state.phase === 'COLLECTING' || state.phase === 'DROP_PENDING') {\n      return state;\n    }\n\n    !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, action.type + \" received while not in DRAGGING phase\") : invariant(false) : void 0;\n\n    var _result2 = moveInDirection({\n      state: state,\n      type: action.type\n    });\n\n    if (!_result2) {\n      return state;\n    }\n\n    return update({\n      state: state,\n      impact: _result2.impact,\n      clientSelection: _result2.clientSelection,\n      scrollJumpRequest: _result2.scrollJumpRequest\n    });\n  }\n\n  if (action.type === 'DROP_PENDING') {\n    var reason = action.payload.reason;\n    !(state.phase === 'COLLECTING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only move into the DROP_PENDING phase from the COLLECTING phase') : invariant(false) : void 0;\n\n    var newState = _extends({\n      phase: 'DROP_PENDING'\n    }, state, {\n      phase: 'DROP_PENDING',\n      isWaiting: true,\n      reason: reason\n    });\n\n    return newState;\n  }\n\n  if (action.type === 'DROP_ANIMATE') {\n    var _action$payload5 = action.payload,\n        completed = _action$payload5.completed,\n        dropDuration = _action$payload5.dropDuration,\n        newHomeClientOffset = _action$payload5.newHomeClientOffset;\n    !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot animate drop from phase \" + state.phase) : invariant(false) : void 0;\n    var _result3 = {\n      phase: 'DROP_ANIMATING',\n      completed: completed,\n      dropDuration: dropDuration,\n      newHomeClientOffset: newHomeClientOffset,\n      dimensions: state.dimensions\n    };\n    return _result3;\n  }\n\n  if (action.type === 'DROP_COMPLETE') {\n    var _completed = action.payload.completed;\n    return {\n      phase: 'IDLE',\n      completed: _completed,\n      shouldFlush: false\n    };\n  }\n\n  return state;\n});\n\nvar beforeInitialCapture = function beforeInitialCapture(args) {\n  return {\n    type: 'BEFORE_INITIAL_CAPTURE',\n    payload: args\n  };\n};\nvar lift = function lift(args) {\n  return {\n    type: 'LIFT',\n    payload: args\n  };\n};\nvar initialPublish = function initialPublish(args) {\n  return {\n    type: 'INITIAL_PUBLISH',\n    payload: args\n  };\n};\nvar publishWhileDragging = function publishWhileDragging(args) {\n  return {\n    type: 'PUBLISH_WHILE_DRAGGING',\n    payload: args\n  };\n};\nvar collectionStarting = function collectionStarting() {\n  return {\n    type: 'COLLECTION_STARTING',\n    payload: null\n  };\n};\nvar updateDroppableScroll = function updateDroppableScroll(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_SCROLL',\n    payload: args\n  };\n};\nvar updateDroppableIsEnabled = function updateDroppableIsEnabled(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_IS_ENABLED',\n    payload: args\n  };\n};\nvar updateDroppableIsCombineEnabled = function updateDroppableIsCombineEnabled(args) {\n  return {\n    type: 'UPDATE_DROPPABLE_IS_COMBINE_ENABLED',\n    payload: args\n  };\n};\nvar move = function move(args) {\n  return {\n    type: 'MOVE',\n    payload: args\n  };\n};\nvar moveByWindowScroll = function moveByWindowScroll(args) {\n  return {\n    type: 'MOVE_BY_WINDOW_SCROLL',\n    payload: args\n  };\n};\nvar updateViewportMaxScroll = function updateViewportMaxScroll(args) {\n  return {\n    type: 'UPDATE_VIEWPORT_MAX_SCROLL',\n    payload: args\n  };\n};\nvar moveUp = function moveUp() {\n  return {\n    type: 'MOVE_UP',\n    payload: null\n  };\n};\nvar moveDown = function moveDown() {\n  return {\n    type: 'MOVE_DOWN',\n    payload: null\n  };\n};\nvar moveRight = function moveRight() {\n  return {\n    type: 'MOVE_RIGHT',\n    payload: null\n  };\n};\nvar moveLeft = function moveLeft() {\n  return {\n    type: 'MOVE_LEFT',\n    payload: null\n  };\n};\nvar flush = function flush() {\n  return {\n    type: 'FLUSH',\n    payload: null\n  };\n};\nvar animateDrop = function animateDrop(args) {\n  return {\n    type: 'DROP_ANIMATE',\n    payload: args\n  };\n};\nvar completeDrop = function completeDrop(args) {\n  return {\n    type: 'DROP_COMPLETE',\n    payload: args\n  };\n};\nvar drop = function drop(args) {\n  return {\n    type: 'DROP',\n    payload: args\n  };\n};\nvar dropPending = function dropPending(args) {\n  return {\n    type: 'DROP_PENDING',\n    payload: args\n  };\n};\nvar dropAnimationFinished = function dropAnimationFinished() {\n  return {\n    type: 'DROP_ANIMATION_FINISHED',\n    payload: null\n  };\n};\n\nfunction checkIndexes(insideDestination) {\n  if (insideDestination.length <= 1) {\n    return;\n  }\n\n  var indexes = insideDestination.map(function (d) {\n    return d.descriptor.index;\n  });\n  var errors = {};\n\n  for (var i = 1; i < indexes.length; i++) {\n    var current = indexes[i];\n    var previous = indexes[i - 1];\n\n    if (current !== previous + 1) {\n      errors[current] = true;\n    }\n  }\n\n  if (!Object.keys(errors).length) {\n    return;\n  }\n\n  var formatted = indexes.map(function (index) {\n    var hasError = Boolean(errors[index]);\n    return hasError ? \"[\\uD83D\\uDD25\" + index + \"]\" : \"\" + index;\n  }).join(', ');\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    Detected non-consecutive <Draggable /> indexes.\\n\\n    (This can cause unexpected bugs)\\n\\n    \" + formatted + \"\\n  \") : void 0;\n}\n\nfunction validateDimensions(critical, dimensions) {\n  if (process.env.NODE_ENV !== 'production') {\n    var insideDestination = getDraggablesInsideDroppable(critical.droppable.id, dimensions.draggables);\n    checkIndexes(insideDestination);\n  }\n}\n\nvar lift$1 = (function (marshal) {\n  return function (_ref) {\n    var getState = _ref.getState,\n        dispatch = _ref.dispatch;\n    return function (next) {\n      return function (action) {\n        if (action.type !== 'LIFT') {\n          next(action);\n          return;\n        }\n\n        var _action$payload = action.payload,\n            id = _action$payload.id,\n            clientSelection = _action$payload.clientSelection,\n            movementMode = _action$payload.movementMode;\n        var initial = getState();\n\n        if (initial.phase === 'DROP_ANIMATING') {\n          dispatch(completeDrop({\n            completed: initial.completed\n          }));\n        }\n\n        !(getState().phase === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase to start a drag') : invariant(false) : void 0;\n        dispatch(flush());\n        dispatch(beforeInitialCapture({\n          draggableId: id,\n          movementMode: movementMode\n        }));\n        var scrollOptions = {\n          shouldPublishImmediately: movementMode === 'SNAP'\n        };\n        var request = {\n          draggableId: id,\n          scrollOptions: scrollOptions\n        };\n\n        var _marshal$startPublish = marshal.startPublishing(request),\n            critical = _marshal$startPublish.critical,\n            dimensions = _marshal$startPublish.dimensions,\n            viewport = _marshal$startPublish.viewport;\n\n        validateDimensions(critical, dimensions);\n        dispatch(initialPublish({\n          critical: critical,\n          dimensions: dimensions,\n          clientSelection: clientSelection,\n          movementMode: movementMode,\n          viewport: viewport\n        }));\n      };\n    };\n  };\n});\n\nvar style = (function (marshal) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'INITIAL_PUBLISH') {\n          marshal.dragging();\n        }\n\n        if (action.type === 'DROP_ANIMATE') {\n          marshal.dropping(action.payload.completed.result.reason);\n        }\n\n        if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE') {\n          marshal.resting();\n        }\n\n        next(action);\n      };\n    };\n  };\n});\n\nvar curves = {\n  outOfTheWay: 'cubic-bezier(0.2, 0, 0, 1)',\n  drop: 'cubic-bezier(.2,1,.1,1)'\n};\nvar combine = {\n  opacity: {\n    drop: 0,\n    combining: 0.7\n  },\n  scale: {\n    drop: 0.75\n  }\n};\nvar timings = {\n  outOfTheWay: 0.2,\n  minDropTime: 0.33,\n  maxDropTime: 0.55\n};\nvar outOfTheWayTiming = timings.outOfTheWay + \"s \" + curves.outOfTheWay;\nvar transitions = {\n  fluid: \"opacity \" + outOfTheWayTiming,\n  snap: \"transform \" + outOfTheWayTiming + \", opacity \" + outOfTheWayTiming,\n  drop: function drop(duration) {\n    var timing = duration + \"s \" + curves.drop;\n    return \"transform \" + timing + \", opacity \" + timing;\n  },\n  outOfTheWay: \"transform \" + outOfTheWayTiming,\n  placeholder: \"height \" + outOfTheWayTiming + \", width \" + outOfTheWayTiming + \", margin \" + outOfTheWayTiming\n};\n\nvar moveTo = function moveTo(offset) {\n  return isEqual(offset, origin) ? null : \"translate(\" + offset.x + \"px, \" + offset.y + \"px)\";\n};\n\nvar transforms = {\n  moveTo: moveTo,\n  drop: function drop(offset, isCombining) {\n    var translate = moveTo(offset);\n\n    if (!translate) {\n      return null;\n    }\n\n    if (!isCombining) {\n      return translate;\n    }\n\n    return translate + \" scale(\" + combine.scale.drop + \")\";\n  }\n};\n\nvar minDropTime = timings.minDropTime,\n    maxDropTime = timings.maxDropTime;\nvar dropTimeRange = maxDropTime - minDropTime;\nvar maxDropTimeAtDistance = 1500;\nvar cancelDropModifier = 0.6;\nvar getDropDuration = (function (_ref) {\n  var current = _ref.current,\n      destination = _ref.destination,\n      reason = _ref.reason;\n  var distance$1 = distance(current, destination);\n\n  if (distance$1 <= 0) {\n    return minDropTime;\n  }\n\n  if (distance$1 >= maxDropTimeAtDistance) {\n    return maxDropTime;\n  }\n\n  var percentage = distance$1 / maxDropTimeAtDistance;\n  var duration = minDropTime + dropTimeRange * percentage;\n  var withDuration = reason === 'CANCEL' ? duration * cancelDropModifier : duration;\n  return Number(withDuration.toFixed(2));\n});\n\nvar getNewHomeClientOffset = (function (_ref) {\n  var impact = _ref.impact,\n      draggable = _ref.draggable,\n      dimensions = _ref.dimensions,\n      viewport = _ref.viewport,\n      afterCritical = _ref.afterCritical;\n  var draggables = dimensions.draggables,\n      droppables = dimensions.droppables;\n  var droppableId = whatIsDraggedOver(impact);\n  var destination = droppableId ? droppables[droppableId] : null;\n  var home = droppables[draggable.descriptor.droppableId];\n  var newClientCenter = getClientBorderBoxCenter({\n    impact: impact,\n    draggable: draggable,\n    draggables: draggables,\n    afterCritical: afterCritical,\n    droppable: destination || home,\n    viewport: viewport\n  });\n  var offset = subtract(newClientCenter, draggable.client.borderBox.center);\n  return offset;\n});\n\nvar getDropImpact = (function (_ref) {\n  var draggables = _ref.draggables,\n      reason = _ref.reason,\n      lastImpact = _ref.lastImpact,\n      home = _ref.home,\n      viewport = _ref.viewport,\n      onLiftImpact = _ref.onLiftImpact;\n\n  if (!lastImpact.at || reason !== 'DROP') {\n    var recomputedHomeImpact = recompute({\n      draggables: draggables,\n      impact: onLiftImpact,\n      destination: home,\n      viewport: viewport,\n      forceShouldAnimate: true\n    });\n    return {\n      impact: recomputedHomeImpact,\n      didDropInsideDroppable: false\n    };\n  }\n\n  if (lastImpact.at.type === 'REORDER') {\n    return {\n      impact: lastImpact,\n      didDropInsideDroppable: true\n    };\n  }\n\n  var withoutMovement = _extends({}, lastImpact, {\n    displaced: emptyGroups\n  });\n\n  return {\n    impact: withoutMovement,\n    didDropInsideDroppable: true\n  };\n});\n\nvar drop$1 = (function (_ref) {\n  var getState = _ref.getState,\n      dispatch = _ref.dispatch;\n  return function (next) {\n    return function (action) {\n      if (action.type !== 'DROP') {\n        next(action);\n        return;\n      }\n\n      var state = getState();\n      var reason = action.payload.reason;\n\n      if (state.phase === 'COLLECTING') {\n        dispatch(dropPending({\n          reason: reason\n        }));\n        return;\n      }\n\n      if (state.phase === 'IDLE') {\n        return;\n      }\n\n      var isWaitingForDrop = state.phase === 'DROP_PENDING' && state.isWaiting;\n      !!isWaitingForDrop ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A DROP action occurred while DROP_PENDING and still waiting') : invariant(false) : void 0;\n      !(state.phase === 'DRAGGING' || state.phase === 'DROP_PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot drop in phase: \" + state.phase) : invariant(false) : void 0;\n      var critical = state.critical;\n      var dimensions = state.dimensions;\n      var draggable = dimensions.draggables[state.critical.draggable.id];\n\n      var _getDropImpact = getDropImpact({\n        reason: reason,\n        lastImpact: state.impact,\n        afterCritical: state.afterCritical,\n        onLiftImpact: state.onLiftImpact,\n        home: state.dimensions.droppables[state.critical.droppable.id],\n        viewport: state.viewport,\n        draggables: state.dimensions.draggables\n      }),\n          impact = _getDropImpact.impact,\n          didDropInsideDroppable = _getDropImpact.didDropInsideDroppable;\n\n      var destination = didDropInsideDroppable ? tryGetDestination(impact) : null;\n      var combine = didDropInsideDroppable ? tryGetCombine(impact) : null;\n      var source = {\n        index: critical.draggable.index,\n        droppableId: critical.droppable.id\n      };\n      var result = {\n        draggableId: draggable.descriptor.id,\n        type: draggable.descriptor.type,\n        source: source,\n        reason: reason,\n        mode: state.movementMode,\n        destination: destination,\n        combine: combine\n      };\n      var newHomeClientOffset = getNewHomeClientOffset({\n        impact: impact,\n        draggable: draggable,\n        dimensions: dimensions,\n        viewport: state.viewport,\n        afterCritical: state.afterCritical\n      });\n      var completed = {\n        critical: state.critical,\n        afterCritical: state.afterCritical,\n        result: result,\n        impact: impact\n      };\n      var isAnimationRequired = !isEqual(state.current.client.offset, newHomeClientOffset) || Boolean(result.combine);\n\n      if (!isAnimationRequired) {\n        dispatch(completeDrop({\n          completed: completed\n        }));\n        return;\n      }\n\n      var dropDuration = getDropDuration({\n        current: state.current.client.offset,\n        destination: newHomeClientOffset,\n        reason: reason\n      });\n      var args = {\n        newHomeClientOffset: newHomeClientOffset,\n        dropDuration: dropDuration,\n        completed: completed\n      };\n      dispatch(animateDrop(args));\n    };\n  };\n});\n\nvar getWindowScroll = (function () {\n  return {\n    x: window.pageXOffset,\n    y: window.pageYOffset\n  };\n});\n\nfunction getWindowScrollBinding(update) {\n  return {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: function fn(event) {\n      if (event.target !== window && event.target !== window.document) {\n        return;\n      }\n\n      update();\n    }\n  };\n}\n\nfunction getScrollListener(_ref) {\n  var onWindowScroll = _ref.onWindowScroll;\n\n  function updateScroll() {\n    onWindowScroll(getWindowScroll());\n  }\n\n  var scheduled = rafSchd(updateScroll);\n  var binding = getWindowScrollBinding(scheduled);\n  var unbind = noop;\n\n  function isActive() {\n    return unbind !== noop;\n  }\n\n  function start() {\n    !!isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start scroll listener when already active') : invariant(false) : void 0;\n    unbind = bindEvents(window, [binding]);\n  }\n\n  function stop() {\n    !isActive() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop scroll listener when not active') : invariant(false) : void 0;\n    scheduled.cancel();\n    unbind();\n    unbind = noop;\n  }\n\n  return {\n    start: start,\n    stop: stop,\n    isActive: isActive\n  };\n}\n\nvar shouldEnd = function shouldEnd(action) {\n  return action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\n};\n\nvar scrollListener = (function (store) {\n  var listener = getScrollListener({\n    onWindowScroll: function onWindowScroll(newScroll) {\n      store.dispatch(moveByWindowScroll({\n        newScroll: newScroll\n      }));\n    }\n  });\n  return function (next) {\n    return function (action) {\n      if (!listener.isActive() && action.type === 'INITIAL_PUBLISH') {\n        listener.start();\n      }\n\n      if (listener.isActive() && shouldEnd(action)) {\n        listener.stop();\n      }\n\n      next(action);\n    };\n  };\n});\n\nvar getExpiringAnnounce = (function (announce) {\n  var wasCalled = false;\n  var isExpired = false;\n  var timeoutId = setTimeout(function () {\n    isExpired = true;\n  });\n\n  var result = function result(message) {\n    if (wasCalled) {\n      process.env.NODE_ENV !== \"production\" ? warning('Announcement already made. Not making a second announcement') : void 0;\n      return;\n    }\n\n    if (isExpired) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Announcements cannot be made asynchronously.\\n        Default message has already been announced.\\n      \") : void 0;\n      return;\n    }\n\n    wasCalled = true;\n    announce(message);\n    clearTimeout(timeoutId);\n  };\n\n  result.wasCalled = function () {\n    return wasCalled;\n  };\n\n  return result;\n});\n\nvar getAsyncMarshal = (function () {\n  var entries = [];\n\n  var execute = function execute(timerId) {\n    var index = findIndex(entries, function (item) {\n      return item.timerId === timerId;\n    });\n    !(index !== -1) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find timer') : invariant(false) : void 0;\n\n    var _entries$splice = entries.splice(index, 1),\n        entry = _entries$splice[0];\n\n    entry.callback();\n  };\n\n  var add = function add(fn) {\n    var timerId = setTimeout(function () {\n      return execute(timerId);\n    });\n    var entry = {\n      timerId: timerId,\n      callback: fn\n    };\n    entries.push(entry);\n  };\n\n  var flush = function flush() {\n    if (!entries.length) {\n      return;\n    }\n\n    var shallow = [].concat(entries);\n    entries.length = 0;\n    shallow.forEach(function (entry) {\n      clearTimeout(entry.timerId);\n      entry.callback();\n    });\n  };\n\n  return {\n    add: add,\n    flush: flush\n  };\n});\n\nvar areLocationsEqual = function areLocationsEqual(first, second) {\n  if (first == null && second == null) {\n    return true;\n  }\n\n  if (first == null || second == null) {\n    return false;\n  }\n\n  return first.droppableId === second.droppableId && first.index === second.index;\n};\nvar isCombineEqual = function isCombineEqual(first, second) {\n  if (first == null && second == null) {\n    return true;\n  }\n\n  if (first == null || second == null) {\n    return false;\n  }\n\n  return first.draggableId === second.draggableId && first.droppableId === second.droppableId;\n};\nvar isCriticalEqual = function isCriticalEqual(first, second) {\n  if (first === second) {\n    return true;\n  }\n\n  var isDraggableEqual = first.draggable.id === second.draggable.id && first.draggable.droppableId === second.draggable.droppableId && first.draggable.type === second.draggable.type && first.draggable.index === second.draggable.index;\n  var isDroppableEqual = first.droppable.id === second.droppable.id && first.droppable.type === second.droppable.type;\n  return isDraggableEqual && isDroppableEqual;\n};\n\nvar withTimings = function withTimings(key, fn) {\n  start();\n  fn();\n  finish();\n};\n\nvar getDragStart = function getDragStart(critical, mode) {\n  return {\n    draggableId: critical.draggable.id,\n    type: critical.droppable.type,\n    source: {\n      droppableId: critical.droppable.id,\n      index: critical.draggable.index\n    },\n    mode: mode\n  };\n};\n\nvar execute = function execute(responder, data, announce, getDefaultMessage) {\n  if (!responder) {\n    announce(getDefaultMessage(data));\n    return;\n  }\n\n  var willExpire = getExpiringAnnounce(announce);\n  var provided = {\n    announce: willExpire\n  };\n  responder(data, provided);\n\n  if (!willExpire.wasCalled()) {\n    announce(getDefaultMessage(data));\n  }\n};\n\nvar getPublisher = (function (getResponders, announce) {\n  var asyncMarshal = getAsyncMarshal();\n  var dragging = null;\n\n  var beforeCapture = function beforeCapture(draggableId, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeCapture as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeCapture', function () {\n      var fn = getResponders().onBeforeCapture;\n\n      if (fn) {\n        var before = {\n          draggableId: draggableId,\n          mode: mode\n        };\n        fn(before);\n      }\n    });\n  };\n\n  var beforeStart = function beforeStart(critical, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    withTimings('onBeforeDragStart', function () {\n      var fn = getResponders().onBeforeDragStart;\n\n      if (fn) {\n        fn(getDragStart(critical, mode));\n      }\n    });\n  };\n\n  var start = function start(critical, mode) {\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onBeforeDragStart as a drag start has already been published') : invariant(false) : void 0;\n    var data = getDragStart(critical, mode);\n    dragging = {\n      mode: mode,\n      lastCritical: critical,\n      lastLocation: data.source,\n      lastCombine: null\n    };\n    asyncMarshal.add(function () {\n      withTimings('onDragStart', function () {\n        return execute(getResponders().onDragStart, data, announce, preset.onDragStart);\n      });\n    });\n  };\n\n  var update = function update(critical, impact) {\n    var location = tryGetDestination(impact);\n    var combine = tryGetCombine(impact);\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragMove when onDragStart has not been called') : invariant(false) : void 0;\n    var hasCriticalChanged = !isCriticalEqual(critical, dragging.lastCritical);\n\n    if (hasCriticalChanged) {\n      dragging.lastCritical = critical;\n    }\n\n    var hasLocationChanged = !areLocationsEqual(dragging.lastLocation, location);\n\n    if (hasLocationChanged) {\n      dragging.lastLocation = location;\n    }\n\n    var hasGroupingChanged = !isCombineEqual(dragging.lastCombine, combine);\n\n    if (hasGroupingChanged) {\n      dragging.lastCombine = combine;\n    }\n\n    if (!hasCriticalChanged && !hasLocationChanged && !hasGroupingChanged) {\n      return;\n    }\n\n    var data = _extends({}, getDragStart(critical, dragging.mode), {\n      combine: combine,\n      destination: location\n    });\n\n    asyncMarshal.add(function () {\n      withTimings('onDragUpdate', function () {\n        return execute(getResponders().onDragUpdate, data, announce, preset.onDragUpdate);\n      });\n    });\n  };\n\n  var flush = function flush() {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only flush responders while dragging') : invariant(false) : void 0;\n    asyncMarshal.flush();\n  };\n\n  var drop = function drop(result) {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fire onDragEnd when there is no matching onDragStart') : invariant(false) : void 0;\n    dragging = null;\n    withTimings('onDragEnd', function () {\n      return execute(getResponders().onDragEnd, result, announce, preset.onDragEnd);\n    });\n  };\n\n  var abort = function abort() {\n    if (!dragging) {\n      return;\n    }\n\n    var result = _extends({}, getDragStart(dragging.lastCritical, dragging.mode), {\n      combine: null,\n      destination: null,\n      reason: 'CANCEL'\n    });\n\n    drop(result);\n  };\n\n  return {\n    beforeCapture: beforeCapture,\n    beforeStart: beforeStart,\n    start: start,\n    update: update,\n    flush: flush,\n    drop: drop,\n    abort: abort\n  };\n});\n\nvar responders = (function (getResponders, announce) {\n  var publisher = getPublisher(getResponders, announce);\n  return function (store) {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'BEFORE_INITIAL_CAPTURE') {\n          publisher.beforeCapture(action.payload.draggableId, action.payload.movementMode);\n          return;\n        }\n\n        if (action.type === 'INITIAL_PUBLISH') {\n          var critical = action.payload.critical;\n          publisher.beforeStart(critical, action.payload.movementMode);\n          next(action);\n          publisher.start(critical, action.payload.movementMode);\n          return;\n        }\n\n        if (action.type === 'DROP_COMPLETE') {\n          var result = action.payload.completed.result;\n          publisher.flush();\n          next(action);\n          publisher.drop(result);\n          return;\n        }\n\n        next(action);\n\n        if (action.type === 'FLUSH') {\n          publisher.abort();\n          return;\n        }\n\n        var state = store.getState();\n\n        if (state.phase === 'DRAGGING') {\n          publisher.update(state.critical, state.impact);\n        }\n      };\n    };\n  };\n});\n\nvar dropAnimationFinish = (function (store) {\n  return function (next) {\n    return function (action) {\n      if (action.type !== 'DROP_ANIMATION_FINISHED') {\n        next(action);\n        return;\n      }\n\n      var state = store.getState();\n      !(state.phase === 'DROP_ANIMATING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot finish a drop animating when no drop is occurring') : invariant(false) : void 0;\n      store.dispatch(completeDrop({\n        completed: state.completed\n      }));\n    };\n  };\n});\n\nvar dropAnimationFlushOnScroll = (function (store) {\n  var unbind = null;\n  var frameId = null;\n\n  function clear() {\n    if (frameId) {\n      cancelAnimationFrame(frameId);\n      frameId = null;\n    }\n\n    if (unbind) {\n      unbind();\n      unbind = null;\n    }\n  }\n\n  return function (next) {\n    return function (action) {\n      if (action.type === 'FLUSH' || action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATION_FINISHED') {\n        clear();\n      }\n\n      next(action);\n\n      if (action.type !== 'DROP_ANIMATE') {\n        return;\n      }\n\n      var binding = {\n        eventName: 'scroll',\n        options: {\n          capture: true,\n          passive: false,\n          once: true\n        },\n        fn: function flushDropAnimation() {\n          var state = store.getState();\n\n          if (state.phase === 'DROP_ANIMATING') {\n            store.dispatch(dropAnimationFinished());\n          }\n        }\n      };\n      frameId = requestAnimationFrame(function () {\n        frameId = null;\n        unbind = bindEvents(window, [binding]);\n      });\n    };\n  };\n});\n\nvar dimensionMarshalStopper = (function (marshal) {\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'DROP_COMPLETE' || action.type === 'FLUSH' || action.type === 'DROP_ANIMATE') {\n          marshal.stopPublishing();\n        }\n\n        next(action);\n      };\n    };\n  };\n});\n\nvar focus = (function (marshal) {\n  var isWatching = false;\n  return function () {\n    return function (next) {\n      return function (action) {\n        if (action.type === 'INITIAL_PUBLISH') {\n          isWatching = true;\n          marshal.tryRecordFocus(action.payload.critical.draggable.id);\n          next(action);\n          marshal.tryRestoreFocusRecorded();\n          return;\n        }\n\n        next(action);\n\n        if (!isWatching) {\n          return;\n        }\n\n        if (action.type === 'FLUSH') {\n          isWatching = false;\n          marshal.tryRestoreFocusRecorded();\n          return;\n        }\n\n        if (action.type === 'DROP_COMPLETE') {\n          isWatching = false;\n          var result = action.payload.completed.result;\n\n          if (result.combine) {\n            marshal.tryShiftRecord(result.draggableId, result.combine.draggableId);\n          }\n\n          marshal.tryRestoreFocusRecorded();\n        }\n      };\n    };\n  };\n});\n\nvar shouldStop = function shouldStop(action) {\n  return action.type === 'DROP_COMPLETE' || action.type === 'DROP_ANIMATE' || action.type === 'FLUSH';\n};\n\nvar autoScroll = (function (autoScroller) {\n  return function (store) {\n    return function (next) {\n      return function (action) {\n        if (shouldStop(action)) {\n          autoScroller.stop();\n          next(action);\n          return;\n        }\n\n        if (action.type === 'INITIAL_PUBLISH') {\n          next(action);\n          var state = store.getState();\n          !(state.phase === 'DRAGGING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected phase to be DRAGGING after INITIAL_PUBLISH') : invariant(false) : void 0;\n          autoScroller.start(state);\n          return;\n        }\n\n        next(action);\n        autoScroller.scroll(store.getState());\n      };\n    };\n  };\n});\n\nvar pendingDrop = (function (store) {\n  return function (next) {\n    return function (action) {\n      next(action);\n\n      if (action.type !== 'PUBLISH_WHILE_DRAGGING') {\n        return;\n      }\n\n      var postActionState = store.getState();\n\n      if (postActionState.phase !== 'DROP_PENDING') {\n        return;\n      }\n\n      if (postActionState.isWaiting) {\n        return;\n      }\n\n      store.dispatch(drop({\n        reason: postActionState.reason\n      }));\n    };\n  };\n});\n\nvar composeEnhancers = process.env.NODE_ENV !== 'production' && typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({\n  name: 'react-beautiful-dnd'\n}) : compose;\nvar createStore = (function (_ref) {\n  var dimensionMarshal = _ref.dimensionMarshal,\n      focusMarshal = _ref.focusMarshal,\n      styleMarshal = _ref.styleMarshal,\n      getResponders = _ref.getResponders,\n      announce = _ref.announce,\n      autoScroller = _ref.autoScroller;\n  return createStore$1(reducer, composeEnhancers(applyMiddleware(style(styleMarshal), dimensionMarshalStopper(dimensionMarshal), lift$1(dimensionMarshal), drop$1, dropAnimationFinish, dropAnimationFlushOnScroll, pendingDrop, autoScroll(autoScroller), scrollListener, focus(focusMarshal), responders(getResponders, announce))));\n});\n\nvar clean$1 = function clean() {\n  return {\n    additions: {},\n    removals: {},\n    modified: {}\n  };\n};\nfunction createPublisher(_ref) {\n  var registry = _ref.registry,\n      callbacks = _ref.callbacks;\n  var staging = clean$1();\n  var frameId = null;\n\n  var collect = function collect() {\n    if (frameId) {\n      return;\n    }\n\n    callbacks.collectionStarting();\n    frameId = requestAnimationFrame(function () {\n      frameId = null;\n      start();\n      var _staging = staging,\n          additions = _staging.additions,\n          removals = _staging.removals,\n          modified = _staging.modified;\n      var added = Object.keys(additions).map(function (id) {\n        return registry.draggable.getById(id).getDimension(origin);\n      }).sort(function (a, b) {\n        return a.descriptor.index - b.descriptor.index;\n      });\n      var updated = Object.keys(modified).map(function (id) {\n        var entry = registry.droppable.getById(id);\n        var scroll = entry.callbacks.getScrollWhileDragging();\n        return {\n          droppableId: id,\n          scroll: scroll\n        };\n      });\n      var result = {\n        additions: added,\n        removals: Object.keys(removals),\n        modified: updated\n      };\n      staging = clean$1();\n      finish();\n      callbacks.publish(result);\n    });\n  };\n\n  var add = function add(entry) {\n    var id = entry.descriptor.id;\n    staging.additions[id] = entry;\n    staging.modified[entry.descriptor.droppableId] = true;\n\n    if (staging.removals[id]) {\n      delete staging.removals[id];\n    }\n\n    collect();\n  };\n\n  var remove = function remove(entry) {\n    var descriptor = entry.descriptor;\n    staging.removals[descriptor.id] = true;\n    staging.modified[descriptor.droppableId] = true;\n\n    if (staging.additions[descriptor.id]) {\n      delete staging.additions[descriptor.id];\n    }\n\n    collect();\n  };\n\n  var stop = function stop() {\n    if (!frameId) {\n      return;\n    }\n\n    cancelAnimationFrame(frameId);\n    frameId = null;\n    staging = clean$1();\n  };\n\n  return {\n    add: add,\n    remove: remove,\n    stop: stop\n  };\n}\n\nvar getMaxScroll = (function (_ref) {\n  var scrollHeight = _ref.scrollHeight,\n      scrollWidth = _ref.scrollWidth,\n      height = _ref.height,\n      width = _ref.width;\n  var maxScroll = subtract({\n    x: scrollWidth,\n    y: scrollHeight\n  }, {\n    x: width,\n    y: height\n  });\n  var adjustedMaxScroll = {\n    x: Math.max(0, maxScroll.x),\n    y: Math.max(0, maxScroll.y)\n  };\n  return adjustedMaxScroll;\n});\n\nvar getDocumentElement = (function () {\n  var doc = document.documentElement;\n  !doc ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.documentElement') : invariant(false) : void 0;\n  return doc;\n});\n\nvar getMaxWindowScroll = (function () {\n  var doc = getDocumentElement();\n  var maxScroll = getMaxScroll({\n    scrollHeight: doc.scrollHeight,\n    scrollWidth: doc.scrollWidth,\n    width: doc.clientWidth,\n    height: doc.clientHeight\n  });\n  return maxScroll;\n});\n\nvar getViewport = (function () {\n  var scroll = getWindowScroll();\n  var maxScroll = getMaxWindowScroll();\n  var top = scroll.y;\n  var left = scroll.x;\n  var doc = getDocumentElement();\n  var width = doc.clientWidth;\n  var height = doc.clientHeight;\n  var right = left + width;\n  var bottom = top + height;\n  var frame = getRect({\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  });\n  var viewport = {\n    frame: frame,\n    scroll: {\n      initial: scroll,\n      current: scroll,\n      max: maxScroll,\n      diff: {\n        value: origin,\n        displacement: origin\n      }\n    }\n  };\n  return viewport;\n});\n\nvar getInitialPublish = (function (_ref) {\n  var critical = _ref.critical,\n      scrollOptions = _ref.scrollOptions,\n      registry = _ref.registry;\n  start();\n  var viewport = getViewport();\n  var windowScroll = viewport.scroll.current;\n  var home = critical.droppable;\n  var droppables = registry.droppable.getAllByType(home.type).map(function (entry) {\n    return entry.callbacks.getDimensionAndWatchScroll(windowScroll, scrollOptions);\n  });\n  var draggables = registry.draggable.getAllByType(critical.draggable.type).map(function (entry) {\n    return entry.getDimension(windowScroll);\n  });\n  var dimensions = {\n    draggables: toDraggableMap(draggables),\n    droppables: toDroppableMap(droppables)\n  };\n  finish();\n  var result = {\n    dimensions: dimensions,\n    critical: critical,\n    viewport: viewport\n  };\n  return result;\n});\n\nfunction shouldPublishUpdate(registry, dragging, entry) {\n  if (entry.descriptor.id === dragging.id) {\n    return false;\n  }\n\n  if (entry.descriptor.type !== dragging.type) {\n    return false;\n  }\n\n  var home = registry.droppable.getById(entry.descriptor.droppableId);\n\n  if (home.descriptor.mode !== 'virtual') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      You are attempting to add or remove a Draggable [id: \" + entry.descriptor.id + \"]\\n      while a drag is occurring. This is only supported for virtual lists.\\n\\n      See https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/patterns/virtual-lists.md\\n    \") : void 0;\n    return false;\n  }\n\n  return true;\n}\n\nvar createDimensionMarshal = (function (registry, callbacks) {\n  var collection = null;\n  var publisher = createPublisher({\n    callbacks: {\n      publish: callbacks.publishWhileDragging,\n      collectionStarting: callbacks.collectionStarting\n    },\n    registry: registry\n  });\n\n  var updateDroppableIsEnabled = function updateDroppableIsEnabled(id, isEnabled) {\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update is enabled flag of Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n\n    if (!collection) {\n      return;\n    }\n\n    callbacks.updateDroppableIsEnabled({\n      id: id,\n      isEnabled: isEnabled\n    });\n  };\n\n  var updateDroppableIsCombineEnabled = function updateDroppableIsCombineEnabled(id, isCombineEnabled) {\n    if (!collection) {\n      return;\n    }\n\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update isCombineEnabled flag of Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    callbacks.updateDroppableIsCombineEnabled({\n      id: id,\n      isCombineEnabled: isCombineEnabled\n    });\n  };\n\n  var updateDroppableScroll = function updateDroppableScroll(id, newScroll) {\n    if (!collection) {\n      return;\n    }\n\n    !registry.droppable.exists(id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot update the scroll on Droppable \" + id + \" as it is not registered\") : invariant(false) : void 0;\n    callbacks.updateDroppableScroll({\n      id: id,\n      newScroll: newScroll\n    });\n  };\n\n  var scrollDroppable = function scrollDroppable(id, change) {\n    if (!collection) {\n      return;\n    }\n\n    registry.droppable.getById(id).callbacks.scroll(change);\n  };\n\n  var stopPublishing = function stopPublishing() {\n    if (!collection) {\n      return;\n    }\n\n    publisher.stop();\n    var home = collection.critical.droppable;\n    registry.droppable.getAllByType(home.type).forEach(function (entry) {\n      return entry.callbacks.dragStopped();\n    });\n    collection.unsubscribe();\n    collection = null;\n  };\n\n  var subscriber = function subscriber(event) {\n    !collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Should only be subscribed when a collection is occurring') : invariant(false) : void 0;\n    var dragging = collection.critical.draggable;\n\n    if (event.type === 'ADDITION') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.add(event.value);\n      }\n    }\n\n    if (event.type === 'REMOVAL') {\n      if (shouldPublishUpdate(registry, dragging, event.value)) {\n        publisher.remove(event.value);\n      }\n    }\n  };\n\n  var startPublishing = function startPublishing(request) {\n    !!collection ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start capturing critical dimensions as there is already a collection') : invariant(false) : void 0;\n    var entry = registry.draggable.getById(request.draggableId);\n    var home = registry.droppable.getById(entry.descriptor.droppableId);\n    var critical = {\n      draggable: entry.descriptor,\n      droppable: home.descriptor\n    };\n    var unsubscribe = registry.subscribe(subscriber);\n    collection = {\n      critical: critical,\n      unsubscribe: unsubscribe\n    };\n    return getInitialPublish({\n      critical: critical,\n      registry: registry,\n      scrollOptions: request.scrollOptions\n    });\n  };\n\n  var marshal = {\n    updateDroppableIsEnabled: updateDroppableIsEnabled,\n    updateDroppableIsCombineEnabled: updateDroppableIsCombineEnabled,\n    scrollDroppable: scrollDroppable,\n    updateDroppableScroll: updateDroppableScroll,\n    startPublishing: startPublishing,\n    stopPublishing: stopPublishing\n  };\n  return marshal;\n});\n\nvar canStartDrag = (function (state, id) {\n  if (state.phase === 'IDLE') {\n    return true;\n  }\n\n  if (state.phase !== 'DROP_ANIMATING') {\n    return false;\n  }\n\n  if (state.completed.result.draggableId === id) {\n    return false;\n  }\n\n  return state.completed.result.reason === 'DROP';\n});\n\nvar scrollWindow = (function (change) {\n  window.scrollBy(change.x, change.y);\n});\n\nvar getScrollableDroppables = memoizeOne(function (droppables) {\n  return toDroppableList(droppables).filter(function (droppable) {\n    if (!droppable.isEnabled) {\n      return false;\n    }\n\n    if (!droppable.frame) {\n      return false;\n    }\n\n    return true;\n  });\n});\n\nvar getScrollableDroppableOver = function getScrollableDroppableOver(target, droppables) {\n  var maybe = find(getScrollableDroppables(droppables), function (droppable) {\n    !droppable.frame ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Invalid result') : invariant(false) : void 0;\n    return isPositionInFrame(droppable.frame.pageMarginBox)(target);\n  });\n  return maybe;\n};\n\nvar getBestScrollableDroppable = (function (_ref) {\n  var center = _ref.center,\n      destination = _ref.destination,\n      droppables = _ref.droppables;\n\n  if (destination) {\n    var _dimension = droppables[destination];\n\n    if (!_dimension.frame) {\n      return null;\n    }\n\n    return _dimension;\n  }\n\n  var dimension = getScrollableDroppableOver(center, droppables);\n  return dimension;\n});\n\nvar config = {\n  startFromPercentage: 0.25,\n  maxScrollAtPercentage: 0.05,\n  maxPixelScroll: 28,\n  ease: function ease(percentage) {\n    return Math.pow(percentage, 2);\n  },\n  durationDampening: {\n    stopDampeningAt: 1200,\n    accelerateAt: 360\n  }\n};\n\nvar getDistanceThresholds = (function (container, axis) {\n  var startScrollingFrom = container[axis.size] * config.startFromPercentage;\n  var maxScrollValueAt = container[axis.size] * config.maxScrollAtPercentage;\n  var thresholds = {\n    startScrollingFrom: startScrollingFrom,\n    maxScrollValueAt: maxScrollValueAt\n  };\n  return thresholds;\n});\n\nvar getPercentage = (function (_ref) {\n  var startOfRange = _ref.startOfRange,\n      endOfRange = _ref.endOfRange,\n      current = _ref.current;\n  var range = endOfRange - startOfRange;\n\n  if (range === 0) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Detected distance range of 0 in the fluid auto scroller\\n      This is unexpected and would cause a divide by 0 issue.\\n      Not allowing an auto scroll\\n    \") : void 0;\n    return 0;\n  }\n\n  var currentInRange = current - startOfRange;\n  var percentage = currentInRange / range;\n  return percentage;\n});\n\nvar minScroll = 1;\n\nvar getValueFromDistance = (function (distanceToEdge, thresholds) {\n  if (distanceToEdge > thresholds.startScrollingFrom) {\n    return 0;\n  }\n\n  if (distanceToEdge <= thresholds.maxScrollValueAt) {\n    return config.maxPixelScroll;\n  }\n\n  if (distanceToEdge === thresholds.startScrollingFrom) {\n    return minScroll;\n  }\n\n  var percentageFromMaxScrollValueAt = getPercentage({\n    startOfRange: thresholds.maxScrollValueAt,\n    endOfRange: thresholds.startScrollingFrom,\n    current: distanceToEdge\n  });\n  var percentageFromStartScrollingFrom = 1 - percentageFromMaxScrollValueAt;\n  var scroll = config.maxPixelScroll * config.ease(percentageFromStartScrollingFrom);\n  return Math.ceil(scroll);\n});\n\nvar accelerateAt = config.durationDampening.accelerateAt;\nvar stopAt = config.durationDampening.stopDampeningAt;\nvar dampenValueByTime = (function (proposedScroll, dragStartTime) {\n  var startOfRange = dragStartTime;\n  var endOfRange = stopAt;\n  var now = Date.now();\n  var runTime = now - startOfRange;\n\n  if (runTime >= stopAt) {\n    return proposedScroll;\n  }\n\n  if (runTime < accelerateAt) {\n    return minScroll;\n  }\n\n  var betweenAccelerateAtAndStopAtPercentage = getPercentage({\n    startOfRange: accelerateAt,\n    endOfRange: endOfRange,\n    current: runTime\n  });\n  var scroll = proposedScroll * config.ease(betweenAccelerateAtAndStopAtPercentage);\n  return Math.ceil(scroll);\n});\n\nvar getValue = (function (_ref) {\n  var distanceToEdge = _ref.distanceToEdge,\n      thresholds = _ref.thresholds,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var scroll = getValueFromDistance(distanceToEdge, thresholds);\n\n  if (scroll === 0) {\n    return 0;\n  }\n\n  if (!shouldUseTimeDampening) {\n    return scroll;\n  }\n\n  return Math.max(dampenValueByTime(scroll, dragStartTime), minScroll);\n});\n\nvar getScrollOnAxis = (function (_ref) {\n  var container = _ref.container,\n      distanceToEdges = _ref.distanceToEdges,\n      dragStartTime = _ref.dragStartTime,\n      axis = _ref.axis,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var thresholds = getDistanceThresholds(container, axis);\n  var isCloserToEnd = distanceToEdges[axis.end] < distanceToEdges[axis.start];\n\n  if (isCloserToEnd) {\n    return getValue({\n      distanceToEdge: distanceToEdges[axis.end],\n      thresholds: thresholds,\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n  }\n\n  return -1 * getValue({\n    distanceToEdge: distanceToEdges[axis.start],\n    thresholds: thresholds,\n    dragStartTime: dragStartTime,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n});\n\nvar adjustForSizeLimits = (function (_ref) {\n  var container = _ref.container,\n      subject = _ref.subject,\n      proposedScroll = _ref.proposedScroll;\n  var isTooBigVertically = subject.height > container.height;\n  var isTooBigHorizontally = subject.width > container.width;\n\n  if (!isTooBigHorizontally && !isTooBigVertically) {\n    return proposedScroll;\n  }\n\n  if (isTooBigHorizontally && isTooBigVertically) {\n    return null;\n  }\n\n  return {\n    x: isTooBigHorizontally ? 0 : proposedScroll.x,\n    y: isTooBigVertically ? 0 : proposedScroll.y\n  };\n});\n\nvar clean$2 = apply(function (value) {\n  return value === 0 ? 0 : value;\n});\nvar getScroll = (function (_ref) {\n  var dragStartTime = _ref.dragStartTime,\n      container = _ref.container,\n      subject = _ref.subject,\n      center = _ref.center,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var distanceToEdges = {\n    top: center.y - container.top,\n    right: container.right - center.x,\n    bottom: container.bottom - center.y,\n    left: center.x - container.left\n  };\n  var y = getScrollOnAxis({\n    container: container,\n    distanceToEdges: distanceToEdges,\n    dragStartTime: dragStartTime,\n    axis: vertical,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  var x = getScrollOnAxis({\n    container: container,\n    distanceToEdges: distanceToEdges,\n    dragStartTime: dragStartTime,\n    axis: horizontal,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  var required = clean$2({\n    x: x,\n    y: y\n  });\n\n  if (isEqual(required, origin)) {\n    return null;\n  }\n\n  var limited = adjustForSizeLimits({\n    container: container,\n    subject: subject,\n    proposedScroll: required\n  });\n\n  if (!limited) {\n    return null;\n  }\n\n  return isEqual(limited, origin) ? null : limited;\n});\n\nvar smallestSigned = apply(function (value) {\n  if (value === 0) {\n    return 0;\n  }\n\n  return value > 0 ? 1 : -1;\n});\nvar getOverlap = function () {\n  var getRemainder = function getRemainder(target, max) {\n    if (target < 0) {\n      return target;\n    }\n\n    if (target > max) {\n      return target - max;\n    }\n\n    return 0;\n  };\n\n  return function (_ref) {\n    var current = _ref.current,\n        max = _ref.max,\n        change = _ref.change;\n    var targetScroll = add(current, change);\n    var overlap = {\n      x: getRemainder(targetScroll.x, max.x),\n      y: getRemainder(targetScroll.y, max.y)\n    };\n\n    if (isEqual(overlap, origin)) {\n      return null;\n    }\n\n    return overlap;\n  };\n}();\nvar canPartiallyScroll = function canPartiallyScroll(_ref2) {\n  var rawMax = _ref2.max,\n      current = _ref2.current,\n      change = _ref2.change;\n  var max = {\n    x: Math.max(current.x, rawMax.x),\n    y: Math.max(current.y, rawMax.y)\n  };\n  var smallestChange = smallestSigned(change);\n  var overlap = getOverlap({\n    max: max,\n    current: current,\n    change: smallestChange\n  });\n\n  if (!overlap) {\n    return true;\n  }\n\n  if (smallestChange.x !== 0 && overlap.x === 0) {\n    return true;\n  }\n\n  if (smallestChange.y !== 0 && overlap.y === 0) {\n    return true;\n  }\n\n  return false;\n};\nvar canScrollWindow = function canScrollWindow(viewport, change) {\n  return canPartiallyScroll({\n    current: viewport.scroll.current,\n    max: viewport.scroll.max,\n    change: change\n  });\n};\nvar getWindowOverlap = function getWindowOverlap(viewport, change) {\n  if (!canScrollWindow(viewport, change)) {\n    return null;\n  }\n\n  var max = viewport.scroll.max;\n  var current = viewport.scroll.current;\n  return getOverlap({\n    current: current,\n    max: max,\n    change: change\n  });\n};\nvar canScrollDroppable = function canScrollDroppable(droppable, change) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return false;\n  }\n\n  return canPartiallyScroll({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change: change\n  });\n};\nvar getDroppableOverlap = function getDroppableOverlap(droppable, change) {\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return null;\n  }\n\n  if (!canScrollDroppable(droppable, change)) {\n    return null;\n  }\n\n  return getOverlap({\n    current: frame.scroll.current,\n    max: frame.scroll.max,\n    change: change\n  });\n};\n\nvar getWindowScrollChange = (function (_ref) {\n  var viewport = _ref.viewport,\n      subject = _ref.subject,\n      center = _ref.center,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var scroll = getScroll({\n    dragStartTime: dragStartTime,\n    container: viewport.frame,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  return scroll && canScrollWindow(viewport, scroll) ? scroll : null;\n});\n\nvar getDroppableScrollChange = (function (_ref) {\n  var droppable = _ref.droppable,\n      subject = _ref.subject,\n      center = _ref.center,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening;\n  var frame = droppable.frame;\n\n  if (!frame) {\n    return null;\n  }\n\n  var scroll = getScroll({\n    dragStartTime: dragStartTime,\n    container: frame.pageMarginBox,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n  return scroll && canScrollDroppable(droppable, scroll) ? scroll : null;\n});\n\nvar scroll$1 = (function (_ref) {\n  var state = _ref.state,\n      dragStartTime = _ref.dragStartTime,\n      shouldUseTimeDampening = _ref.shouldUseTimeDampening,\n      scrollWindow = _ref.scrollWindow,\n      scrollDroppable = _ref.scrollDroppable;\n  var center = state.current.page.borderBoxCenter;\n  var draggable = state.dimensions.draggables[state.critical.draggable.id];\n  var subject = draggable.page.marginBox;\n\n  if (state.isWindowScrollAllowed) {\n    var viewport = state.viewport;\n\n    var _change = getWindowScrollChange({\n      dragStartTime: dragStartTime,\n      viewport: viewport,\n      subject: subject,\n      center: center,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n\n    if (_change) {\n      scrollWindow(_change);\n      return;\n    }\n  }\n\n  var droppable = getBestScrollableDroppable({\n    center: center,\n    destination: whatIsDraggedOver(state.impact),\n    droppables: state.dimensions.droppables\n  });\n\n  if (!droppable) {\n    return;\n  }\n\n  var change = getDroppableScrollChange({\n    dragStartTime: dragStartTime,\n    droppable: droppable,\n    subject: subject,\n    center: center,\n    shouldUseTimeDampening: shouldUseTimeDampening\n  });\n\n  if (change) {\n    scrollDroppable(droppable.descriptor.id, change);\n  }\n});\n\nvar createFluidScroller = (function (_ref) {\n  var scrollWindow = _ref.scrollWindow,\n      scrollDroppable = _ref.scrollDroppable;\n  var scheduleWindowScroll = rafSchd(scrollWindow);\n  var scheduleDroppableScroll = rafSchd(scrollDroppable);\n  var dragging = null;\n\n  var tryScroll = function tryScroll(state) {\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot fluid scroll if not dragging') : invariant(false) : void 0;\n    var _dragging = dragging,\n        shouldUseTimeDampening = _dragging.shouldUseTimeDampening,\n        dragStartTime = _dragging.dragStartTime;\n    scroll$1({\n      state: state,\n      scrollWindow: scheduleWindowScroll,\n      scrollDroppable: scheduleDroppableScroll,\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: shouldUseTimeDampening\n    });\n  };\n\n  var start$1 = function start$1(state) {\n    start();\n    !!dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot start auto scrolling when already started') : invariant(false) : void 0;\n    var dragStartTime = Date.now();\n    var wasScrollNeeded = false;\n\n    var fakeScrollCallback = function fakeScrollCallback() {\n      wasScrollNeeded = true;\n    };\n\n    scroll$1({\n      state: state,\n      dragStartTime: 0,\n      shouldUseTimeDampening: false,\n      scrollWindow: fakeScrollCallback,\n      scrollDroppable: fakeScrollCallback\n    });\n    dragging = {\n      dragStartTime: dragStartTime,\n      shouldUseTimeDampening: wasScrollNeeded\n    };\n    finish();\n\n    if (wasScrollNeeded) {\n      tryScroll(state);\n    }\n  };\n\n  var stop = function stop() {\n    if (!dragging) {\n      return;\n    }\n\n    scheduleWindowScroll.cancel();\n    scheduleDroppableScroll.cancel();\n    dragging = null;\n  };\n\n  return {\n    start: start$1,\n    stop: stop,\n    scroll: tryScroll\n  };\n});\n\nvar createJumpScroller = (function (_ref) {\n  var move = _ref.move,\n      scrollDroppable = _ref.scrollDroppable,\n      scrollWindow = _ref.scrollWindow;\n\n  var moveByOffset = function moveByOffset(state, offset) {\n    var client = add(state.current.client.selection, offset);\n    move({\n      client: client\n    });\n  };\n\n  var scrollDroppableAsMuchAsItCan = function scrollDroppableAsMuchAsItCan(droppable, change) {\n    if (!canScrollDroppable(droppable, change)) {\n      return change;\n    }\n\n    var overlap = getDroppableOverlap(droppable, change);\n\n    if (!overlap) {\n      scrollDroppable(droppable.descriptor.id, change);\n      return null;\n    }\n\n    var whatTheDroppableCanScroll = subtract(change, overlap);\n    scrollDroppable(droppable.descriptor.id, whatTheDroppableCanScroll);\n    var remainder = subtract(change, whatTheDroppableCanScroll);\n    return remainder;\n  };\n\n  var scrollWindowAsMuchAsItCan = function scrollWindowAsMuchAsItCan(isWindowScrollAllowed, viewport, change) {\n    if (!isWindowScrollAllowed) {\n      return change;\n    }\n\n    if (!canScrollWindow(viewport, change)) {\n      return change;\n    }\n\n    var overlap = getWindowOverlap(viewport, change);\n\n    if (!overlap) {\n      scrollWindow(change);\n      return null;\n    }\n\n    var whatTheWindowCanScroll = subtract(change, overlap);\n    scrollWindow(whatTheWindowCanScroll);\n    var remainder = subtract(change, whatTheWindowCanScroll);\n    return remainder;\n  };\n\n  var jumpScroller = function jumpScroller(state) {\n    var request = state.scrollJumpRequest;\n\n    if (!request) {\n      return;\n    }\n\n    var destination = whatIsDraggedOver(state.impact);\n    !destination ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot perform a jump scroll when there is no destination') : invariant(false) : void 0;\n    var droppableRemainder = scrollDroppableAsMuchAsItCan(state.dimensions.droppables[destination], request);\n\n    if (!droppableRemainder) {\n      return;\n    }\n\n    var viewport = state.viewport;\n    var windowRemainder = scrollWindowAsMuchAsItCan(state.isWindowScrollAllowed, viewport, droppableRemainder);\n\n    if (!windowRemainder) {\n      return;\n    }\n\n    moveByOffset(state, windowRemainder);\n  };\n\n  return jumpScroller;\n});\n\nvar createAutoScroller = (function (_ref) {\n  var scrollDroppable = _ref.scrollDroppable,\n      scrollWindow = _ref.scrollWindow,\n      move = _ref.move;\n  var fluidScroller = createFluidScroller({\n    scrollWindow: scrollWindow,\n    scrollDroppable: scrollDroppable\n  });\n  var jumpScroll = createJumpScroller({\n    move: move,\n    scrollWindow: scrollWindow,\n    scrollDroppable: scrollDroppable\n  });\n\n  var scroll = function scroll(state) {\n    if (state.phase !== 'DRAGGING') {\n      return;\n    }\n\n    if (state.movementMode === 'FLUID') {\n      fluidScroller.scroll(state);\n      return;\n    }\n\n    if (!state.scrollJumpRequest) {\n      return;\n    }\n\n    jumpScroll(state);\n  };\n\n  var scroller = {\n    scroll: scroll,\n    start: fluidScroller.start,\n    stop: fluidScroller.stop\n  };\n  return scroller;\n});\n\nvar prefix$1 = 'data-rbd';\nvar dragHandle = function () {\n  var base = prefix$1 + \"-drag-handle\";\n  return {\n    base: base,\n    draggableId: base + \"-draggable-id\",\n    contextId: base + \"-context-id\"\n  };\n}();\nvar draggable = function () {\n  var base = prefix$1 + \"-draggable\";\n  return {\n    base: base,\n    contextId: base + \"-context-id\",\n    id: base + \"-id\"\n  };\n}();\nvar droppable = function () {\n  var base = prefix$1 + \"-droppable\";\n  return {\n    base: base,\n    contextId: base + \"-context-id\",\n    id: base + \"-id\"\n  };\n}();\nvar scrollContainer = {\n  contextId: prefix$1 + \"-scroll-container-context-id\"\n};\n\nvar makeGetSelector = function makeGetSelector(context) {\n  return function (attribute) {\n    return \"[\" + attribute + \"=\\\"\" + context + \"\\\"]\";\n  };\n};\n\nvar getStyles = function getStyles(rules, property) {\n  return rules.map(function (rule) {\n    var value = rule.styles[property];\n\n    if (!value) {\n      return '';\n    }\n\n    return rule.selector + \" { \" + value + \" }\";\n  }).join(' ');\n};\n\nvar noPointerEvents = 'pointer-events: none;';\nvar getStyles$1 = (function (contextId) {\n  var getSelector = makeGetSelector(contextId);\n\n  var dragHandle$1 = function () {\n    var grabCursor = \"\\n      cursor: -webkit-grab;\\n      cursor: grab;\\n    \";\n    return {\n      selector: getSelector(dragHandle.contextId),\n      styles: {\n        always: \"\\n          -webkit-touch-callout: none;\\n          -webkit-tap-highlight-color: rgba(0,0,0,0);\\n          touch-action: manipulation;\\n        \",\n        resting: grabCursor,\n        dragging: noPointerEvents,\n        dropAnimating: grabCursor\n      }\n    };\n  }();\n\n  var draggable$1 = function () {\n    var transition = \"\\n      transition: \" + transitions.outOfTheWay + \";\\n    \";\n    return {\n      selector: getSelector(draggable.contextId),\n      styles: {\n        dragging: transition,\n        dropAnimating: transition,\n        userCancel: transition\n      }\n    };\n  }();\n\n  var droppable$1 = {\n    selector: getSelector(droppable.contextId),\n    styles: {\n      always: \"overflow-anchor: none;\"\n    }\n  };\n  var body = {\n    selector: 'body',\n    styles: {\n      dragging: \"\\n        cursor: grabbing;\\n        cursor: -webkit-grabbing;\\n        user-select: none;\\n        -webkit-user-select: none;\\n        -moz-user-select: none;\\n        -ms-user-select: none;\\n        overflow-anchor: none;\\n      \"\n    }\n  };\n  var rules = [draggable$1, dragHandle$1, droppable$1, body];\n  return {\n    always: getStyles(rules, 'always'),\n    resting: getStyles(rules, 'resting'),\n    dragging: getStyles(rules, 'dragging'),\n    dropAnimating: getStyles(rules, 'dropAnimating'),\n    userCancel: getStyles(rules, 'userCancel')\n  };\n});\n\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined' ? useLayoutEffect : useEffect;\n\nvar getHead = function getHead() {\n  var head = document.querySelector('head');\n  !head ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find the head to append a style to') : invariant(false) : void 0;\n  return head;\n};\n\nvar createStyleEl = function createStyleEl(nonce) {\n  var el = document.createElement('style');\n\n  if (nonce) {\n    el.setAttribute('nonce', nonce);\n  }\n\n  el.type = 'text/css';\n  return el;\n};\n\nfunction useStyleMarshal(contextId, nonce) {\n  var styles = useMemo(function () {\n    return getStyles$1(contextId);\n  }, [contextId]);\n  var alwaysRef = useRef(null);\n  var dynamicRef = useRef(null);\n  var setDynamicStyle = useCallback(memoizeOne(function (proposed) {\n    var el = dynamicRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }), []);\n  var setAlwaysStyle = useCallback(function (proposed) {\n    var el = alwaysRef.current;\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot set dynamic style element if it is not set') : invariant(false) : void 0;\n    el.textContent = proposed;\n  }, []);\n  useIsomorphicLayoutEffect(function () {\n    !(!alwaysRef.current && !dynamicRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'style elements already mounted') : invariant(false) : void 0;\n    var always = createStyleEl(nonce);\n    var dynamic = createStyleEl(nonce);\n    alwaysRef.current = always;\n    dynamicRef.current = dynamic;\n    always.setAttribute(prefix$1 + \"-always\", contextId);\n    dynamic.setAttribute(prefix$1 + \"-dynamic\", contextId);\n    getHead().appendChild(always);\n    getHead().appendChild(dynamic);\n    setAlwaysStyle(styles.always);\n    setDynamicStyle(styles.resting);\n    return function () {\n      var remove = function remove(ref) {\n        var current = ref.current;\n        !current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot unmount ref as it is not set') : invariant(false) : void 0;\n        getHead().removeChild(current);\n        ref.current = null;\n      };\n\n      remove(alwaysRef);\n      remove(dynamicRef);\n    };\n  }, [nonce, setAlwaysStyle, setDynamicStyle, styles.always, styles.resting, contextId]);\n  var dragging = useCallback(function () {\n    return setDynamicStyle(styles.dragging);\n  }, [setDynamicStyle, styles.dragging]);\n  var dropping = useCallback(function (reason) {\n    if (reason === 'DROP') {\n      setDynamicStyle(styles.dropAnimating);\n      return;\n    }\n\n    setDynamicStyle(styles.userCancel);\n  }, [setDynamicStyle, styles.dropAnimating, styles.userCancel]);\n  var resting = useCallback(function () {\n    if (!dynamicRef.current) {\n      return;\n    }\n\n    setDynamicStyle(styles.resting);\n  }, [setDynamicStyle, styles.resting]);\n  var marshal = useMemo(function () {\n    return {\n      dragging: dragging,\n      dropping: dropping,\n      resting: resting\n    };\n  }, [dragging, dropping, resting]);\n  return marshal;\n}\n\nvar getWindowFromEl = (function (el) {\n  return el && el.ownerDocument ? el.ownerDocument.defaultView : window;\n});\n\nfunction isHtmlElement(el) {\n  return el instanceof getWindowFromEl(el).HTMLElement;\n}\n\nfunction findDragHandle(contextId, draggableId) {\n  var selector = \"[\" + dragHandle.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n  var possible = toArray(document.querySelectorAll(selector));\n\n  if (!possible.length) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find any drag handles in the context \\\"\" + contextId + \"\\\"\") : void 0;\n    return null;\n  }\n\n  var handle = find(possible, function (el) {\n    return el.getAttribute(dragHandle.draggableId) === draggableId;\n  });\n\n  if (!handle) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find drag handle with id \\\"\" + draggableId + \"\\\" as no handle with a matching id was found\") : void 0;\n    return null;\n  }\n\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle needs to be a HTMLElement') : void 0;\n    return null;\n  }\n\n  return handle;\n}\n\nfunction useFocusMarshal(contextId) {\n  var entriesRef = useRef({});\n  var recordRef = useRef(null);\n  var restoreFocusFrameRef = useRef(null);\n  var isMountedRef = useRef(false);\n  var register = useCallback(function register(id, focus) {\n    var entry = {\n      id: id,\n      focus: focus\n    };\n    entriesRef.current[id] = entry;\n    return function unregister() {\n      var entries = entriesRef.current;\n      var current = entries[id];\n\n      if (current !== entry) {\n        delete entries[id];\n      }\n    };\n  }, []);\n  var tryGiveFocus = useCallback(function tryGiveFocus(tryGiveFocusTo) {\n    var handle = findDragHandle(contextId, tryGiveFocusTo);\n\n    if (handle && handle !== document.activeElement) {\n      handle.focus();\n    }\n  }, [contextId]);\n  var tryShiftRecord = useCallback(function tryShiftRecord(previous, redirectTo) {\n    if (recordRef.current === previous) {\n      recordRef.current = redirectTo;\n    }\n  }, []);\n  var tryRestoreFocusRecorded = useCallback(function tryRestoreFocusRecorded() {\n    if (restoreFocusFrameRef.current) {\n      return;\n    }\n\n    if (!isMountedRef.current) {\n      return;\n    }\n\n    restoreFocusFrameRef.current = requestAnimationFrame(function () {\n      restoreFocusFrameRef.current = null;\n      var record = recordRef.current;\n\n      if (record) {\n        tryGiveFocus(record);\n      }\n    });\n  }, [tryGiveFocus]);\n  var tryRecordFocus = useCallback(function tryRecordFocus(id) {\n    recordRef.current = null;\n    var focused = document.activeElement;\n\n    if (!focused) {\n      return;\n    }\n\n    if (focused.getAttribute(dragHandle.draggableId) !== id) {\n      return;\n    }\n\n    recordRef.current = id;\n  }, []);\n  useIsomorphicLayoutEffect(function () {\n    isMountedRef.current = true;\n    return function clearFrameOnUnmount() {\n      isMountedRef.current = false;\n      var frameId = restoreFocusFrameRef.current;\n\n      if (frameId) {\n        cancelAnimationFrame(frameId);\n      }\n    };\n  }, []);\n  var marshal = useMemo(function () {\n    return {\n      register: register,\n      tryRecordFocus: tryRecordFocus,\n      tryRestoreFocusRecorded: tryRestoreFocusRecorded,\n      tryShiftRecord: tryShiftRecord\n    };\n  }, [register, tryRecordFocus, tryRestoreFocusRecorded, tryShiftRecord]);\n  return marshal;\n}\n\nfunction createRegistry() {\n  var entries = {\n    draggables: {},\n    droppables: {}\n  };\n  var subscribers = [];\n\n  function subscribe(cb) {\n    subscribers.push(cb);\n    return function unsubscribe() {\n      var index = subscribers.indexOf(cb);\n\n      if (index === -1) {\n        return;\n      }\n\n      subscribers.splice(index, 1);\n    };\n  }\n\n  function notify(event) {\n    if (subscribers.length) {\n      subscribers.forEach(function (cb) {\n        return cb(event);\n      });\n    }\n  }\n\n  function findDraggableById(id) {\n    return entries.draggables[id] || null;\n  }\n\n  function getDraggableById(id) {\n    var entry = findDraggableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find draggable entry with id [\" + id + \"]\") : invariant(false) : void 0;\n    return entry;\n  }\n\n  var draggableAPI = {\n    register: function register(entry) {\n      entries.draggables[entry.descriptor.id] = entry;\n      notify({\n        type: 'ADDITION',\n        value: entry\n      });\n    },\n    update: function update(entry, last) {\n      var current = entries.draggables[last.descriptor.id];\n\n      if (!current) {\n        return;\n      }\n\n      if (current.uniqueId !== entry.uniqueId) {\n        return;\n      }\n\n      delete entries.draggables[last.descriptor.id];\n      entries.draggables[entry.descriptor.id] = entry;\n    },\n    unregister: function unregister(entry) {\n      var draggableId = entry.descriptor.id;\n      var current = findDraggableById(draggableId);\n\n      if (!current) {\n        return;\n      }\n\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n\n      delete entries.draggables[draggableId];\n      notify({\n        type: 'REMOVAL',\n        value: entry\n      });\n    },\n    getById: getDraggableById,\n    findById: findDraggableById,\n    exists: function exists(id) {\n      return Boolean(findDraggableById(id));\n    },\n    getAllByType: function getAllByType(type) {\n      return values(entries.draggables).filter(function (entry) {\n        return entry.descriptor.type === type;\n      });\n    }\n  };\n\n  function findDroppableById(id) {\n    return entries.droppables[id] || null;\n  }\n\n  function getDroppableById(id) {\n    var entry = findDroppableById(id);\n    !entry ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot find droppable entry with id [\" + id + \"]\") : invariant(false) : void 0;\n    return entry;\n  }\n\n  var droppableAPI = {\n    register: function register(entry) {\n      entries.droppables[entry.descriptor.id] = entry;\n    },\n    unregister: function unregister(entry) {\n      var current = findDroppableById(entry.descriptor.id);\n\n      if (!current) {\n        return;\n      }\n\n      if (entry.uniqueId !== current.uniqueId) {\n        return;\n      }\n\n      delete entries.droppables[entry.descriptor.id];\n    },\n    getById: getDroppableById,\n    findById: findDroppableById,\n    exists: function exists(id) {\n      return Boolean(findDroppableById(id));\n    },\n    getAllByType: function getAllByType(type) {\n      return values(entries.droppables).filter(function (entry) {\n        return entry.descriptor.type === type;\n      });\n    }\n  };\n\n  function clean() {\n    entries.draggables = {};\n    entries.droppables = {};\n    subscribers.length = 0;\n  }\n\n  return {\n    draggable: draggableAPI,\n    droppable: droppableAPI,\n    subscribe: subscribe,\n    clean: clean\n  };\n}\n\nfunction useRegistry() {\n  var registry = useMemo(createRegistry, []);\n  useEffect(function () {\n    return function unmount() {\n      requestAnimationFrame(registry.clean);\n    };\n  }, [registry]);\n  return registry;\n}\n\nvar StoreContext = React.createContext(null);\n\nvar getBodyElement = (function () {\n  var body = document.body;\n  !body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot find document.body') : invariant(false) : void 0;\n  return body;\n});\n\nvar visuallyHidden = {\n  position: 'absolute',\n  width: '1px',\n  height: '1px',\n  margin: '-1px',\n  border: '0',\n  padding: '0',\n  overflow: 'hidden',\n  clip: 'rect(0 0 0 0)',\n  'clip-path': 'inset(100%)'\n};\n\nvar getId = function getId(contextId) {\n  return \"rbd-announcement-\" + contextId;\n};\nfunction useAnnouncer(contextId) {\n  var id = useMemo(function () {\n    return getId(contextId);\n  }, [contextId]);\n  var ref = useRef(null);\n  useEffect(function setup() {\n    var el = document.createElement('div');\n    ref.current = el;\n    el.id = id;\n    el.setAttribute('aria-live', 'assertive');\n    el.setAttribute('aria-atomic', 'true');\n\n    _extends(el.style, visuallyHidden);\n\n    getBodyElement().appendChild(el);\n    return function cleanup() {\n      setTimeout(function remove() {\n        var body = getBodyElement();\n\n        if (body.contains(el)) {\n          body.removeChild(el);\n        }\n\n        if (el === ref.current) {\n          ref.current = null;\n        }\n      });\n    };\n  }, [id]);\n  var announce = useCallback(function (message) {\n    var el = ref.current;\n\n    if (el) {\n      el.textContent = message;\n      return;\n    }\n\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      A screen reader message was trying to be announced but it was unable to do so.\\n      This can occur if you unmount your <DragDropContext /> in your onDragEnd.\\n      Consider calling provided.announce() before the unmount so that the instruction will\\n      not be lost for users relying on a screen reader.\\n\\n      Message not passed to screen reader:\\n\\n      \\\"\" + message + \"\\\"\\n    \") : void 0;\n  }, []);\n  return announce;\n}\n\nvar count = 0;\nvar defaults = {\n  separator: '::'\n};\nfunction reset() {\n  count = 0;\n}\nfunction useUniqueId(prefix, options) {\n  if (options === void 0) {\n    options = defaults;\n  }\n\n  return useMemo(function () {\n    return \"\" + prefix + options.separator + count++;\n  }, [options.separator, prefix]);\n}\n\nfunction getElementId(_ref) {\n  var contextId = _ref.contextId,\n      uniqueId = _ref.uniqueId;\n  return \"rbd-hidden-text-\" + contextId + \"-\" + uniqueId;\n}\nfunction useHiddenTextElement(_ref2) {\n  var contextId = _ref2.contextId,\n      text = _ref2.text;\n  var uniqueId = useUniqueId('hidden-text', {\n    separator: '-'\n  });\n  var id = useMemo(function () {\n    return getElementId({\n      contextId: contextId,\n      uniqueId: uniqueId\n    });\n  }, [uniqueId, contextId]);\n  useEffect(function mount() {\n    var el = document.createElement('div');\n    el.id = id;\n    el.textContent = text;\n    el.style.display = 'none';\n    getBodyElement().appendChild(el);\n    return function unmount() {\n      var body = getBodyElement();\n\n      if (body.contains(el)) {\n        body.removeChild(el);\n      }\n    };\n  }, [id, text]);\n  return id;\n}\n\nvar AppContext = React.createContext(null);\n\nvar peerDependencies = {\n\treact: \"^16.8.5 || ^17.0.0\",\n\t\"react-dom\": \"^16.8.5 || ^17.0.0\"\n};\n\nvar semver = /(\\d+)\\.(\\d+)\\.(\\d+)/;\n\nvar getVersion = function getVersion(value) {\n  var result = semver.exec(value);\n  !(result != null) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Unable to parse React version \" + value) : invariant(false) : void 0;\n  var major = Number(result[1]);\n  var minor = Number(result[2]);\n  var patch = Number(result[3]);\n  return {\n    major: major,\n    minor: minor,\n    patch: patch,\n    raw: value\n  };\n};\n\nvar isSatisfied = function isSatisfied(expected, actual) {\n  if (actual.major > expected.major) {\n    return true;\n  }\n\n  if (actual.major < expected.major) {\n    return false;\n  }\n\n  if (actual.minor > expected.minor) {\n    return true;\n  }\n\n  if (actual.minor < expected.minor) {\n    return false;\n  }\n\n  return actual.patch >= expected.patch;\n};\n\nvar checkReactVersion = (function (peerDepValue, actualValue) {\n  var peerDep = getVersion(peerDepValue);\n  var actual = getVersion(actualValue);\n\n  if (isSatisfied(peerDep, actual)) {\n    return;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    React version: [\" + actual.raw + \"]\\n    does not satisfy expected peer dependency version: [\" + peerDep.raw + \"]\\n\\n    This can result in run time bugs, and even fatal crashes\\n  \") : void 0;\n});\n\nvar suffix = \"\\n  We expect a html5 doctype: <!doctype html>\\n  This is to ensure consistent browser layout and measurement\\n\\n  More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/doctype.md\\n\";\nvar checkDoctype = (function (doc) {\n  var doctype = doc.doctype;\n\n  if (!doctype) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      No <!doctype html> found.\\n\\n      \" + suffix + \"\\n    \") : void 0;\n    return;\n  }\n\n  if (doctype.name.toLowerCase() !== 'html') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Unexpected <!doctype> found: (\" + doctype.name + \")\\n\\n      \" + suffix + \"\\n    \") : void 0;\n  }\n\n  if (doctype.publicId !== '') {\n    process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Unexpected <!doctype> publicId found: (\" + doctype.publicId + \")\\n      A html5 doctype does not have a publicId\\n\\n      \" + suffix + \"\\n    \") : void 0;\n  }\n});\n\nfunction useDev(useHook) {\n  if (process.env.NODE_ENV !== 'production') {\n    useHook();\n  }\n}\n\nfunction useDevSetupWarning(fn, inputs) {\n  useDev(function () {\n    useEffect(function () {\n      try {\n        fn();\n      } catch (e) {\n        error(\"\\n          A setup problem was encountered.\\n\\n          > \" + e.message + \"\\n        \");\n      }\n    }, inputs);\n  });\n}\n\nfunction useStartupValidation() {\n  useDevSetupWarning(function () {\n    checkReactVersion(peerDependencies.react, React.version);\n    checkDoctype(document);\n  }, []);\n}\n\nfunction usePrevious(current) {\n  var ref = useRef(current);\n  useEffect(function () {\n    ref.current = current;\n  });\n  return ref;\n}\n\nfunction create() {\n  var lock = null;\n\n  function isClaimed() {\n    return Boolean(lock);\n  }\n\n  function isActive(value) {\n    return value === lock;\n  }\n\n  function claim(abandon) {\n    !!lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot claim lock as it is already claimed') : invariant(false) : void 0;\n    var newLock = {\n      abandon: abandon\n    };\n    lock = newLock;\n    return newLock;\n  }\n\n  function release() {\n    !lock ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot release lock when there is no lock') : invariant(false) : void 0;\n    lock = null;\n  }\n\n  function tryAbandon() {\n    if (lock) {\n      lock.abandon();\n      release();\n    }\n  }\n\n  return {\n    isClaimed: isClaimed,\n    isActive: isActive,\n    claim: claim,\n    release: release,\n    tryAbandon: tryAbandon\n  };\n}\n\nvar tab = 9;\nvar enter = 13;\nvar escape = 27;\nvar space = 32;\nvar pageUp = 33;\nvar pageDown = 34;\nvar end = 35;\nvar home = 36;\nvar arrowLeft = 37;\nvar arrowUp = 38;\nvar arrowRight = 39;\nvar arrowDown = 40;\n\nvar _preventedKeys;\nvar preventedKeys = (_preventedKeys = {}, _preventedKeys[enter] = true, _preventedKeys[tab] = true, _preventedKeys);\nvar preventStandardKeyEvents = (function (event) {\n  if (preventedKeys[event.keyCode]) {\n    event.preventDefault();\n  }\n});\n\nvar supportedEventName = function () {\n  var base = 'visibilitychange';\n\n  if (typeof document === 'undefined') {\n    return base;\n  }\n\n  var candidates = [base, \"ms\" + base, \"webkit\" + base, \"moz\" + base, \"o\" + base];\n  var supported = find(candidates, function (eventName) {\n    return \"on\" + eventName in document;\n  });\n  return supported || base;\n}();\n\nvar primaryButton = 0;\nvar sloppyClickThreshold = 5;\n\nfunction isSloppyClickThresholdExceeded(original, current) {\n  return Math.abs(current.x - original.x) >= sloppyClickThreshold || Math.abs(current.y - original.y) >= sloppyClickThreshold;\n}\n\nvar idle$1 = {\n  type: 'IDLE'\n};\n\nfunction getCaptureBindings(_ref) {\n  var cancel = _ref.cancel,\n      completed = _ref.completed,\n      getPhase = _ref.getPhase,\n      setPhase = _ref.setPhase;\n  return [{\n    eventName: 'mousemove',\n    fn: function fn(event) {\n      var button = event.button,\n          clientX = event.clientX,\n          clientY = event.clientY;\n\n      if (button !== primaryButton) {\n        return;\n      }\n\n      var point = {\n        x: clientX,\n        y: clientY\n      };\n      var phase = getPhase();\n\n      if (phase.type === 'DRAGGING') {\n        event.preventDefault();\n        phase.actions.move(point);\n        return;\n      }\n\n      !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot be IDLE') : invariant(false) : void 0;\n      var pending = phase.point;\n\n      if (!isSloppyClickThresholdExceeded(pending, point)) {\n        return;\n      }\n\n      event.preventDefault();\n      var actions = phase.actions.fluidLift(point);\n      setPhase({\n        type: 'DRAGGING',\n        actions: actions\n      });\n    }\n  }, {\n    eventName: 'mouseup',\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: function fn(event) {\n      if (getPhase().type === 'DRAGGING') {\n        event.preventDefault();\n      }\n\n      cancel();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type === 'PENDING') {\n        cancel();\n        return;\n      }\n\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'scroll',\n    options: {\n      passive: true,\n      capture: false\n    },\n    fn: function fn() {\n      if (getPhase().type === 'PENDING') {\n        cancel();\n      }\n    }\n  }, {\n    eventName: 'webkitmouseforcedown',\n    fn: function fn(event) {\n      var phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Unexpected phase') : invariant(false) : void 0;\n\n      if (phase.actions.shouldRespectForcePress()) {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction useMouseSensor(api) {\n  var phaseRef = useRef(idle$1);\n  var unbindEventsRef = useRef(noop);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'mousedown',\n      fn: function onMouseDown(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        if (event.button !== primaryButton) {\n          return;\n        }\n\n        if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {\n          return;\n        }\n\n        var draggableId = api.findClosestDraggableId(event);\n\n        if (!draggableId) {\n          return;\n        }\n\n        var actions = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n\n        if (!actions) {\n          return;\n        }\n\n        event.preventDefault();\n        var point = {\n          x: event.clientX,\n          y: event.clientY\n        };\n        unbindEventsRef.current();\n        startPendingDrag(actions, point);\n      }\n    };\n  }, [api]);\n  var preventForcePressBinding = useMemo(function () {\n    return {\n      eventName: 'webkitmouseforcewillbegin',\n      fn: function fn(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        var id = api.findClosestDraggableId(event);\n\n        if (!id) {\n          return;\n        }\n\n        var options = api.findOptionsForDraggable(id);\n\n        if (!options) {\n          return;\n        }\n\n        if (options.shouldRespectForcePress) {\n          return;\n        }\n\n        if (!api.canGetLock(id)) {\n          return;\n        }\n\n        event.preventDefault();\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function listenForCapture() {\n    var options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [preventForcePressBinding, startCaptureBinding], options);\n  }, [preventForcePressBinding, startCaptureBinding]);\n  var stop = useCallback(function () {\n    var current = phaseRef.current;\n\n    if (current.type === 'IDLE') {\n      return;\n    }\n\n    phaseRef.current = idle$1;\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture]);\n  var cancel = useCallback(function () {\n    var phase = phaseRef.current;\n    stop();\n\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  var bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    var bindings = getCaptureBindings({\n      cancel: cancel,\n      completed: stop,\n      getPhase: function getPhase() {\n        return phaseRef.current;\n      },\n      setPhase: function setPhase(phase) {\n        phaseRef.current = phase;\n      }\n    });\n    unbindEventsRef.current = bindEvents(window, bindings, options);\n  }, [cancel, stop]);\n  var startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(phaseRef.current.type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    phaseRef.current = {\n      type: 'PENDING',\n      point: point,\n      actions: actions\n    };\n    bindCapturingEvents();\n  }, [bindCapturingEvents]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nvar _scrollJumpKeys;\n\nfunction noop$1() {}\n\nvar scrollJumpKeys = (_scrollJumpKeys = {}, _scrollJumpKeys[pageDown] = true, _scrollJumpKeys[pageUp] = true, _scrollJumpKeys[home] = true, _scrollJumpKeys[end] = true, _scrollJumpKeys);\n\nfunction getDraggingBindings(actions, stop) {\n  function cancel() {\n    stop();\n    actions.cancel();\n  }\n\n  function drop() {\n    stop();\n    actions.drop();\n  }\n\n  return [{\n    eventName: 'keydown',\n    fn: function fn(event) {\n      if (event.keyCode === escape) {\n        event.preventDefault();\n        cancel();\n        return;\n      }\n\n      if (event.keyCode === space) {\n        event.preventDefault();\n        drop();\n        return;\n      }\n\n      if (event.keyCode === arrowDown) {\n        event.preventDefault();\n        actions.moveDown();\n        return;\n      }\n\n      if (event.keyCode === arrowUp) {\n        event.preventDefault();\n        actions.moveUp();\n        return;\n      }\n\n      if (event.keyCode === arrowRight) {\n        event.preventDefault();\n        actions.moveRight();\n        return;\n      }\n\n      if (event.keyCode === arrowLeft) {\n        event.preventDefault();\n        actions.moveLeft();\n        return;\n      }\n\n      if (scrollJumpKeys[event.keyCode]) {\n        event.preventDefault();\n        return;\n      }\n\n      preventStandardKeyEvents(event);\n    }\n  }, {\n    eventName: 'mousedown',\n    fn: cancel\n  }, {\n    eventName: 'mouseup',\n    fn: cancel\n  }, {\n    eventName: 'click',\n    fn: cancel\n  }, {\n    eventName: 'touchstart',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'wheel',\n    fn: cancel,\n    options: {\n      passive: true\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction useKeyboardSensor(api) {\n  var unbindEventsRef = useRef(noop$1);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'keydown',\n      fn: function onKeyDown(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        if (event.keyCode !== space) {\n          return;\n        }\n\n        var draggableId = api.findClosestDraggableId(event);\n\n        if (!draggableId) {\n          return;\n        }\n\n        var preDrag = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n\n        if (!preDrag) {\n          return;\n        }\n\n        event.preventDefault();\n        var isCapturing = true;\n        var actions = preDrag.snapLift();\n        unbindEventsRef.current();\n\n        function stop() {\n          !isCapturing ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop capturing a keyboard drag when not capturing') : invariant(false) : void 0;\n          isCapturing = false;\n          unbindEventsRef.current();\n          listenForCapture();\n        }\n\n        unbindEventsRef.current = bindEvents(window, getDraggingBindings(actions, stop), {\n          capture: true,\n          passive: false\n        });\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function tryStartCapture() {\n    var options = {\n      passive: false,\n      capture: true\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n    };\n  }, [listenForCapture]);\n}\n\nvar idle$2 = {\n  type: 'IDLE'\n};\nvar timeForLongPress = 120;\nvar forcePressThreshold = 0.15;\n\nfunction getWindowBindings(_ref) {\n  var cancel = _ref.cancel,\n      getPhase = _ref.getPhase;\n  return [{\n    eventName: 'orientationchange',\n    fn: cancel\n  }, {\n    eventName: 'resize',\n    fn: cancel\n  }, {\n    eventName: 'contextmenu',\n    fn: function fn(event) {\n      event.preventDefault();\n    }\n  }, {\n    eventName: 'keydown',\n    fn: function fn(event) {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      if (event.keyCode === escape) {\n        event.preventDefault();\n      }\n\n      cancel();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction getHandleBindings(_ref2) {\n  var cancel = _ref2.cancel,\n      completed = _ref2.completed,\n      getPhase = _ref2.getPhase;\n  return [{\n    eventName: 'touchmove',\n    options: {\n      capture: false\n    },\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      phase.hasMoved = true;\n      var _event$touches$ = event.touches[0],\n          clientX = _event$touches$.clientX,\n          clientY = _event$touches$.clientY;\n      var point = {\n        x: clientX,\n        y: clientY\n      };\n      event.preventDefault();\n      phase.actions.move(point);\n    }\n  }, {\n    eventName: 'touchend',\n    fn: function fn(event) {\n      var phase = getPhase();\n\n      if (phase.type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n      phase.actions.drop({\n        shouldBlockNextClick: true\n      });\n      completed();\n    }\n  }, {\n    eventName: 'touchcancel',\n    fn: function fn(event) {\n      if (getPhase().type !== 'DRAGGING') {\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n      cancel();\n    }\n  }, {\n    eventName: 'touchforcechange',\n    fn: function fn(event) {\n      var phase = getPhase();\n      !(phase.type !== 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n      var touch = event.touches[0];\n\n      if (!touch) {\n        return;\n      }\n\n      var isForcePress = touch.force >= forcePressThreshold;\n\n      if (!isForcePress) {\n        return;\n      }\n\n      var shouldRespect = phase.actions.shouldRespectForcePress();\n\n      if (phase.type === 'PENDING') {\n        if (shouldRespect) {\n          cancel();\n        }\n\n        return;\n      }\n\n      if (shouldRespect) {\n        if (phase.hasMoved) {\n          event.preventDefault();\n          return;\n        }\n\n        cancel();\n        return;\n      }\n\n      event.preventDefault();\n    }\n  }, {\n    eventName: supportedEventName,\n    fn: cancel\n  }];\n}\n\nfunction useTouchSensor(api) {\n  var phaseRef = useRef(idle$2);\n  var unbindEventsRef = useRef(noop);\n  var getPhase = useCallback(function getPhase() {\n    return phaseRef.current;\n  }, []);\n  var setPhase = useCallback(function setPhase(phase) {\n    phaseRef.current = phase;\n  }, []);\n  var startCaptureBinding = useMemo(function () {\n    return {\n      eventName: 'touchstart',\n      fn: function onTouchStart(event) {\n        if (event.defaultPrevented) {\n          return;\n        }\n\n        var draggableId = api.findClosestDraggableId(event);\n\n        if (!draggableId) {\n          return;\n        }\n\n        var actions = api.tryGetLock(draggableId, stop, {\n          sourceEvent: event\n        });\n\n        if (!actions) {\n          return;\n        }\n\n        var touch = event.touches[0];\n        var clientX = touch.clientX,\n            clientY = touch.clientY;\n        var point = {\n          x: clientX,\n          y: clientY\n        };\n        unbindEventsRef.current();\n        startPendingDrag(actions, point);\n      }\n    };\n  }, [api]);\n  var listenForCapture = useCallback(function listenForCapture() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    unbindEventsRef.current = bindEvents(window, [startCaptureBinding], options);\n  }, [startCaptureBinding]);\n  var stop = useCallback(function () {\n    var current = phaseRef.current;\n\n    if (current.type === 'IDLE') {\n      return;\n    }\n\n    if (current.type === 'PENDING') {\n      clearTimeout(current.longPressTimerId);\n    }\n\n    setPhase(idle$2);\n    unbindEventsRef.current();\n    listenForCapture();\n  }, [listenForCapture, setPhase]);\n  var cancel = useCallback(function () {\n    var phase = phaseRef.current;\n    stop();\n\n    if (phase.type === 'DRAGGING') {\n      phase.actions.cancel({\n        shouldBlockNextClick: true\n      });\n    }\n\n    if (phase.type === 'PENDING') {\n      phase.actions.abort();\n    }\n  }, [stop]);\n  var bindCapturingEvents = useCallback(function bindCapturingEvents() {\n    var options = {\n      capture: true,\n      passive: false\n    };\n    var args = {\n      cancel: cancel,\n      completed: stop,\n      getPhase: getPhase\n    };\n    var unbindTarget = bindEvents(window, getHandleBindings(args), options);\n    var unbindWindow = bindEvents(window, getWindowBindings(args), options);\n\n    unbindEventsRef.current = function unbindAll() {\n      unbindTarget();\n      unbindWindow();\n    };\n  }, [cancel, getPhase, stop]);\n  var startDragging = useCallback(function startDragging() {\n    var phase = getPhase();\n    !(phase.type === 'PENDING') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot start dragging from phase \" + phase.type) : invariant(false) : void 0;\n    var actions = phase.actions.fluidLift(phase.point);\n    setPhase({\n      type: 'DRAGGING',\n      actions: actions,\n      hasMoved: false\n    });\n  }, [getPhase, setPhase]);\n  var startPendingDrag = useCallback(function startPendingDrag(actions, point) {\n    !(getPhase().type === 'IDLE') ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected to move from IDLE to PENDING drag') : invariant(false) : void 0;\n    var longPressTimerId = setTimeout(startDragging, timeForLongPress);\n    setPhase({\n      type: 'PENDING',\n      point: point,\n      actions: actions,\n      longPressTimerId: longPressTimerId\n    });\n    bindCapturingEvents();\n  }, [bindCapturingEvents, getPhase, setPhase, startDragging]);\n  useIsomorphicLayoutEffect(function mount() {\n    listenForCapture();\n    return function unmount() {\n      unbindEventsRef.current();\n      var phase = getPhase();\n\n      if (phase.type === 'PENDING') {\n        clearTimeout(phase.longPressTimerId);\n        setPhase(idle$2);\n      }\n    };\n  }, [getPhase, listenForCapture, setPhase]);\n  useIsomorphicLayoutEffect(function webkitHack() {\n    var unbind = bindEvents(window, [{\n      eventName: 'touchmove',\n      fn: function fn() {},\n      options: {\n        capture: false,\n        passive: false\n      }\n    }]);\n    return unbind;\n  }, []);\n}\n\nfunction useValidateSensorHooks(sensorHooks) {\n  useDev(function () {\n    var previousRef = usePrevious(sensorHooks);\n    useDevSetupWarning(function () {\n      !(previousRef.current.length === sensorHooks.length) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot change the amount of sensor hooks after mounting') : invariant(false) : void 0;\n    });\n  });\n}\n\nvar interactiveTagNames = {\n  input: true,\n  button: true,\n  textarea: true,\n  select: true,\n  option: true,\n  optgroup: true,\n  video: true,\n  audio: true\n};\n\nfunction isAnInteractiveElement(parent, current) {\n  if (current == null) {\n    return false;\n  }\n\n  var hasAnInteractiveTag = Boolean(interactiveTagNames[current.tagName.toLowerCase()]);\n\n  if (hasAnInteractiveTag) {\n    return true;\n  }\n\n  var attribute = current.getAttribute('contenteditable');\n\n  if (attribute === 'true' || attribute === '') {\n    return true;\n  }\n\n  if (current === parent) {\n    return false;\n  }\n\n  return isAnInteractiveElement(parent, current.parentElement);\n}\n\nfunction isEventInInteractiveElement(draggable, event) {\n  var target = event.target;\n\n  if (!isHtmlElement(target)) {\n    return false;\n  }\n\n  return isAnInteractiveElement(draggable, target);\n}\n\nvar getBorderBoxCenterPosition = (function (el) {\n  return getRect(el.getBoundingClientRect()).center;\n});\n\nfunction isElement(el) {\n  return el instanceof getWindowFromEl(el).Element;\n}\n\nvar supportedMatchesName = function () {\n  var base = 'matches';\n\n  if (typeof document === 'undefined') {\n    return base;\n  }\n\n  var candidates = [base, 'msMatchesSelector', 'webkitMatchesSelector'];\n  var value = find(candidates, function (name) {\n    return name in Element.prototype;\n  });\n  return value || base;\n}();\n\nfunction closestPonyfill(el, selector) {\n  if (el == null) {\n    return null;\n  }\n\n  if (el[supportedMatchesName](selector)) {\n    return el;\n  }\n\n  return closestPonyfill(el.parentElement, selector);\n}\n\nfunction closest$1(el, selector) {\n  if (el.closest) {\n    return el.closest(selector);\n  }\n\n  return closestPonyfill(el, selector);\n}\n\nfunction getSelector(contextId) {\n  return \"[\" + dragHandle.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n}\n\nfunction findClosestDragHandleFromEvent(contextId, event) {\n  var target = event.target;\n\n  if (!isElement(target)) {\n    process.env.NODE_ENV !== \"production\" ? warning('event.target must be a Element') : void 0;\n    return null;\n  }\n\n  var selector = getSelector(contextId);\n  var handle = closest$1(target, selector);\n\n  if (!handle) {\n    return null;\n  }\n\n  if (!isHtmlElement(handle)) {\n    process.env.NODE_ENV !== \"production\" ? warning('drag handle must be a HTMLElement') : void 0;\n    return null;\n  }\n\n  return handle;\n}\n\nfunction tryGetClosestDraggableIdFromEvent(contextId, event) {\n  var handle = findClosestDragHandleFromEvent(contextId, event);\n\n  if (!handle) {\n    return null;\n  }\n\n  return handle.getAttribute(dragHandle.draggableId);\n}\n\nfunction findDraggable(contextId, draggableId) {\n  var selector = \"[\" + draggable.contextId + \"=\\\"\" + contextId + \"\\\"]\";\n  var possible = toArray(document.querySelectorAll(selector));\n  var draggable$1 = find(possible, function (el) {\n    return el.getAttribute(draggable.id) === draggableId;\n  });\n\n  if (!draggable$1) {\n    return null;\n  }\n\n  if (!isHtmlElement(draggable$1)) {\n    process.env.NODE_ENV !== \"production\" ? warning('Draggable element is not a HTMLElement') : void 0;\n    return null;\n  }\n\n  return draggable$1;\n}\n\nfunction preventDefault(event) {\n  event.preventDefault();\n}\n\nfunction _isActive(_ref) {\n  var expected = _ref.expected,\n      phase = _ref.phase,\n      isLockActive = _ref.isLockActive,\n      shouldWarn = _ref.shouldWarn;\n\n  if (!isLockActive()) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Cannot perform action.\\n        The sensor no longer has an action lock.\\n\\n        Tips:\\n\\n        - Throw away your action handlers when forceStop() is called\\n        - Check actions.isActive() if you really need to\\n      \") : void 0;\n    }\n\n    return false;\n  }\n\n  if (expected !== phase) {\n    if (shouldWarn) {\n      process.env.NODE_ENV !== \"production\" ? warning(\"\\n        Cannot perform action.\\n        The actions you used belong to an outdated phase\\n\\n        Current phase: \" + expected + \"\\n        You called an action from outdated phase: \" + phase + \"\\n\\n        Tips:\\n\\n        - Do not use preDragActions actions after calling preDragActions.lift()\\n      \") : void 0;\n    }\n\n    return false;\n  }\n\n  return true;\n}\n\nfunction canStart(_ref2) {\n  var lockAPI = _ref2.lockAPI,\n      store = _ref2.store,\n      registry = _ref2.registry,\n      draggableId = _ref2.draggableId;\n\n  if (lockAPI.isClaimed()) {\n    return false;\n  }\n\n  var entry = registry.draggable.findById(draggableId);\n\n  if (!entry) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find draggable with id: \" + draggableId) : void 0;\n    return false;\n  }\n\n  if (!entry.options.isEnabled) {\n    return false;\n  }\n\n  if (!canStartDrag(store.getState(), draggableId)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction tryStart(_ref3) {\n  var lockAPI = _ref3.lockAPI,\n      contextId = _ref3.contextId,\n      store = _ref3.store,\n      registry = _ref3.registry,\n      draggableId = _ref3.draggableId,\n      forceSensorStop = _ref3.forceSensorStop,\n      sourceEvent = _ref3.sourceEvent;\n  var shouldStart = canStart({\n    lockAPI: lockAPI,\n    store: store,\n    registry: registry,\n    draggableId: draggableId\n  });\n\n  if (!shouldStart) {\n    return null;\n  }\n\n  var entry = registry.draggable.getById(draggableId);\n  var el = findDraggable(contextId, entry.descriptor.id);\n\n  if (!el) {\n    process.env.NODE_ENV !== \"production\" ? warning(\"Unable to find draggable element with id: \" + draggableId) : void 0;\n    return null;\n  }\n\n  if (sourceEvent && !entry.options.canDragInteractiveElements && isEventInInteractiveElement(el, sourceEvent)) {\n    return null;\n  }\n\n  var lock = lockAPI.claim(forceSensorStop || noop);\n  var phase = 'PRE_DRAG';\n\n  function getShouldRespectForcePress() {\n    return entry.options.shouldRespectForcePress;\n  }\n\n  function isLockActive() {\n    return lockAPI.isActive(lock);\n  }\n\n  function tryDispatch(expected, getAction) {\n    if (_isActive({\n      expected: expected,\n      phase: phase,\n      isLockActive: isLockActive,\n      shouldWarn: true\n    })) {\n      store.dispatch(getAction());\n    }\n  }\n\n  var tryDispatchWhenDragging = tryDispatch.bind(null, 'DRAGGING');\n\n  function lift$1(args) {\n    function completed() {\n      lockAPI.release();\n      phase = 'COMPLETED';\n    }\n\n    if (phase !== 'PRE_DRAG') {\n      completed();\n      !(phase === 'PRE_DRAG') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Cannot lift in phase \" + phase) : invariant(false) : void 0;\n    }\n\n    store.dispatch(lift(args.liftActionArgs));\n    phase = 'DRAGGING';\n\n    function finish(reason, options) {\n      if (options === void 0) {\n        options = {\n          shouldBlockNextClick: false\n        };\n      }\n\n      args.cleanup();\n\n      if (options.shouldBlockNextClick) {\n        var unbind = bindEvents(window, [{\n          eventName: 'click',\n          fn: preventDefault,\n          options: {\n            once: true,\n            passive: false,\n            capture: true\n          }\n        }]);\n        setTimeout(unbind);\n      }\n\n      completed();\n      store.dispatch(drop({\n        reason: reason\n      }));\n    }\n\n    return _extends({\n      isActive: function isActive() {\n        return _isActive({\n          expected: 'DRAGGING',\n          phase: phase,\n          isLockActive: isLockActive,\n          shouldWarn: false\n        });\n      },\n      shouldRespectForcePress: getShouldRespectForcePress,\n      drop: function drop(options) {\n        return finish('DROP', options);\n      },\n      cancel: function cancel(options) {\n        return finish('CANCEL', options);\n      }\n    }, args.actions);\n  }\n\n  function fluidLift(clientSelection) {\n    var move$1 = rafSchd(function (client) {\n      tryDispatchWhenDragging(function () {\n        return move({\n          client: client\n        });\n      });\n    });\n    var api = lift$1({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: clientSelection,\n        movementMode: 'FLUID'\n      },\n      cleanup: function cleanup() {\n        return move$1.cancel();\n      },\n      actions: {\n        move: move$1\n      }\n    });\n    return _extends({}, api, {\n      move: move$1\n    });\n  }\n\n  function snapLift() {\n    var actions = {\n      moveUp: function moveUp$1() {\n        return tryDispatchWhenDragging(moveUp);\n      },\n      moveRight: function moveRight$1() {\n        return tryDispatchWhenDragging(moveRight);\n      },\n      moveDown: function moveDown$1() {\n        return tryDispatchWhenDragging(moveDown);\n      },\n      moveLeft: function moveLeft$1() {\n        return tryDispatchWhenDragging(moveLeft);\n      }\n    };\n    return lift$1({\n      liftActionArgs: {\n        id: draggableId,\n        clientSelection: getBorderBoxCenterPosition(el),\n        movementMode: 'SNAP'\n      },\n      cleanup: noop,\n      actions: actions\n    });\n  }\n\n  function abortPreDrag() {\n    var shouldRelease = _isActive({\n      expected: 'PRE_DRAG',\n      phase: phase,\n      isLockActive: isLockActive,\n      shouldWarn: true\n    });\n\n    if (shouldRelease) {\n      lockAPI.release();\n    }\n  }\n\n  var preDrag = {\n    isActive: function isActive() {\n      return _isActive({\n        expected: 'PRE_DRAG',\n        phase: phase,\n        isLockActive: isLockActive,\n        shouldWarn: false\n      });\n    },\n    shouldRespectForcePress: getShouldRespectForcePress,\n    fluidLift: fluidLift,\n    snapLift: snapLift,\n    abort: abortPreDrag\n  };\n  return preDrag;\n}\n\nvar defaultSensors = [useMouseSensor, useKeyboardSensor, useTouchSensor];\nfunction useSensorMarshal(_ref4) {\n  var contextId = _ref4.contextId,\n      store = _ref4.store,\n      registry = _ref4.registry,\n      customSensors = _ref4.customSensors,\n      enableDefaultSensors = _ref4.enableDefaultSensors;\n  var useSensors = [].concat(enableDefaultSensors ? defaultSensors : [], customSensors || []);\n  var lockAPI = useState(function () {\n    return create();\n  })[0];\n  var tryAbandonLock = useCallback(function tryAbandonLock(previous, current) {\n    if (previous.isDragging && !current.isDragging) {\n      lockAPI.tryAbandon();\n    }\n  }, [lockAPI]);\n  useIsomorphicLayoutEffect(function listenToStore() {\n    var previous = store.getState();\n    var unsubscribe = store.subscribe(function () {\n      var current = store.getState();\n      tryAbandonLock(previous, current);\n      previous = current;\n    });\n    return unsubscribe;\n  }, [lockAPI, store, tryAbandonLock]);\n  useIsomorphicLayoutEffect(function () {\n    return lockAPI.tryAbandon;\n  }, [lockAPI.tryAbandon]);\n  var canGetLock = useCallback(function (draggableId) {\n    return canStart({\n      lockAPI: lockAPI,\n      registry: registry,\n      store: store,\n      draggableId: draggableId\n    });\n  }, [lockAPI, registry, store]);\n  var tryGetLock = useCallback(function (draggableId, forceStop, options) {\n    return tryStart({\n      lockAPI: lockAPI,\n      registry: registry,\n      contextId: contextId,\n      store: store,\n      draggableId: draggableId,\n      forceSensorStop: forceStop,\n      sourceEvent: options && options.sourceEvent ? options.sourceEvent : null\n    });\n  }, [contextId, lockAPI, registry, store]);\n  var findClosestDraggableId = useCallback(function (event) {\n    return tryGetClosestDraggableIdFromEvent(contextId, event);\n  }, [contextId]);\n  var findOptionsForDraggable = useCallback(function (id) {\n    var entry = registry.draggable.findById(id);\n    return entry ? entry.options : null;\n  }, [registry.draggable]);\n  var tryReleaseLock = useCallback(function tryReleaseLock() {\n    if (!lockAPI.isClaimed()) {\n      return;\n    }\n\n    lockAPI.tryAbandon();\n\n    if (store.getState().phase !== 'IDLE') {\n      store.dispatch(flush());\n    }\n  }, [lockAPI, store]);\n  var isLockClaimed = useCallback(lockAPI.isClaimed, [lockAPI]);\n  var api = useMemo(function () {\n    return {\n      canGetLock: canGetLock,\n      tryGetLock: tryGetLock,\n      findClosestDraggableId: findClosestDraggableId,\n      findOptionsForDraggable: findOptionsForDraggable,\n      tryReleaseLock: tryReleaseLock,\n      isLockClaimed: isLockClaimed\n    };\n  }, [canGetLock, tryGetLock, findClosestDraggableId, findOptionsForDraggable, tryReleaseLock, isLockClaimed]);\n  useValidateSensorHooks(useSensors);\n\n  for (var i = 0; i < useSensors.length; i++) {\n    useSensors[i](api);\n  }\n}\n\nvar createResponders = function createResponders(props) {\n  return {\n    onBeforeCapture: props.onBeforeCapture,\n    onBeforeDragStart: props.onBeforeDragStart,\n    onDragStart: props.onDragStart,\n    onDragEnd: props.onDragEnd,\n    onDragUpdate: props.onDragUpdate\n  };\n};\n\nfunction getStore(lazyRef) {\n  !lazyRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find store from lazy ref') : invariant(false) : void 0;\n  return lazyRef.current;\n}\n\nfunction App(props) {\n  var contextId = props.contextId,\n      setCallbacks = props.setCallbacks,\n      sensors = props.sensors,\n      nonce = props.nonce,\n      dragHandleUsageInstructions = props.dragHandleUsageInstructions;\n  var lazyStoreRef = useRef(null);\n  useStartupValidation();\n  var lastPropsRef = usePrevious(props);\n  var getResponders = useCallback(function () {\n    return createResponders(lastPropsRef.current);\n  }, [lastPropsRef]);\n  var announce = useAnnouncer(contextId);\n  var dragHandleUsageInstructionsId = useHiddenTextElement({\n    contextId: contextId,\n    text: dragHandleUsageInstructions\n  });\n  var styleMarshal = useStyleMarshal(contextId, nonce);\n  var lazyDispatch = useCallback(function (action) {\n    getStore(lazyStoreRef).dispatch(action);\n  }, []);\n  var marshalCallbacks = useMemo(function () {\n    return bindActionCreators({\n      publishWhileDragging: publishWhileDragging,\n      updateDroppableScroll: updateDroppableScroll,\n      updateDroppableIsEnabled: updateDroppableIsEnabled,\n      updateDroppableIsCombineEnabled: updateDroppableIsCombineEnabled,\n      collectionStarting: collectionStarting\n    }, lazyDispatch);\n  }, [lazyDispatch]);\n  var registry = useRegistry();\n  var dimensionMarshal = useMemo(function () {\n    return createDimensionMarshal(registry, marshalCallbacks);\n  }, [registry, marshalCallbacks]);\n  var autoScroller = useMemo(function () {\n    return createAutoScroller(_extends({\n      scrollWindow: scrollWindow,\n      scrollDroppable: dimensionMarshal.scrollDroppable\n    }, bindActionCreators({\n      move: move\n    }, lazyDispatch)));\n  }, [dimensionMarshal.scrollDroppable, lazyDispatch]);\n  var focusMarshal = useFocusMarshal(contextId);\n  var store = useMemo(function () {\n    return createStore({\n      announce: announce,\n      autoScroller: autoScroller,\n      dimensionMarshal: dimensionMarshal,\n      focusMarshal: focusMarshal,\n      getResponders: getResponders,\n      styleMarshal: styleMarshal\n    });\n  }, [announce, autoScroller, dimensionMarshal, focusMarshal, getResponders, styleMarshal]);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (lazyStoreRef.current && lazyStoreRef.current !== store) {\n      process.env.NODE_ENV !== \"production\" ? warning('unexpected store change') : void 0;\n    }\n  }\n\n  lazyStoreRef.current = store;\n  var tryResetStore = useCallback(function () {\n    var current = getStore(lazyStoreRef);\n    var state = current.getState();\n\n    if (state.phase !== 'IDLE') {\n      current.dispatch(flush());\n    }\n  }, []);\n  var isDragging = useCallback(function () {\n    var state = getStore(lazyStoreRef).getState();\n    return state.isDragging || state.phase === 'DROP_ANIMATING';\n  }, []);\n  var appCallbacks = useMemo(function () {\n    return {\n      isDragging: isDragging,\n      tryAbort: tryResetStore\n    };\n  }, [isDragging, tryResetStore]);\n  setCallbacks(appCallbacks);\n  var getCanLift = useCallback(function (id) {\n    return canStartDrag(getStore(lazyStoreRef).getState(), id);\n  }, []);\n  var getIsMovementAllowed = useCallback(function () {\n    return isMovementAllowed(getStore(lazyStoreRef).getState());\n  }, []);\n  var appContext = useMemo(function () {\n    return {\n      marshal: dimensionMarshal,\n      focus: focusMarshal,\n      contextId: contextId,\n      canLift: getCanLift,\n      isMovementAllowed: getIsMovementAllowed,\n      dragHandleUsageInstructionsId: dragHandleUsageInstructionsId,\n      registry: registry\n    };\n  }, [contextId, dimensionMarshal, dragHandleUsageInstructionsId, focusMarshal, getCanLift, getIsMovementAllowed, registry]);\n  useSensorMarshal({\n    contextId: contextId,\n    store: store,\n    registry: registry,\n    customSensors: sensors,\n    enableDefaultSensors: props.enableDefaultSensors !== false\n  });\n  useEffect(function () {\n    return tryResetStore;\n  }, [tryResetStore]);\n  return React.createElement(AppContext.Provider, {\n    value: appContext\n  }, React.createElement(Provider, {\n    context: StoreContext,\n    store: store\n  }, props.children));\n}\n\nvar count$1 = 0;\nfunction reset$1() {\n  count$1 = 0;\n}\nfunction useInstanceCount() {\n  return useMemo(function () {\n    return \"\" + count$1++;\n  }, []);\n}\n\nfunction resetServerContext() {\n  reset$1();\n  reset();\n}\nfunction DragDropContext(props) {\n  var contextId = useInstanceCount();\n  var dragHandleUsageInstructions = props.dragHandleUsageInstructions || preset.dragHandleUsageInstructions;\n  return React.createElement(ErrorBoundary, null, function (setCallbacks) {\n    return React.createElement(App, {\n      nonce: props.nonce,\n      contextId: contextId,\n      setCallbacks: setCallbacks,\n      dragHandleUsageInstructions: dragHandleUsageInstructions,\n      enableDefaultSensors: props.enableDefaultSensors,\n      sensors: props.sensors,\n      onBeforeCapture: props.onBeforeCapture,\n      onBeforeDragStart: props.onBeforeDragStart,\n      onDragStart: props.onDragStart,\n      onDragUpdate: props.onDragUpdate,\n      onDragEnd: props.onDragEnd\n    }, props.children);\n  });\n}\n\nvar isEqual$1 = function isEqual(base) {\n  return function (value) {\n    return base === value;\n  };\n};\n\nvar isScroll = isEqual$1('scroll');\nvar isAuto = isEqual$1('auto');\nvar isVisible$1 = isEqual$1('visible');\n\nvar isEither = function isEither(overflow, fn) {\n  return fn(overflow.overflowX) || fn(overflow.overflowY);\n};\n\nvar isBoth = function isBoth(overflow, fn) {\n  return fn(overflow.overflowX) && fn(overflow.overflowY);\n};\n\nvar isElementScrollable = function isElementScrollable(el) {\n  var style = window.getComputedStyle(el);\n  var overflow = {\n    overflowX: style.overflowX,\n    overflowY: style.overflowY\n  };\n  return isEither(overflow, isScroll) || isEither(overflow, isAuto);\n};\n\nvar isBodyScrollable = function isBodyScrollable() {\n  if (process.env.NODE_ENV === 'production') {\n    return false;\n  }\n\n  var body = getBodyElement();\n  var html = document.documentElement;\n  !html ? process.env.NODE_ENV !== \"production\" ? invariant(false) : invariant(false) : void 0;\n\n  if (!isElementScrollable(body)) {\n    return false;\n  }\n\n  var htmlStyle = window.getComputedStyle(html);\n  var htmlOverflow = {\n    overflowX: htmlStyle.overflowX,\n    overflowY: htmlStyle.overflowY\n  };\n\n  if (isBoth(htmlOverflow, isVisible$1)) {\n    return false;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    We have detected that your <body> element might be a scroll container.\\n    We have found no reliable way of detecting whether the <body> element is a scroll container.\\n    Under most circumstances a <body> scroll bar will be on the <html> element (document.documentElement)\\n\\n    Because we cannot determine if the <body> is a scroll container, and generally it is not one,\\n    we will be treating the <body> as *not* a scroll container\\n\\n    More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/how-we-detect-scroll-containers.md\\n  \") : void 0;\n  return false;\n};\n\nvar getClosestScrollable = function getClosestScrollable(el) {\n  if (el == null) {\n    return null;\n  }\n\n  if (el === document.body) {\n    return isBodyScrollable() ? el : null;\n  }\n\n  if (el === document.documentElement) {\n    return null;\n  }\n\n  if (!isElementScrollable(el)) {\n    return getClosestScrollable(el.parentElement);\n  }\n\n  return el;\n};\n\nvar checkForNestedScrollContainers = (function (scrollable) {\n  if (!scrollable) {\n    return;\n  }\n\n  var anotherScrollParent = getClosestScrollable(scrollable.parentElement);\n\n  if (!anotherScrollParent) {\n    return;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n    Droppable: unsupported nested scroll container detected.\\n    A Droppable can only have one scroll parent (which can be itself)\\n    Nested scroll containers are currently not supported.\\n\\n    We hope to support nested scroll containers soon: https://github.com/atlassian/react-beautiful-dnd/issues/131\\n  \") : void 0;\n});\n\nvar getScroll$1 = (function (el) {\n  return {\n    x: el.scrollLeft,\n    y: el.scrollTop\n  };\n});\n\nvar getIsFixed = function getIsFixed(el) {\n  if (!el) {\n    return false;\n  }\n\n  var style = window.getComputedStyle(el);\n\n  if (style.position === 'fixed') {\n    return true;\n  }\n\n  return getIsFixed(el.parentElement);\n};\n\nvar getEnv = (function (start) {\n  var closestScrollable = getClosestScrollable(start);\n  var isFixedOnPage = getIsFixed(start);\n  return {\n    closestScrollable: closestScrollable,\n    isFixedOnPage: isFixedOnPage\n  };\n});\n\nvar getDroppableDimension = (function (_ref) {\n  var descriptor = _ref.descriptor,\n      isEnabled = _ref.isEnabled,\n      isCombineEnabled = _ref.isCombineEnabled,\n      isFixedOnPage = _ref.isFixedOnPage,\n      direction = _ref.direction,\n      client = _ref.client,\n      page = _ref.page,\n      closest = _ref.closest;\n\n  var frame = function () {\n    if (!closest) {\n      return null;\n    }\n\n    var scrollSize = closest.scrollSize,\n        frameClient = closest.client;\n    var maxScroll = getMaxScroll({\n      scrollHeight: scrollSize.scrollHeight,\n      scrollWidth: scrollSize.scrollWidth,\n      height: frameClient.paddingBox.height,\n      width: frameClient.paddingBox.width\n    });\n    return {\n      pageMarginBox: closest.page.marginBox,\n      frameClient: frameClient,\n      scrollSize: scrollSize,\n      shouldClipSubject: closest.shouldClipSubject,\n      scroll: {\n        initial: closest.scroll,\n        current: closest.scroll,\n        max: maxScroll,\n        diff: {\n          value: origin,\n          displacement: origin\n        }\n      }\n    };\n  }();\n\n  var axis = direction === 'vertical' ? vertical : horizontal;\n  var subject = getSubject({\n    page: page,\n    withPlaceholder: null,\n    axis: axis,\n    frame: frame\n  });\n  var dimension = {\n    descriptor: descriptor,\n    isCombineEnabled: isCombineEnabled,\n    isFixedOnPage: isFixedOnPage,\n    axis: axis,\n    isEnabled: isEnabled,\n    client: client,\n    page: page,\n    frame: frame,\n    subject: subject\n  };\n  return dimension;\n});\n\nvar getClient = function getClient(targetRef, closestScrollable) {\n  var base = getBox(targetRef);\n\n  if (!closestScrollable) {\n    return base;\n  }\n\n  if (targetRef !== closestScrollable) {\n    return base;\n  }\n\n  var top = base.paddingBox.top - closestScrollable.scrollTop;\n  var left = base.paddingBox.left - closestScrollable.scrollLeft;\n  var bottom = top + closestScrollable.scrollHeight;\n  var right = left + closestScrollable.scrollWidth;\n  var paddingBox = {\n    top: top,\n    right: right,\n    bottom: bottom,\n    left: left\n  };\n  var borderBox = expand(paddingBox, base.border);\n  var client = createBox({\n    borderBox: borderBox,\n    margin: base.margin,\n    border: base.border,\n    padding: base.padding\n  });\n  return client;\n};\n\nvar getDimension = (function (_ref) {\n  var ref = _ref.ref,\n      descriptor = _ref.descriptor,\n      env = _ref.env,\n      windowScroll = _ref.windowScroll,\n      direction = _ref.direction,\n      isDropDisabled = _ref.isDropDisabled,\n      isCombineEnabled = _ref.isCombineEnabled,\n      shouldClipSubject = _ref.shouldClipSubject;\n  var closestScrollable = env.closestScrollable;\n  var client = getClient(ref, closestScrollable);\n  var page = withScroll(client, windowScroll);\n\n  var closest = function () {\n    if (!closestScrollable) {\n      return null;\n    }\n\n    var frameClient = getBox(closestScrollable);\n    var scrollSize = {\n      scrollHeight: closestScrollable.scrollHeight,\n      scrollWidth: closestScrollable.scrollWidth\n    };\n    return {\n      client: frameClient,\n      page: withScroll(frameClient, windowScroll),\n      scroll: getScroll$1(closestScrollable),\n      scrollSize: scrollSize,\n      shouldClipSubject: shouldClipSubject\n    };\n  }();\n\n  var dimension = getDroppableDimension({\n    descriptor: descriptor,\n    isEnabled: !isDropDisabled,\n    isCombineEnabled: isCombineEnabled,\n    isFixedOnPage: env.isFixedOnPage,\n    direction: direction,\n    client: client,\n    page: page,\n    closest: closest\n  });\n  return dimension;\n});\n\nvar immediate = {\n  passive: false\n};\nvar delayed = {\n  passive: true\n};\nvar getListenerOptions = (function (options) {\n  return options.shouldPublishImmediately ? immediate : delayed;\n});\n\nfunction useRequiredContext(Context) {\n  var result = useContext(Context);\n  !result ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find required context') : invariant(false) : void 0;\n  return result;\n}\n\nvar getClosestScrollableFromDrag = function getClosestScrollableFromDrag(dragging) {\n  return dragging && dragging.env.closestScrollable || null;\n};\n\nfunction useDroppablePublisher(args) {\n  var whileDraggingRef = useRef(null);\n  var appContext = useRequiredContext(AppContext);\n  var uniqueId = useUniqueId('droppable');\n  var registry = appContext.registry,\n      marshal = appContext.marshal;\n  var previousRef = usePrevious(args);\n  var descriptor = useMemo(function () {\n    return {\n      id: args.droppableId,\n      type: args.type,\n      mode: args.mode\n    };\n  }, [args.droppableId, args.mode, args.type]);\n  var publishedDescriptorRef = useRef(descriptor);\n  var memoizedUpdateScroll = useMemo(function () {\n    return memoizeOne(function (x, y) {\n      !whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only update scroll when dragging') : invariant(false) : void 0;\n      var scroll = {\n        x: x,\n        y: y\n      };\n      marshal.updateDroppableScroll(descriptor.id, scroll);\n    });\n  }, [descriptor.id, marshal]);\n  var getClosestScroll = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n\n    if (!dragging || !dragging.env.closestScrollable) {\n      return origin;\n    }\n\n    return getScroll$1(dragging.env.closestScrollable);\n  }, []);\n  var updateScroll = useCallback(function () {\n    var scroll = getClosestScroll();\n    memoizedUpdateScroll(scroll.x, scroll.y);\n  }, [getClosestScroll, memoizedUpdateScroll]);\n  var scheduleScrollUpdate = useMemo(function () {\n    return rafSchd(updateScroll);\n  }, [updateScroll]);\n  var onClosestScroll = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find scroll options while scrolling') : invariant(false) : void 0;\n    var options = dragging.scrollOptions;\n\n    if (options.shouldPublishImmediately) {\n      updateScroll();\n      return;\n    }\n\n    scheduleScrollUpdate();\n  }, [scheduleScrollUpdate, updateScroll]);\n  var getDimensionAndWatchScroll = useCallback(function (windowScroll, options) {\n    !!whileDraggingRef.current ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect a droppable while a drag is occurring') : invariant(false) : void 0;\n    var previous = previousRef.current;\n    var ref = previous.getDroppableRef();\n    !ref ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot collect without a droppable ref') : invariant(false) : void 0;\n    var env = getEnv(ref);\n    var dragging = {\n      ref: ref,\n      descriptor: descriptor,\n      env: env,\n      scrollOptions: options\n    };\n    whileDraggingRef.current = dragging;\n    var dimension = getDimension({\n      ref: ref,\n      descriptor: descriptor,\n      env: env,\n      windowScroll: windowScroll,\n      direction: previous.direction,\n      isDropDisabled: previous.isDropDisabled,\n      isCombineEnabled: previous.isCombineEnabled,\n      shouldClipSubject: !previous.ignoreContainerClipping\n    });\n    var scrollable = env.closestScrollable;\n\n    if (scrollable) {\n      scrollable.setAttribute(scrollContainer.contextId, appContext.contextId);\n      scrollable.addEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n\n      if (process.env.NODE_ENV !== 'production') {\n        checkForNestedScrollContainers(scrollable);\n      }\n    }\n\n    return dimension;\n  }, [appContext.contextId, descriptor, onClosestScroll, previousRef]);\n  var getScrollWhileDragging = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !(dragging && closest) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Can only recollect Droppable client for Droppables that have a scroll container') : invariant(false) : void 0;\n    return getScroll$1(closest);\n  }, []);\n  var dragStopped = useCallback(function () {\n    var dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot stop drag when no active drag') : invariant(false) : void 0;\n    var closest = getClosestScrollableFromDrag(dragging);\n    whileDraggingRef.current = null;\n\n    if (!closest) {\n      return;\n    }\n\n    scheduleScrollUpdate.cancel();\n    closest.removeAttribute(scrollContainer.contextId);\n    closest.removeEventListener('scroll', onClosestScroll, getListenerOptions(dragging.scrollOptions));\n  }, [onClosestScroll, scheduleScrollUpdate]);\n  var scroll = useCallback(function (change) {\n    var dragging = whileDraggingRef.current;\n    !dragging ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll when there is no drag') : invariant(false) : void 0;\n    var closest = getClosestScrollableFromDrag(dragging);\n    !closest ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot scroll a droppable with no closest scrollable') : invariant(false) : void 0;\n    closest.scrollTop += change.y;\n    closest.scrollLeft += change.x;\n  }, []);\n  var callbacks = useMemo(function () {\n    return {\n      getDimensionAndWatchScroll: getDimensionAndWatchScroll,\n      getScrollWhileDragging: getScrollWhileDragging,\n      dragStopped: dragStopped,\n      scroll: scroll\n    };\n  }, [dragStopped, getDimensionAndWatchScroll, getScrollWhileDragging, scroll]);\n  var entry = useMemo(function () {\n    return {\n      uniqueId: uniqueId,\n      descriptor: descriptor,\n      callbacks: callbacks\n    };\n  }, [callbacks, descriptor, uniqueId]);\n  useIsomorphicLayoutEffect(function () {\n    publishedDescriptorRef.current = entry.descriptor;\n    registry.droppable.register(entry);\n    return function () {\n      if (whileDraggingRef.current) {\n        process.env.NODE_ENV !== \"production\" ? warning('Unsupported: changing the droppableId or type of a Droppable during a drag') : void 0;\n        dragStopped();\n      }\n\n      registry.droppable.unregister(entry);\n    };\n  }, [callbacks, descriptor, dragStopped, entry, marshal, registry.droppable]);\n  useIsomorphicLayoutEffect(function () {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n\n    marshal.updateDroppableIsEnabled(publishedDescriptorRef.current.id, !args.isDropDisabled);\n  }, [args.isDropDisabled, marshal]);\n  useIsomorphicLayoutEffect(function () {\n    if (!whileDraggingRef.current) {\n      return;\n    }\n\n    marshal.updateDroppableIsCombineEnabled(publishedDescriptorRef.current.id, args.isCombineEnabled);\n  }, [args.isCombineEnabled, marshal]);\n}\n\nfunction noop$2() {}\n\nvar empty = {\n  width: 0,\n  height: 0,\n  margin: noSpacing\n};\n\nvar getSize = function getSize(_ref) {\n  var isAnimatingOpenOnMount = _ref.isAnimatingOpenOnMount,\n      placeholder = _ref.placeholder,\n      animate = _ref.animate;\n\n  if (isAnimatingOpenOnMount) {\n    return empty;\n  }\n\n  if (animate === 'close') {\n    return empty;\n  }\n\n  return {\n    height: placeholder.client.borderBox.height,\n    width: placeholder.client.borderBox.width,\n    margin: placeholder.client.margin\n  };\n};\n\nvar getStyle = function getStyle(_ref2) {\n  var isAnimatingOpenOnMount = _ref2.isAnimatingOpenOnMount,\n      placeholder = _ref2.placeholder,\n      animate = _ref2.animate;\n  var size = getSize({\n    isAnimatingOpenOnMount: isAnimatingOpenOnMount,\n    placeholder: placeholder,\n    animate: animate\n  });\n  return {\n    display: placeholder.display,\n    boxSizing: 'border-box',\n    width: size.width,\n    height: size.height,\n    marginTop: size.margin.top,\n    marginRight: size.margin.right,\n    marginBottom: size.margin.bottom,\n    marginLeft: size.margin.left,\n    flexShrink: '0',\n    flexGrow: '0',\n    pointerEvents: 'none',\n    transition: animate !== 'none' ? transitions.placeholder : null\n  };\n};\n\nfunction Placeholder(props) {\n  var animateOpenTimerRef = useRef(null);\n  var tryClearAnimateOpenTimer = useCallback(function () {\n    if (!animateOpenTimerRef.current) {\n      return;\n    }\n\n    clearTimeout(animateOpenTimerRef.current);\n    animateOpenTimerRef.current = null;\n  }, []);\n  var animate = props.animate,\n      onTransitionEnd = props.onTransitionEnd,\n      onClose = props.onClose,\n      contextId = props.contextId;\n\n  var _useState = useState(props.animate === 'open'),\n      isAnimatingOpenOnMount = _useState[0],\n      setIsAnimatingOpenOnMount = _useState[1];\n\n  useEffect(function () {\n    if (!isAnimatingOpenOnMount) {\n      return noop$2;\n    }\n\n    if (animate !== 'open') {\n      tryClearAnimateOpenTimer();\n      setIsAnimatingOpenOnMount(false);\n      return noop$2;\n    }\n\n    if (animateOpenTimerRef.current) {\n      return noop$2;\n    }\n\n    animateOpenTimerRef.current = setTimeout(function () {\n      animateOpenTimerRef.current = null;\n      setIsAnimatingOpenOnMount(false);\n    });\n    return tryClearAnimateOpenTimer;\n  }, [animate, isAnimatingOpenOnMount, tryClearAnimateOpenTimer]);\n  var onSizeChangeEnd = useCallback(function (event) {\n    if (event.propertyName !== 'height') {\n      return;\n    }\n\n    onTransitionEnd();\n\n    if (animate === 'close') {\n      onClose();\n    }\n  }, [animate, onClose, onTransitionEnd]);\n  var style = getStyle({\n    isAnimatingOpenOnMount: isAnimatingOpenOnMount,\n    animate: props.animate,\n    placeholder: props.placeholder\n  });\n  return React.createElement(props.placeholder.tagName, {\n    style: style,\n    'data-rbd-placeholder-context-id': contextId,\n    onTransitionEnd: onSizeChangeEnd,\n    ref: props.innerRef\n  });\n}\n\nvar Placeholder$1 = React.memo(Placeholder);\n\nvar DroppableContext = React.createContext(null);\n\nfunction checkIsValidInnerRef(el) {\n  !(el && isHtmlElement(el)) ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"\\n    provided.innerRef has not been provided with a HTMLElement.\\n\\n    You can find a guide on using the innerRef callback functions at:\\n    https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/guides/using-inner-ref.md\\n  \") : invariant(false) : void 0;\n}\n\nfunction isBoolean(value) {\n  return typeof value === 'boolean';\n}\n\nfunction runChecks(args, checks) {\n  checks.forEach(function (check) {\n    return check(args);\n  });\n}\n\nvar shared = [function required(_ref) {\n  var props = _ref.props;\n  !props.droppableId ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'A Droppable requires a droppableId prop') : invariant(false) : void 0;\n  !(typeof props.droppableId === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"A Droppable requires a [string] droppableId. Provided: [\" + typeof props.droppableId + \"]\") : invariant(false) : void 0;\n}, function _boolean(_ref2) {\n  var props = _ref2.props;\n  !isBoolean(props.isDropDisabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isDropDisabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.isCombineEnabled) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'isCombineEnabled must be a boolean') : invariant(false) : void 0;\n  !isBoolean(props.ignoreContainerClipping) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'ignoreContainerClipping must be a boolean') : invariant(false) : void 0;\n}, function ref(_ref3) {\n  var getDroppableRef = _ref3.getDroppableRef;\n  checkIsValidInnerRef(getDroppableRef());\n}];\nvar standard = [function placeholder(_ref4) {\n  var props = _ref4.props,\n      getPlaceholderRef = _ref4.getPlaceholderRef;\n\n  if (!props.placeholder) {\n    return;\n  }\n\n  var ref = getPlaceholderRef();\n\n  if (ref) {\n    return;\n  }\n\n  process.env.NODE_ENV !== \"production\" ? warning(\"\\n      Droppable setup issue [droppableId: \\\"\" + props.droppableId + \"\\\"]:\\n      DroppableProvided > placeholder could not be found.\\n\\n      Please be sure to add the {provided.placeholder} React Node as a child of your Droppable.\\n      More information: https://github.com/atlassian/react-beautiful-dnd/blob/master/docs/api/droppable.md\\n    \") : void 0;\n}];\nvar virtual = [function hasClone(_ref5) {\n  var props = _ref5.props;\n  !props.renderClone ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Must provide a clone render function (renderClone) for virtual lists') : invariant(false) : void 0;\n}, function hasNoPlaceholder(_ref6) {\n  var getPlaceholderRef = _ref6.getPlaceholderRef;\n  !!getPlaceholderRef() ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Expected virtual list to not have a placeholder') : invariant(false) : void 0;\n}];\nfunction useValidation(args) {\n  useDevSetupWarning(function () {\n    runChecks(args, shared);\n\n    if (args.props.mode === 'standard') {\n      runChecks(args, standard);\n    }\n\n    if (args.props.mode === 'virtual') {\n      runChecks(args, virtual);\n    }\n  });\n}\n\nvar AnimateInOut = function (_React$PureComponent) {\n  _inheritsLoose(AnimateInOut, _React$PureComponent);\n\n  function AnimateInOut() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$PureComponent.call.apply(_React$PureComponent, [this].concat(args)) || this;\n    _this.state = {\n      isVisible: Boolean(_this.props.on),\n      data: _this.props.on,\n      animate: _this.props.shouldAnimate && _this.props.on ? 'open' : 'none'\n    };\n\n    _this.onClose = function () {\n      if (_this.state.animate !== 'close') {\n        return;\n      }\n\n      _this.setState({\n        isVisible: false\n      });\n    };\n\n    return _this;\n  }\n\n  AnimateInOut.getDerivedStateFromProps = function getDerivedStateFromProps(props, state) {\n    if (!props.shouldAnimate) {\n      return {\n        isVisible: Boolean(props.on),\n        data: props.on,\n        animate: 'none'\n      };\n    }\n\n    if (props.on) {\n      return {\n        isVisible: true,\n        data: props.on,\n        animate: 'open'\n      };\n    }\n\n    if (state.isVisible) {\n      return {\n        isVisible: true,\n        data: state.data,\n        animate: 'close'\n      };\n    }\n\n    return {\n      isVisible: false,\n      animate: 'close',\n      data: null\n    };\n  };\n\n  var _proto = AnimateInOut.prototype;\n\n  _proto.render = function render() {\n    if (!this.state.isVisible) {\n      return null;\n    }\n\n    var provided = {\n      onClose: this.onClose,\n      data: this.state.data,\n      animate: this.state.animate\n    };\n    return this.props.children(provided);\n  };\n\n  return AnimateInOut;\n}(React.PureComponent);\n\nvar zIndexOptions = {\n  dragging: 5000,\n  dropAnimating: 4500\n};\n\nvar getDraggingTransition = function getDraggingTransition(shouldAnimateDragMovement, dropping) {\n  if (dropping) {\n    return transitions.drop(dropping.duration);\n  }\n\n  if (shouldAnimateDragMovement) {\n    return transitions.snap;\n  }\n\n  return transitions.fluid;\n};\n\nvar getDraggingOpacity = function getDraggingOpacity(isCombining, isDropAnimating) {\n  if (!isCombining) {\n    return null;\n  }\n\n  return isDropAnimating ? combine.opacity.drop : combine.opacity.combining;\n};\n\nvar getShouldDraggingAnimate = function getShouldDraggingAnimate(dragging) {\n  if (dragging.forceShouldAnimate != null) {\n    return dragging.forceShouldAnimate;\n  }\n\n  return dragging.mode === 'SNAP';\n};\n\nfunction getDraggingStyle(dragging) {\n  var dimension = dragging.dimension;\n  var box = dimension.client;\n  var offset = dragging.offset,\n      combineWith = dragging.combineWith,\n      dropping = dragging.dropping;\n  var isCombining = Boolean(combineWith);\n  var shouldAnimate = getShouldDraggingAnimate(dragging);\n  var isDropAnimating = Boolean(dropping);\n  var transform = isDropAnimating ? transforms.drop(offset, isCombining) : transforms.moveTo(offset);\n  var style = {\n    position: 'fixed',\n    top: box.marginBox.top,\n    left: box.marginBox.left,\n    boxSizing: 'border-box',\n    width: box.borderBox.width,\n    height: box.borderBox.height,\n    transition: getDraggingTransition(shouldAnimate, dropping),\n    transform: transform,\n    opacity: getDraggingOpacity(isCombining, isDropAnimating),\n    zIndex: isDropAnimating ? zIndexOptions.dropAnimating : zIndexOptions.dragging,\n    pointerEvents: 'none'\n  };\n  return style;\n}\n\nfunction getSecondaryStyle(secondary) {\n  return {\n    transform: transforms.moveTo(secondary.offset),\n    transition: secondary.shouldAnimateDisplacement ? null : 'none'\n  };\n}\n\nfunction getStyle$1(mapped) {\n  return mapped.type === 'DRAGGING' ? getDraggingStyle(mapped) : getSecondaryStyle(mapped);\n}\n\nfunction getDimension$1(descriptor, el, windowScroll) {\n  if (windowScroll === void 0) {\n    windowScroll = origin;\n  }\n\n  var computedStyles = window.getComputedStyle(el);\n  var borderBox = el.getBoundingClientRect();\n  var client = calculateBox(borderBox, computedStyles);\n  var page = withScroll(client, windowScroll);\n  var placeholder = {\n    client: client,\n    tagName: el.tagName.toLowerCase(),\n    display: computedStyles.display\n  };\n  var displaceBy = {\n    x: client.marginBox.width,\n    y: client.marginBox.height\n  };\n  var dimension = {\n    descriptor: descriptor,\n    placeholder: placeholder,\n    displaceBy: displaceBy,\n    client: client,\n    page: page\n  };\n  return dimension;\n}\n\nfunction useDraggablePublisher(args) {\n  var uniqueId = useUniqueId('draggable');\n  var descriptor = args.descriptor,\n      registry = args.registry,\n      getDraggableRef = args.getDraggableRef,\n      canDragInteractiveElements = args.canDragInteractiveElements,\n      shouldRespectForcePress = args.shouldRespectForcePress,\n      isEnabled = args.isEnabled;\n  var options = useMemo(function () {\n    return {\n      canDragInteractiveElements: canDragInteractiveElements,\n      shouldRespectForcePress: shouldRespectForcePress,\n      isEnabled: isEnabled\n    };\n  }, [canDragInteractiveElements, isEnabled, shouldRespectForcePress]);\n  var getDimension = useCallback(function (windowScroll) {\n    var el = getDraggableRef();\n    !el ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Cannot get dimension when no ref is set') : invariant(false) : void 0;\n    return getDimension$1(descriptor, el, windowScroll);\n  }, [descriptor, getDraggableRef]);\n  var entry = useMemo(function () {\n    return {\n      uniqueId: uniqueId,\n      descriptor: descriptor,\n      options: options,\n      getDimension: getDimension\n    };\n  }, [descriptor, getDimension, options, uniqueId]);\n  var publishedRef = useRef(entry);\n  var isFirstPublishRef = useRef(true);\n  useIsomorphicLayoutEffect(function () {\n    registry.draggable.register(publishedRef.current);\n    return function () {\n      return registry.draggable.unregister(publishedRef.current);\n    };\n  }, [registry.draggable]);\n  useIsomorphicLayoutEffect(function () {\n    if (isFirstPublishRef.current) {\n      isFirstPublishRef.current = false;\n      return;\n    }\n\n    var last = publishedRef.current;\n    publishedRef.current = entry;\n    registry.draggable.update(entry, last);\n  }, [entry, registry.draggable]);\n}\n\nfunction useValidation$1(props, contextId, getRef) {\n  useDevSetupWarning(function () {\n    function prefix(id) {\n      return \"Draggable[id: \" + id + \"]: \";\n    }\n\n    var id = props.draggableId;\n    !id ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable requires a draggableId') : invariant(false) : void 0;\n    !(typeof id === 'string') ? process.env.NODE_ENV !== \"production\" ? invariant(false, \"Draggable requires a [string] draggableId.\\n      Provided: [type: \" + typeof id + \"] (value: \" + id + \")\") : invariant(false) : void 0;\n    !isInteger(props.index) ? process.env.NODE_ENV !== \"production\" ? invariant(false, prefix(id) + \" requires an integer index prop\") : invariant(false) : void 0;\n\n    if (props.mapped.type === 'DRAGGING') {\n      return;\n    }\n\n    checkIsValidInnerRef(getRef());\n\n    if (props.isEnabled) {\n      !findDragHandle(contextId, id) ? process.env.NODE_ENV !== \"production\" ? invariant(false, prefix(id) + \" Unable to find drag handle\") : invariant(false) : void 0;\n    }\n  });\n}\nfunction useClonePropValidation(isClone) {\n  useDev(function () {\n    var initialRef = useRef(isClone);\n    useDevSetupWarning(function () {\n      !(isClone === initialRef.current) ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Draggable isClone prop value changed during component life') : invariant(false) : void 0;\n    }, [isClone]);\n  });\n}\n\nfunction preventHtml5Dnd(event) {\n  event.preventDefault();\n}\n\nfunction Draggable(props) {\n  var ref = useRef(null);\n  var setRef = useCallback(function (el) {\n    ref.current = el;\n  }, []);\n  var getRef = useCallback(function () {\n    return ref.current;\n  }, []);\n\n  var _useRequiredContext = useRequiredContext(AppContext),\n      contextId = _useRequiredContext.contextId,\n      dragHandleUsageInstructionsId = _useRequiredContext.dragHandleUsageInstructionsId,\n      registry = _useRequiredContext.registry;\n\n  var _useRequiredContext2 = useRequiredContext(DroppableContext),\n      type = _useRequiredContext2.type,\n      droppableId = _useRequiredContext2.droppableId;\n\n  var descriptor = useMemo(function () {\n    return {\n      id: props.draggableId,\n      index: props.index,\n      type: type,\n      droppableId: droppableId\n    };\n  }, [props.draggableId, props.index, type, droppableId]);\n  var children = props.children,\n      draggableId = props.draggableId,\n      isEnabled = props.isEnabled,\n      shouldRespectForcePress = props.shouldRespectForcePress,\n      canDragInteractiveElements = props.canDragInteractiveElements,\n      isClone = props.isClone,\n      mapped = props.mapped,\n      dropAnimationFinishedAction = props.dropAnimationFinished;\n  useValidation$1(props, contextId, getRef);\n  useClonePropValidation(isClone);\n\n  if (!isClone) {\n    var forPublisher = useMemo(function () {\n      return {\n        descriptor: descriptor,\n        registry: registry,\n        getDraggableRef: getRef,\n        canDragInteractiveElements: canDragInteractiveElements,\n        shouldRespectForcePress: shouldRespectForcePress,\n        isEnabled: isEnabled\n      };\n    }, [descriptor, registry, getRef, canDragInteractiveElements, shouldRespectForcePress, isEnabled]);\n    useDraggablePublisher(forPublisher);\n  }\n\n  var dragHandleProps = useMemo(function () {\n    return isEnabled ? {\n      tabIndex: 0,\n      role: 'button',\n      'aria-describedby': dragHandleUsageInstructionsId,\n      'data-rbd-drag-handle-draggable-id': draggableId,\n      'data-rbd-drag-handle-context-id': contextId,\n      draggable: false,\n      onDragStart: preventHtml5Dnd\n    } : null;\n  }, [contextId, dragHandleUsageInstructionsId, draggableId, isEnabled]);\n  var onMoveEnd = useCallback(function (event) {\n    if (mapped.type !== 'DRAGGING') {\n      return;\n    }\n\n    if (!mapped.dropping) {\n      return;\n    }\n\n    if (event.propertyName !== 'transform') {\n      return;\n    }\n\n    dropAnimationFinishedAction();\n  }, [dropAnimationFinishedAction, mapped]);\n  var provided = useMemo(function () {\n    var style = getStyle$1(mapped);\n    var onTransitionEnd = mapped.type === 'DRAGGING' && mapped.dropping ? onMoveEnd : null;\n    var result = {\n      innerRef: setRef,\n      draggableProps: {\n        'data-rbd-draggable-context-id': contextId,\n        'data-rbd-draggable-id': draggableId,\n        style: style,\n        onTransitionEnd: onTransitionEnd\n      },\n      dragHandleProps: dragHandleProps\n    };\n    return result;\n  }, [contextId, dragHandleProps, draggableId, mapped, onMoveEnd, setRef]);\n  var rubric = useMemo(function () {\n    return {\n      draggableId: descriptor.id,\n      type: descriptor.type,\n      source: {\n        index: descriptor.index,\n        droppableId: descriptor.droppableId\n      }\n    };\n  }, [descriptor.droppableId, descriptor.id, descriptor.index, descriptor.type]);\n  return children(provided, mapped.snapshot, rubric);\n}\n\nvar isStrictEqual = (function (a, b) {\n  return a === b;\n});\n\nvar whatIsDraggedOverFromResult = (function (result) {\n  var combine = result.combine,\n      destination = result.destination;\n\n  if (destination) {\n    return destination.droppableId;\n  }\n\n  if (combine) {\n    return combine.droppableId;\n  }\n\n  return null;\n});\n\nvar getCombineWithFromResult = function getCombineWithFromResult(result) {\n  return result.combine ? result.combine.draggableId : null;\n};\n\nvar getCombineWithFromImpact = function getCombineWithFromImpact(impact) {\n  return impact.at && impact.at.type === 'COMBINE' ? impact.at.combine.draggableId : null;\n};\n\nfunction getDraggableSelector() {\n  var memoizedOffset = memoizeOne(function (x, y) {\n    return {\n      x: x,\n      y: y\n    };\n  });\n  var getMemoizedSnapshot = memoizeOne(function (mode, isClone, draggingOver, combineWith, dropping) {\n    return {\n      isDragging: true,\n      isClone: isClone,\n      isDropAnimating: Boolean(dropping),\n      dropAnimation: dropping,\n      mode: mode,\n      draggingOver: draggingOver,\n      combineWith: combineWith,\n      combineTargetFor: null\n    };\n  });\n  var getMemoizedProps = memoizeOne(function (offset, mode, dimension, isClone, draggingOver, combineWith, forceShouldAnimate) {\n    return {\n      mapped: {\n        type: 'DRAGGING',\n        dropping: null,\n        draggingOver: draggingOver,\n        combineWith: combineWith,\n        mode: mode,\n        offset: offset,\n        dimension: dimension,\n        forceShouldAnimate: forceShouldAnimate,\n        snapshot: getMemoizedSnapshot(mode, isClone, draggingOver, combineWith, null)\n      }\n    };\n  });\n\n  var selector = function selector(state, ownProps) {\n    if (state.isDragging) {\n      if (state.critical.draggable.id !== ownProps.draggableId) {\n        return null;\n      }\n\n      var offset = state.current.client.offset;\n      var dimension = state.dimensions.draggables[ownProps.draggableId];\n      var draggingOver = whatIsDraggedOver(state.impact);\n      var combineWith = getCombineWithFromImpact(state.impact);\n      var forceShouldAnimate = state.forceShouldAnimate;\n      return getMemoizedProps(memoizedOffset(offset.x, offset.y), state.movementMode, dimension, ownProps.isClone, draggingOver, combineWith, forceShouldAnimate);\n    }\n\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n\n      if (completed.result.draggableId !== ownProps.draggableId) {\n        return null;\n      }\n\n      var isClone = ownProps.isClone;\n      var _dimension = state.dimensions.draggables[ownProps.draggableId];\n      var result = completed.result;\n      var mode = result.mode;\n\n      var _draggingOver = whatIsDraggedOverFromResult(result);\n\n      var _combineWith = getCombineWithFromResult(result);\n\n      var duration = state.dropDuration;\n      var dropping = {\n        duration: duration,\n        curve: curves.drop,\n        moveTo: state.newHomeClientOffset,\n        opacity: _combineWith ? combine.opacity.drop : null,\n        scale: _combineWith ? combine.scale.drop : null\n      };\n      return {\n        mapped: {\n          type: 'DRAGGING',\n          offset: state.newHomeClientOffset,\n          dimension: _dimension,\n          dropping: dropping,\n          draggingOver: _draggingOver,\n          combineWith: _combineWith,\n          mode: mode,\n          forceShouldAnimate: null,\n          snapshot: getMemoizedSnapshot(mode, isClone, _draggingOver, _combineWith, dropping)\n        }\n      };\n    }\n\n    return null;\n  };\n\n  return selector;\n}\n\nfunction getSecondarySnapshot(combineTargetFor) {\n  return {\n    isDragging: false,\n    isDropAnimating: false,\n    isClone: false,\n    dropAnimation: null,\n    mode: null,\n    draggingOver: null,\n    combineTargetFor: combineTargetFor,\n    combineWith: null\n  };\n}\n\nvar atRest = {\n  mapped: {\n    type: 'SECONDARY',\n    offset: origin,\n    combineTargetFor: null,\n    shouldAnimateDisplacement: true,\n    snapshot: getSecondarySnapshot(null)\n  }\n};\n\nfunction getSecondarySelector() {\n  var memoizedOffset = memoizeOne(function (x, y) {\n    return {\n      x: x,\n      y: y\n    };\n  });\n  var getMemoizedSnapshot = memoizeOne(getSecondarySnapshot);\n  var getMemoizedProps = memoizeOne(function (offset, combineTargetFor, shouldAnimateDisplacement) {\n    if (combineTargetFor === void 0) {\n      combineTargetFor = null;\n    }\n\n    return {\n      mapped: {\n        type: 'SECONDARY',\n        offset: offset,\n        combineTargetFor: combineTargetFor,\n        shouldAnimateDisplacement: shouldAnimateDisplacement,\n        snapshot: getMemoizedSnapshot(combineTargetFor)\n      }\n    };\n  });\n\n  var getFallback = function getFallback(combineTargetFor) {\n    return combineTargetFor ? getMemoizedProps(origin, combineTargetFor, true) : null;\n  };\n\n  var getProps = function getProps(ownId, draggingId, impact, afterCritical) {\n    var visualDisplacement = impact.displaced.visible[ownId];\n    var isAfterCriticalInVirtualList = Boolean(afterCritical.inVirtualList && afterCritical.effected[ownId]);\n    var combine = tryGetCombine(impact);\n    var combineTargetFor = combine && combine.draggableId === ownId ? draggingId : null;\n\n    if (!visualDisplacement) {\n      if (!isAfterCriticalInVirtualList) {\n        return getFallback(combineTargetFor);\n      }\n\n      if (impact.displaced.invisible[ownId]) {\n        return null;\n      }\n\n      var change = negate(afterCritical.displacedBy.point);\n\n      var _offset = memoizedOffset(change.x, change.y);\n\n      return getMemoizedProps(_offset, combineTargetFor, true);\n    }\n\n    if (isAfterCriticalInVirtualList) {\n      return getFallback(combineTargetFor);\n    }\n\n    var displaceBy = impact.displacedBy.point;\n    var offset = memoizedOffset(displaceBy.x, displaceBy.y);\n    return getMemoizedProps(offset, combineTargetFor, visualDisplacement.shouldAnimate);\n  };\n\n  var selector = function selector(state, ownProps) {\n    if (state.isDragging) {\n      if (state.critical.draggable.id === ownProps.draggableId) {\n        return null;\n      }\n\n      return getProps(ownProps.draggableId, state.critical.draggable.id, state.impact, state.afterCritical);\n    }\n\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n\n      if (completed.result.draggableId === ownProps.draggableId) {\n        return null;\n      }\n\n      return getProps(ownProps.draggableId, completed.result.draggableId, completed.impact, completed.afterCritical);\n    }\n\n    return null;\n  };\n\n  return selector;\n}\n\nvar makeMapStateToProps = function makeMapStateToProps() {\n  var draggingSelector = getDraggableSelector();\n  var secondarySelector = getSecondarySelector();\n\n  var selector = function selector(state, ownProps) {\n    return draggingSelector(state, ownProps) || secondarySelector(state, ownProps) || atRest;\n  };\n\n  return selector;\n};\nvar mapDispatchToProps = {\n  dropAnimationFinished: dropAnimationFinished\n};\nvar ConnectedDraggable = connect(makeMapStateToProps, mapDispatchToProps, null, {\n  context: StoreContext,\n  pure: true,\n  areStatePropsEqual: isStrictEqual\n})(Draggable);\n\nfunction PrivateDraggable(props) {\n  var droppableContext = useRequiredContext(DroppableContext);\n  var isUsingCloneFor = droppableContext.isUsingCloneFor;\n\n  if (isUsingCloneFor === props.draggableId && !props.isClone) {\n    return null;\n  }\n\n  return React.createElement(ConnectedDraggable, props);\n}\nfunction PublicDraggable(props) {\n  var isEnabled = typeof props.isDragDisabled === 'boolean' ? !props.isDragDisabled : true;\n  var canDragInteractiveElements = Boolean(props.disableInteractiveElementBlocking);\n  var shouldRespectForcePress = Boolean(props.shouldRespectForcePress);\n  return React.createElement(PrivateDraggable, _extends({}, props, {\n    isClone: false,\n    isEnabled: isEnabled,\n    canDragInteractiveElements: canDragInteractiveElements,\n    shouldRespectForcePress: shouldRespectForcePress\n  }));\n}\n\nfunction Droppable(props) {\n  var appContext = useContext(AppContext);\n  !appContext ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'Could not find app context') : invariant(false) : void 0;\n  var contextId = appContext.contextId,\n      isMovementAllowed = appContext.isMovementAllowed;\n  var droppableRef = useRef(null);\n  var placeholderRef = useRef(null);\n  var children = props.children,\n      droppableId = props.droppableId,\n      type = props.type,\n      mode = props.mode,\n      direction = props.direction,\n      ignoreContainerClipping = props.ignoreContainerClipping,\n      isDropDisabled = props.isDropDisabled,\n      isCombineEnabled = props.isCombineEnabled,\n      snapshot = props.snapshot,\n      useClone = props.useClone,\n      updateViewportMaxScroll = props.updateViewportMaxScroll,\n      getContainerForClone = props.getContainerForClone;\n  var getDroppableRef = useCallback(function () {\n    return droppableRef.current;\n  }, []);\n  var setDroppableRef = useCallback(function (value) {\n    droppableRef.current = value;\n  }, []);\n  var getPlaceholderRef = useCallback(function () {\n    return placeholderRef.current;\n  }, []);\n  var setPlaceholderRef = useCallback(function (value) {\n    placeholderRef.current = value;\n  }, []);\n  useValidation({\n    props: props,\n    getDroppableRef: getDroppableRef,\n    getPlaceholderRef: getPlaceholderRef\n  });\n  var onPlaceholderTransitionEnd = useCallback(function () {\n    if (isMovementAllowed()) {\n      updateViewportMaxScroll({\n        maxScroll: getMaxWindowScroll()\n      });\n    }\n  }, [isMovementAllowed, updateViewportMaxScroll]);\n  useDroppablePublisher({\n    droppableId: droppableId,\n    type: type,\n    mode: mode,\n    direction: direction,\n    isDropDisabled: isDropDisabled,\n    isCombineEnabled: isCombineEnabled,\n    ignoreContainerClipping: ignoreContainerClipping,\n    getDroppableRef: getDroppableRef\n  });\n  var placeholder = React.createElement(AnimateInOut, {\n    on: props.placeholder,\n    shouldAnimate: props.shouldAnimatePlaceholder\n  }, function (_ref) {\n    var onClose = _ref.onClose,\n        data = _ref.data,\n        animate = _ref.animate;\n    return React.createElement(Placeholder$1, {\n      placeholder: data,\n      onClose: onClose,\n      innerRef: setPlaceholderRef,\n      animate: animate,\n      contextId: contextId,\n      onTransitionEnd: onPlaceholderTransitionEnd\n    });\n  });\n  var provided = useMemo(function () {\n    return {\n      innerRef: setDroppableRef,\n      placeholder: placeholder,\n      droppableProps: {\n        'data-rbd-droppable-id': droppableId,\n        'data-rbd-droppable-context-id': contextId\n      }\n    };\n  }, [contextId, droppableId, placeholder, setDroppableRef]);\n  var isUsingCloneFor = useClone ? useClone.dragging.draggableId : null;\n  var droppableContext = useMemo(function () {\n    return {\n      droppableId: droppableId,\n      type: type,\n      isUsingCloneFor: isUsingCloneFor\n    };\n  }, [droppableId, isUsingCloneFor, type]);\n\n  function getClone() {\n    if (!useClone) {\n      return null;\n    }\n\n    var dragging = useClone.dragging,\n        render = useClone.render;\n    var node = React.createElement(PrivateDraggable, {\n      draggableId: dragging.draggableId,\n      index: dragging.source.index,\n      isClone: true,\n      isEnabled: true,\n      shouldRespectForcePress: false,\n      canDragInteractiveElements: true\n    }, function (draggableProvided, draggableSnapshot) {\n      return render(draggableProvided, draggableSnapshot, dragging);\n    });\n    return ReactDOM.createPortal(node, getContainerForClone());\n  }\n\n  return React.createElement(DroppableContext.Provider, {\n    value: droppableContext\n  }, children(provided, snapshot), getClone());\n}\n\nvar isMatchingType = function isMatchingType(type, critical) {\n  return type === critical.droppable.type;\n};\n\nvar getDraggable = function getDraggable(critical, dimensions) {\n  return dimensions.draggables[critical.draggable.id];\n};\n\nvar makeMapStateToProps$1 = function makeMapStateToProps() {\n  var idleWithAnimation = {\n    placeholder: null,\n    shouldAnimatePlaceholder: true,\n    snapshot: {\n      isDraggingOver: false,\n      draggingOverWith: null,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: false\n    },\n    useClone: null\n  };\n\n  var idleWithoutAnimation = _extends({}, idleWithAnimation, {\n    shouldAnimatePlaceholder: false\n  });\n\n  var getDraggableRubric = memoizeOne(function (descriptor) {\n    return {\n      draggableId: descriptor.id,\n      type: descriptor.type,\n      source: {\n        index: descriptor.index,\n        droppableId: descriptor.droppableId\n      }\n    };\n  });\n  var getMapProps = memoizeOne(function (id, isEnabled, isDraggingOverForConsumer, isDraggingOverForImpact, dragging, renderClone) {\n    var draggableId = dragging.descriptor.id;\n    var isHome = dragging.descriptor.droppableId === id;\n\n    if (isHome) {\n      var useClone = renderClone ? {\n        render: renderClone,\n        dragging: getDraggableRubric(dragging.descriptor)\n      } : null;\n      var _snapshot = {\n        isDraggingOver: isDraggingOverForConsumer,\n        draggingOverWith: isDraggingOverForConsumer ? draggableId : null,\n        draggingFromThisWith: draggableId,\n        isUsingPlaceholder: true\n      };\n      return {\n        placeholder: dragging.placeholder,\n        shouldAnimatePlaceholder: false,\n        snapshot: _snapshot,\n        useClone: useClone\n      };\n    }\n\n    if (!isEnabled) {\n      return idleWithoutAnimation;\n    }\n\n    if (!isDraggingOverForImpact) {\n      return idleWithAnimation;\n    }\n\n    var snapshot = {\n      isDraggingOver: isDraggingOverForConsumer,\n      draggingOverWith: draggableId,\n      draggingFromThisWith: null,\n      isUsingPlaceholder: true\n    };\n    return {\n      placeholder: dragging.placeholder,\n      shouldAnimatePlaceholder: true,\n      snapshot: snapshot,\n      useClone: null\n    };\n  });\n\n  var selector = function selector(state, ownProps) {\n    var id = ownProps.droppableId;\n    var type = ownProps.type;\n    var isEnabled = !ownProps.isDropDisabled;\n    var renderClone = ownProps.renderClone;\n\n    if (state.isDragging) {\n      var critical = state.critical;\n\n      if (!isMatchingType(type, critical)) {\n        return idleWithoutAnimation;\n      }\n\n      var dragging = getDraggable(critical, state.dimensions);\n      var isDraggingOver = whatIsDraggedOver(state.impact) === id;\n      return getMapProps(id, isEnabled, isDraggingOver, isDraggingOver, dragging, renderClone);\n    }\n\n    if (state.phase === 'DROP_ANIMATING') {\n      var completed = state.completed;\n\n      if (!isMatchingType(type, completed.critical)) {\n        return idleWithoutAnimation;\n      }\n\n      var _dragging = getDraggable(completed.critical, state.dimensions);\n\n      return getMapProps(id, isEnabled, whatIsDraggedOverFromResult(completed.result) === id, whatIsDraggedOver(completed.impact) === id, _dragging, renderClone);\n    }\n\n    if (state.phase === 'IDLE' && state.completed && !state.shouldFlush) {\n      var _completed = state.completed;\n\n      if (!isMatchingType(type, _completed.critical)) {\n        return idleWithoutAnimation;\n      }\n\n      var wasOver = whatIsDraggedOver(_completed.impact) === id;\n      var wasCombining = Boolean(_completed.impact.at && _completed.impact.at.type === 'COMBINE');\n      var isHome = _completed.critical.droppable.id === id;\n\n      if (wasOver) {\n        return wasCombining ? idleWithAnimation : idleWithoutAnimation;\n      }\n\n      if (isHome) {\n        return idleWithAnimation;\n      }\n\n      return idleWithoutAnimation;\n    }\n\n    return idleWithoutAnimation;\n  };\n\n  return selector;\n};\nvar mapDispatchToProps$1 = {\n  updateViewportMaxScroll: updateViewportMaxScroll\n};\n\nfunction getBody() {\n  !document.body ? process.env.NODE_ENV !== \"production\" ? invariant(false, 'document.body is not ready') : invariant(false) : void 0;\n  return document.body;\n}\n\nvar defaultProps = {\n  mode: 'standard',\n  type: 'DEFAULT',\n  direction: 'vertical',\n  isDropDisabled: false,\n  isCombineEnabled: false,\n  ignoreContainerClipping: false,\n  renderClone: null,\n  getContainerForClone: getBody\n};\nvar ConnectedDroppable = connect(makeMapStateToProps$1, mapDispatchToProps$1, null, {\n  context: StoreContext,\n  pure: true,\n  areStatePropsEqual: isStrictEqual\n})(Droppable);\nConnectedDroppable.defaultProps = defaultProps;\n\nexport { DragDropContext, PublicDraggable as Draggable, ConnectedDroppable as Droppable, resetServerContext, useKeyboardSensor, useMouseSensor, useTouchSensor };\n", "/** @license React v17.0.2\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var b=60103,c=60106,d=60107,e=60108,f=60114,g=60109,h=60110,k=60112,l=60113,m=60120,n=60115,p=60116,q=60121,r=60122,u=60117,v=60129,w=60131;\nif(\"function\"===typeof Symbol&&Symbol.for){var x=Symbol.for;b=x(\"react.element\");c=x(\"react.portal\");d=x(\"react.fragment\");e=x(\"react.strict_mode\");f=x(\"react.profiler\");g=x(\"react.provider\");h=x(\"react.context\");k=x(\"react.forward_ref\");l=x(\"react.suspense\");m=x(\"react.suspense_list\");n=x(\"react.memo\");p=x(\"react.lazy\");q=x(\"react.block\");r=x(\"react.server.block\");u=x(\"react.fundamental\");v=x(\"react.debug_trace_mode\");w=x(\"react.legacy_hidden\")}\nfunction y(a){if(\"object\"===typeof a&&null!==a){var t=a.$$typeof;switch(t){case b:switch(a=a.type,a){case d:case f:case e:case l:case m:return a;default:switch(a=a&&a.$$typeof,a){case h:case k:case p:case n:case g:return a;default:return t}}case c:return t}}}var z=g,A=b,B=k,C=d,D=p,E=n,F=c,G=f,H=e,I=l;exports.ContextConsumer=h;exports.ContextProvider=z;exports.Element=A;exports.ForwardRef=B;exports.Fragment=C;exports.Lazy=D;exports.Memo=E;exports.Portal=F;exports.Profiler=G;exports.StrictMode=H;\nexports.Suspense=I;exports.isAsyncMode=function(){return!1};exports.isConcurrentMode=function(){return!1};exports.isContextConsumer=function(a){return y(a)===h};exports.isContextProvider=function(a){return y(a)===g};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===b};exports.isForwardRef=function(a){return y(a)===k};exports.isFragment=function(a){return y(a)===d};exports.isLazy=function(a){return y(a)===p};exports.isMemo=function(a){return y(a)===n};\nexports.isPortal=function(a){return y(a)===c};exports.isProfiler=function(a){return y(a)===f};exports.isStrictMode=function(a){return y(a)===e};exports.isSuspense=function(a){return y(a)===l};exports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===d||a===f||a===v||a===e||a===l||a===m||a===w||\"object\"===typeof a&&null!==a&&(a.$$typeof===p||a.$$typeof===n||a.$$typeof===g||a.$$typeof===h||a.$$typeof===k||a.$$typeof===u||a.$$typeof===q||a[0]===r)?!0:!1};\nexports.typeOf=y;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n"], "names": ["safeIsNaN", "Number", "isNaN", "value", "areInputsEqual", "newInputs", "lastInputs", "length", "i", "first", "second", "resultFn", "isEqual", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "arguments", "this", "apply", "formatProdErrorMessage", "code", "$$observable", "Symbol", "observable", "randomString", "Math", "random", "toString", "substring", "split", "join", "ActionTypes", "INIT", "REPLACE", "PROBE_UNKNOWN_ACTION", "isPlainObject", "obj", "proto", "Object", "getPrototypeOf", "createStore", "reducer", "preloadedState", "enhancer", "_ref2", "Error", "undefined", "currentReducer", "currentState", "currentListeners", "nextListeners", "isDispatching", "ensureCanMutateNextListeners", "slice", "getState", "subscribe", "listener", "isSubscribed", "push", "index", "indexOf", "splice", "dispatch", "action", "type", "listeners", "replaceReducer", "nextReducer", "_ref", "outerSubscribe", "observer", "observeState", "next", "unsubscribe", "bindActionCreator", "actionCreator", "bindActionCreators", "actionCreators", "boundActionCreators", "key", "compose", "_len", "funcs", "Array", "_key", "arg", "reduce", "a", "b", "ReactReduxContext", "React", "batch", "callback", "getBatch", "nullListeners", "notify", "get", "createSubscription", "store", "parentSub", "handleChangeWrapper", "subscription", "onStateChange", "trySubscribe", "addNestedSub", "last", "clear", "prev", "createListenerCollection", "notifyNestedSubs", "Boolean", "tryUnsubscribe", "getListeners", "useIsomorphicLayoutEffect", "window", "document", "createElement", "useLayoutEffect", "useEffect", "context", "children", "contextValue", "useMemo", "previousState", "Context", "Provider", "_excluded", "_excluded2", "EMPTY_ARRAY", "NO_SUBSCRIPTION_ARRAY", "storeStateUpdatesReducer", "state", "updateCount", "payload", "useIsomorphicLayoutEffectWithArgs", "effectFunc", "effectArgs", "dependencies", "captureWrapperProps", "lastWrapperProps", "lastChildProps", "renderIsScheduled", "wrapperProps", "actualChildProps", "childPropsFromStoreUpdate", "current", "subscribeUpdates", "shouldHandleStateChanges", "childPropsSelector", "forceComponentUpdateDispatch", "didUnsubscribe", "lastThrownError", "checkForUpdates", "newChildProps", "error", "latestStoreState", "e", "initStateUpdates", "connectAdvanced", "selectorFactory", "_ref2$getDisplayName", "getDisplayName", "name", "_ref2$methodName", "methodName", "_ref2$renderCountProp", "renderCountProp", "_ref2$shouldHandleSta", "_ref2$storeKey", "storeKey", "_ref2$forwardRef", "with<PERSON>ef", "forwardRef", "_ref2$context", "connectOptions", "_objectWithoutPropertiesLoose", "WrappedComponent", "wrappedComponentName", "displayName", "selectorFactoryOptions", "_extends", "pure", "usePureOnlyMemo", "ConnectFunction", "props", "_useMemo", "reactReduxForwardedRef", "props<PERSON><PERSON><PERSON><PERSON>", "ContextToUse", "Consumer", "isContextConsumer", "useContext", "didStoreComeFromProps", "createChildSelector", "_useMemo2", "bind", "overriddenContextValue", "_useReducer", "useReducer", "previousStateUpdateResult", "useRef", "renderedWrappedComponent", "ref", "Connect", "forwarded", "hoistStatics", "is", "x", "y", "shallowEqual", "objA", "objB", "keysA", "keys", "keysB", "prototype", "hasOwnProperty", "call", "wrapMapToPropsConstant", "getConstant", "options", "constant", "constantSelector", "dependsOnOwnProps", "getDependsOnOwnProps", "mapToProps", "wrapMapToPropsFunc", "proxy", "stateOrDispatch", "ownProps", "mapDispatchToProps", "_loop", "mapStateToProps", "defaultMergeProps", "stateProps", "dispatchProps", "mergeProps", "mergedProps", "areMergedPropsEqual", "hasRunOnce", "nextMergedProps", "wrapMergePropsFunc", "impureFinalPropsSelectorFactory", "pureFinalPropsSelectorFactory", "areStatesEqual", "areOwnPropsEqual", "areStatePropsEqual", "hasRunAtLeastOnce", "handleSubsequentCalls", "nextState", "nextOwnProps", "propsChanged", "stateChanged", "nextStateProps", "statePropsChanged", "handleNewState", "finalPropsSelectorFactory", "initMapStateToProps", "initMapDispatchToProps", "initMergeProps", "match", "factories", "result", "strictEqual", "createConnect", "_temp", "_ref$connectHOC", "connectHOC", "_ref$mapStateToPropsF", "mapStateToPropsFactories", "defaultMapStateToPropsFactories", "_ref$mapDispatchToPro", "mapDispatchToPropsFactories", "defaultMapDispatchToPropsFactories", "_ref$mergePropsFactor", "mergePropsFactories", "defaultMergePropsFactories", "_ref$selectorFactory", "defaultSelectorFactory", "_ref3", "_ref3$pure", "_ref3$areStatesEqual", "_ref3$areOwnPropsEqua", "_ref3$areStatePropsEq", "_ref3$areMergedPropsE", "extraOptions", "newBatch", "useMemoOne", "getResult", "inputs", "initial", "useState", "isFirstRun", "committed", "cache", "useCallback", "getRect", "top", "right", "bottom", "left", "width", "height", "center", "expand", "target", "expandBy", "shrink", "shrinkBy", "noSpacing", "createBox", "borderBox", "_ref2$margin", "margin", "_ref2$border", "border", "_ref2$padding", "padding", "marginBox", "paddingBox", "contentBox", "parse", "raw", "invariant", "offset", "original", "change", "shiftBy", "shifted", "withScroll", "scroll", "pageXOffset", "pageYOffset", "calculateBox", "styles", "marginTop", "marginRight", "marginBottom", "marginLeft", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "borderTopWidth", "borderRightWidth", "borderBottomWidth", "borderLeftWidth", "getBox", "el", "getBoundingClientRect", "getComputedStyle", "fn", "frameId", "wrapperFn", "args", "requestAnimationFrame", "cancel", "cancelAnimationFrame", "log", "message", "noop", "bindEvents", "bindings", "sharedOptions", "unbindings", "map", "binding", "shared", "fromBinding", "getOptions", "addEventListener", "eventName", "removeEventListener", "for<PERSON>ach", "unbind", "prefix", "RbdInvariant", "condition", "Error<PERSON>ou<PERSON><PERSON>", "_React$Component", "_this", "concat", "callbacks", "onWindowError", "event", "getCallbacks", "isDragging", "tryAbort", "preventDefault", "setCallbacks", "_inherits<PERSON><PERSON>e", "_proto", "componentDidMount", "componentDidCatch", "err", "setState", "componentWillUnmount", "render", "position", "withLocation", "source", "destination", "isInHomeList", "droppableId", "startPosition", "endPosition", "<PERSON><PERSON><PERSON><PERSON>", "id", "combine", "draggableId", "returnedToStart", "preset", "start", "update", "location", "reason", "origin", "add", "point1", "point2", "subtract", "negate", "point", "patch", "line", "otherValue", "distance", "sqrt", "pow", "closest", "points", "min", "offsetByPosition", "spacing", "getCorners", "clip", "frame", "shouldClipSubject", "subject", "max", "executeClip", "pageMarginBox", "getSubject", "page", "withPlaceholder", "axis", "scrolled", "diff", "displacement", "increased", "_extends2", "increasedBy", "end", "increase", "active", "scrollDroppable", "droppable", "newScroll", "scrollable", "scrollDiff", "scrollDisplacement", "values", "findIndex", "list", "predicate", "find", "toArray", "toDroppableMap", "memoizeOne", "droppables", "previous", "descriptor", "toDraggableMap", "draggables", "toDroppableList", "toDraggableList", "getDraggablesInsideDroppable", "filter", "draggable", "sort", "tryGetDestination", "impact", "at", "tryGetCombine", "removeDraggableFromList", "remove", "item", "isHomeOf", "noDisplacedBy", "emptyGroups", "invisible", "visible", "all", "noImpact", "displaced", "displacedBy", "<PERSON><PERSON><PERSON><PERSON>", "lowerBound", "upperBound", "isPartiallyVisibleThroughFrame", "isWithinVertical", "isWithinHorizontal", "isPartiallyVisibleVertically", "isPartiallyVisibleHorizontally", "isBiggerVertically", "isBiggerHorizontally", "isTotallyVisibleThroughFrame", "vertical", "direction", "crossAxisLine", "size", "crossAxisStart", "crossAxisEnd", "crossAxisSize", "horizontal", "isVisible", "toBeDisplaced", "viewport", "withDroppableDisplacement", "isVisibleThroughFrameFn", "<PERSON><PERSON><PERSON><PERSON>", "getDroppableDisplaced", "isVisibleInDroppable", "isVisibleInViewport", "isPartiallyVisible", "isTotallyVisible", "getDisplacementGroups", "afterDragging", "forceShouldAnimate", "groups", "get<PERSON><PERSON><PERSON>", "shouldAnimate", "getShouldAnimate", "goAtEnd", "insideDestination", "inHomeList", "newIndex", "indexOfLastItem", "getIndexOfLastItem", "calculateReorderImpact", "withoutDragging", "sliceFrom", "didStartAfterCritical", "afterCritical", "effected", "moveToNextIndex", "isMovingForward", "previousImpact", "wasAt", "_newIndex", "currentIndex", "proposedIndex", "firstIndex", "lastIndex", "fromReorder", "isCombineEnabled", "combineId", "combineWithIndex", "fromCombine", "whenCombining", "combineWith", "displaceBy", "isDisplaced", "getCombinedItemDisplacement", "distanceFromStartToBorderBoxCenter", "box", "getCrossAxisBorderBoxCenter", "isMoving", "goAfter", "moveRelativeTo", "goBefore", "distanceFromEndToBorderBoxCenter", "whenReordering", "draggablePage", "moveInto", "goIntoStart", "closestAfter", "withDisplacement", "getPageBorderBoxCenterFromImpact", "withoutDisplacement", "getResultWithoutDroppableDisplacement", "scrollViewport", "getDraggables", "ids", "getClientFromPageBorderBoxCenter", "pageBorderBoxCenter", "withoutPageScrollChange", "withViewportDisplacement", "client", "isTotallyVisibleInNewLocation", "newPageBorderBoxCenter", "_ref$onlyOnMainAxis", "onlyOnMainAxis", "changeNeeded", "isTotallyVisibleOnAxis", "moveToNextPlace", "previousPageBorderBoxCenter", "previousClientSelection", "isEnabled", "getImpact", "closestId", "withoutDraggable", "indexOfClosest", "d", "moveToNextCombine", "clientSelection", "scrollJumpRequest", "cautious", "maxScroll<PERSON>hange", "scrolledViewport", "scrolledDroppable", "withViewportScroll", "withDroppableScroll", "tryGetVisible", "speculativelyIncrease", "getKnownActive", "rect", "getCurrentPageBorderBoxCenter", "getCurrentPageBorderBox", "getDisplacedBy", "withMaxScroll", "addPlaceholder", "placeholderSize", "requiredGrowth", "mode", "availableSpace", "needsToGrowBy", "sum", "dimension", "getRequiredGrowthForPlaceholder", "added", "oldFrameMaxScroll", "_subject", "maxScroll", "newFrame", "moveCrossAxis", "isOver", "isBetweenSourceClipped", "candidates", "activeOfTarget", "isBetweenDestinationClipped", "array", "contains", "isWithinDroppable", "getBestCrossAxisDroppable", "sorted", "distanceToA", "distanceToB", "getClosestDraggable", "proposed", "proposedPageBorderBoxCenter", "isGoingBeforeTarget", "relativeTo", "moveToNewDroppable", "whatIsDraggedOver", "moveInDirection", "isActuallyOver", "getDroppableOver", "dimensions", "isMainAxisMovementAllowed", "home", "critical", "isMovingOnMainAxis", "borderBoxCenter", "_state$dimensions", "selection", "isMovementAllowed", "phase", "isPositionInFrame", "getDroppableOver$1", "pageBorderBox", "childCenter", "isContained", "isStartContained", "isEndContained", "startCenter", "candidate", "getFurthestAway", "offsetRectByPosition", "getIsDisplaced", "getDragImpact", "pageOffset", "destinationId", "pageBorderBoxWithDroppableScroll", "area", "targetRect", "targetStart", "targetEnd", "child", "childRect", "threshold", "didStartAfterCritical$1", "getCombineImpact", "atIndex", "getReorderImpact", "patchDroppableMap", "updated", "clearUnusedPlaceholder", "now", "lastDroppable", "_subject2", "oldMaxScroll", "removePlaceholder", "forcedClientSelection", "forcedDimensions", "forcedViewport", "forcedImpact", "newImpact", "withUpdatedPlaceholders", "cleaned", "patched", "recomputePlaceholders", "recompute", "getDraggables$1", "getClientBorderBoxCenter", "refreshSnap", "movementMode", "needsVisibilityCheck", "getLiftEffect", "insideHome", "rawIndex", "inVirtualList", "process", "finish", "adjustAdditionsForScrollChanges", "additions", "updatedDroppables", "windowScrollChange", "getFrame", "droppableScrollChange", "moved", "offset$1", "initialWindowScroll", "placeholder", "offsetDraggable", "isSnapping", "postDroppableChange", "isEnabledChanging", "patchDimensionMap", "removeScrollJumpRequest", "idle", "completed", "<PERSON><PERSON><PERSON><PERSON>", "_action$payload", "isWindowScrollAllowed", "every", "isFixedOnPage", "_getLiftEffect", "onLiftImpact", "published", "withScrollChange", "modified", "existing", "updatedAdditions", "removals", "wasOverId", "wasOver", "draggingState", "isWaiting", "publishWhileDraggingInVirtual", "_clientSelection", "_action$payload2", "_action$payload3", "_id", "_target", "_action$payload4", "_id2", "_target2", "_updated", "_newScroll", "_viewport", "_result2", "_action$payload5", "dropDuration", "newHomeClientOffset", "publishWhileDragging", "collectionStarting", "updateDroppableScroll", "updateDroppableIsEnabled", "updateDroppableIsCombineEnabled", "move", "moveUp", "moveDown", "moveRight", "moveLeft", "completeDrop", "drop", "dropAnimationFinished", "curves", "combining", "outOfTheWayTiming", "transitions", "fluid", "snap", "duration", "timing", "outOfTheWay", "moveTo", "transforms", "isCombining", "translate", "minDropTime", "maxDropTime", "dropTimeRange", "drop$1", "_getDropImpact", "lastImpact", "didDropInsideDroppable", "getDropImpact", "newClientCenter", "getNewHomeClientOffset", "distance$1", "toFixed", "getDropDuration", "animateDrop", "dropPending", "getWindowScroll", "getScrollListener", "onWindowScroll", "scheduled", "rafSchd", "passive", "capture", "getWindowScrollBinding", "isActive", "stop", "scrollListener", "shouldEnd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "timerId", "setTimeout", "execute", "entry", "flush", "shallow", "clearTimeout", "with<PERSON><PERSON><PERSON>", "getDragStart", "responder", "data", "announce", "getDefaultMessage", "willExpire", "wasCalled", "isExpired", "timeoutId", "getExpiringAnnounce", "responders", "getResponders", "publisher", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dragging", "onDragEnd", "beforeCapture", "onBeforeCapture", "beforeStart", "onBeforeDragStart", "lastCritical", "lastLocation", "lastCombine", "onDragStart", "hasCriticalChanged", "isDraggableEqual", "isDroppableEqual", "isCriticalEqual", "hasLocationChanged", "hasGroupingChanged", "isCombineEqual", "onDragUpdate", "abort", "getPublisher", "dropAnimationFinish", "dropAnimationFlushOnScroll", "once", "pendingDrop", "postActionState", "composeEnhancers", "marshal", "dimension<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "style<PERSON><PERSON><PERSON>", "autoScroller", "createStore$1", "middlewares", "_dispatch", "middlewareAPI", "chain", "middleware", "_objectSpread", "applyMiddleware", "dropping", "resting", "stopPublishing", "dimensionMarshalStopper", "request", "scrollOptions", "shouldPublishImmediately", "_marshal$startPublish", "startPublishing", "lift$1", "shouldStop", "autoScroll", "isWatching", "tryRecordFoc<PERSON>", "tryRestoreFocusRecorded", "tryShiftRecord", "focus", "getMaxScroll", "scrollHeight", "scrollWidth", "getDocumentElement", "doc", "documentElement", "getMaxWindowScroll", "clientWidth", "clientHeight", "getInitialPublish", "registry", "getViewport", "windowScroll", "getAllByType", "getDimensionAndWatchScroll", "getDimension", "shouldPublishUpdate", "getById", "createDimensionMarshal", "collection", "staging", "collect", "_staging", "getScrollWhileDragging", "publish", "createPublisher", "subscriber", "exists", "dragStopped", "canStartDrag", "scrollWindow", "scrollBy", "getScrollableDroppables", "getBestScrollableDroppable", "_dimension", "maybe", "getScrollableDroppableOver", "config", "percentage", "stopDampeningAt", "accelerateAt", "getPercentage", "startOfRange", "endOfRange", "range", "stopAt", "getValue", "distanceToEdge", "thresholds", "dragStartTime", "shouldUseTimeDampening", "startScrollingFrom", "maxScrollValueAt", "percentageFromMaxScrollValueAt", "ceil", "getValueFromDistance", "proposedScroll", "runTime", "Date", "betweenAccelerateAtAndStopAtPercentage", "dampenValueByTime", "getScrollOnAxis", "container", "distanceToEdges", "getDistanceThresholds", "clean$2", "getScroll", "required", "limited", "isTooBigVertically", "isTooBigHorizontally", "adjustForSizeLimits", "smallestSigned", "getOverlap", "get<PERSON><PERSON><PERSON>", "targetScroll", "overlap", "canPartiallyScroll", "rawMax", "smallestChange", "canScrollWindow", "canScrollDroppable", "scroll$1", "_change", "getWindowScrollChange", "getDroppableScrollChange", "createJumpScroller", "scrollDroppableAsMuchAsItCan", "getDroppableOverlap", "whatTheDroppableCanScroll", "scrollWindowAsMuchAsItCan", "getWindowOverlap", "whatTheWindowCanScroll", "droppableRemainder", "windowRemainder", "moveByOffset", "createAutoScroller", "fluidScroller", "scheduleWindowScroll", "scheduleDroppableScroll", "tryScroll", "_dragging", "wasScrollNeeded", "fakeScrollCallback", "createFluidScroller", "jumpScroll", "dragHandle", "base", "prefix$1", "contextId", "scrollContainer", "getStyles", "rules", "property", "rule", "selector", "getHead", "head", "querySelector", "createStyleEl", "nonce", "setAttribute", "useStyleMarshal", "getSelector", "attribute", "dragHandle$1", "grabCursor", "always", "dropAnimating", "transition", "userCancel", "getStyles$1", "alwaysRef", "dynamicRef", "setDynamicStyle", "textContent", "setAlwaysStyle", "dynamic", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "getWindowFromEl", "ownerDocument", "defaultView", "isHtmlElement", "HTMLElement", "findDragHandle", "possible", "querySelectorAll", "handle", "getAttribute", "createRegistry", "subscribers", "cb", "findDraggableById", "findDroppableById", "register", "uniqueId", "unregister", "findById", "clean", "StoreContext", "getBodyElement", "body", "visuallyHidden", "overflow", "count", "defaults", "separator", "useUniqueId", "AppContext", "useDev", "useHook", "useDevSetupWarning", "useStartupValidation", "usePrevious", "_<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "preventStandardKeyEvents", "keyCode", "supportedEventName", "_scrollJump<PERSON>eys", "idle$1", "getCaptureBindings", "getPhase", "setPhase", "button", "clientX", "clientY", "actions", "pending", "abs", "fluidLift", "shouldBlockNextClick", "shouldRespectForcePress", "noop$1", "scrollJumpKeys", "getDraggingBindings", "idle$2", "interactiveTagNames", "input", "textarea", "select", "option", "optgroup", "video", "audio", "isAnInteractiveElement", "parent", "tagName", "toLowerCase", "parentElement", "isEventInInteractiveElement", "getBorderBoxCenterPosition", "supportedMatchesName", "Element", "closestPonyfill", "closest$1", "findClosestDragHandleFromEvent", "_isActive", "expected", "isLockActive", "<PERSON><PERSON><PERSON><PERSON>", "canStart", "lockAPI", "isClaimed", "tryStart", "forceSensorStop", "sourceEvent", "draggable$1", "findDraggable", "canDragInteractiveElements", "lock", "claim", "getShouldRespectForcePress", "tryDispatchWhenDragging", "getAction", "release", "cleanup", "lift", "liftActionArgs", "move$1", "api", "snapLift", "defaultSensors", "phaseRef", "unbindEventsRef", "startCaptureBinding", "defaultPrevented", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "altKey", "findClosestDraggableId", "tryGetLock", "startPendingDrag", "preventForcePressBinding", "findOptionsForDraggable", "canGetLock", "listenForCapture", "bindCapturingEvents", "preDrag", "isCapturing", "touch", "touches", "longPressTimerId", "unbindTarget", "hasMoved", "_event$touches$", "force", "shouldRespect", "getHandleBindings", "unbind<PERSON><PERSON>ow", "getWindowBindings", "startDragging", "useSensorMarshal", "_ref4", "customSensors", "enableDefaultSensors", "useSensors", "abandon", "newLock", "tryAbandon", "create", "tryAbandonLock", "forceStop", "tryGetClosestDraggableIdFromEvent", "tryReleaseLock", "isLockClaimed", "getStore", "lazyRef", "App", "sensors", "dragHandleUsageInstructions", "lazyStoreRef", "lastPropsRef", "createResponders", "getId", "style", "useAnnouncer", "dragHandleUsageInstructionsId", "text", "display", "useHiddenTextElement", "lazyDispatch", "marshalCallbacks", "useRegistry", "entriesRef", "recordRef", "restoreFocusFrameRef", "isMountedRef", "tryGiveFocus", "tryGiveFocusTo", "activeElement", "redirectTo", "record", "focused", "useFocusMarshal", "tryResetStore", "getCanLift", "getIsMovementAllowed", "appContext", "canLift", "count$1", "DragDropContext", "isEqual$1", "isScroll", "isAuto", "is<PERSON><PERSON><PERSON>", "overflowX", "overflowY", "isElementScrollable", "getClosestScrollable", "getScroll$1", "scrollLeft", "scrollTop", "getIsFixed", "getEnv", "closestScrollable", "env", "isDropDisabled", "targetRef", "getClient", "frameClient", "scrollSize", "getDroppableDimension", "immediate", "delayed", "getListenerOptions", "useRequiredContext", "getClosestScrollableFromDrag", "noop$2", "empty", "getStyle", "isAnimatingOpenOnMount", "animate", "getSize", "boxSizing", "flexShrink", "flexGrow", "pointerEvents", "Placeholder$1", "animateOpenTimerRef", "tryClearAnimateOpenTimer", "onTransitionEnd", "onClose", "_useState", "setIsAnimatingOpenOnMount", "onSizeChangeEnd", "propertyName", "innerRef", "DroppableContext", "AnimateInOut", "_React$PureComponent", "on", "getDerivedStateFromProps", "provided", "zIndexOptions", "getDraggingTransition", "shouldAnimateDragMovement", "getDraggingOpacity", "isDropAnimating", "getStyle$1", "mapped", "getShouldDraggingAnimate", "transform", "opacity", "zIndex", "getDraggingStyle", "secondary", "shouldAnimateDisplacement", "useDraggablePublisher", "getDraggableRef", "computedStyles", "getDimension$1", "publishedRef", "isFirstPublishRef", "useValidation$1", "getRef", "preventHtml5Dnd", "isStrictEqual", "whatIsDraggedOverFromResult", "getSecondarySnapshot", "combineTargetFor", "isClone", "dropAnimation", "draggingOver", "atRest", "snapshot", "ConnectedDraggable", "connect", "draggingSelector", "memoizedOffset", "getMemoizedSnapshot", "getMemoizedProps", "_draggingOver", "_combineWith", "getCombineWithFromResult", "curve", "scale", "getDraggableSelector", "secondarySelector", "get<PERSON>allback", "getProps", "ownId", "draggingId", "visualDisplacement", "isAfterCriticalInVirtualList", "_offset", "getSecondarySelector", "setRef", "_useRequiredContext", "_useRequiredContext2", "dropAnimationFinishedAction", "dragHandleProps", "tabIndex", "role", "onMoveEnd", "draggableProps", "rubric", "PrivateDraggable", "isUsingCloneFor", "PublicDraggable", "isDragDisabled", "disableInteractiveElementBlocking", "isMatchingType", "getDraggable", "defaultProps", "ignoreContainerClipping", "renderClone", "getContainerForClone", "ConnectedDroppable", "idleWithAnimation", "shouldAnimatePlaceholder", "isDraggingOver", "draggingOverWith", "draggingFromThisWith", "isUsingPlaceholder", "useClone", "idleWithoutAnimation", "getDraggableRubric", "getMapProps", "isDraggingOverForConsumer", "isDraggingOverForImpact", "_snapshot", "_completed", "was<PERSON><PERSON><PERSON>", "isHome", "updateViewportMaxScroll", "droppableRef", "placeholder<PERSON><PERSON>", "getDroppableRef", "setDroppableRef", "setPlaceholderRef", "onPlaceholderTransitionEnd", "whileDraggingRef", "previousRef", "publishedDescriptorRef", "memoizedUpdateScroll", "getClosestScroll", "updateScroll", "scheduleScrollUpdate", "onClosestScroll", "removeAttribute", "useDroppablePublisher", "droppableProps", "droppableContext", "node", "draggableProvided", "draggableSnapshot", "ReactDOM", "getClone", "c", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "u", "v", "w", "for", "t", "$$typeof", "exports", "module"], "sourceRoot": ""}