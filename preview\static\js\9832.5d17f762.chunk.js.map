{"version": 3, "file": "static/js/9832.5d17f762.chunk.js", "mappings": "qQAGMA,EAAY,CACd,CACIC,GAAI,EACJ,UAAa,cACb,SAAY,OACZ,QAAW,YACX,OAAU,UACV,SAAY,eAEhB,CACIA,GAAI,EACJ,UAAa,cACb,SAAY,SACZ,QAAW,QACX,OAAU,UACV,SAAY,eAEhB,CACIA,GAAI,EACJ,UAAa,gBACb,SAAY,OACZ,QAAW,WACX,OAAU,UACV,SAAY,eAEhB,CACIA,GAAI,EACJ,UAAa,gBACb,SAAY,QACZ,QAAW,gBACX,OAAU,UACV,SAAY,eAEhB,CACIA,GAAI,EACJ,UAAa,aACb,SAAY,OACZ,QAAW,aACX,OAAU,UACV,SAAY,gBAIpB,SAASC,EAAT,GAAuE,IAAlDC,EAAiD,EAAjDA,UAAWC,EAAsC,EAAtCA,OAAmBC,GAAmB,EAA9BC,UAA8B,EAAnBD,iBASzCE,EARU,CACd,CAAEC,IAAK,KAAMC,MAAO,KACpB,CAAED,IAAK,YAAaC,MAAO,cAC3B,CAAED,IAAK,WAAYC,MAAO,aAC1B,CAAED,IAAK,UAAWC,MAAO,WACzB,CAAED,IAAK,SAAUC,MAAO,UACxB,CAAED,IAAK,WAAYC,MAAO,aAENC,KAAI,YAAqB,IAAlBF,EAAiB,EAAjBA,IAAKC,EAAY,EAAZA,MAC1BE,EAASH,IAAQJ,EACvB,OACE,UAACQ,EAAD,CAEED,OAAQA,EACRE,QAAS,WACHF,GACFN,IAEFF,EAAUK,EACX,EARH,UAUGC,EAVH,KAUU,cAAGK,UAA0C,oBAThDN,EAYV,IACD,OACE,4BACE,wBAAKD,KAGV,CAED,SAASK,EAAT,GAAoD,IAA9BG,EAA6B,EAA7BA,SAAkBF,GAAW,EAAnBF,OAAmB,EAAXE,SACtC,OACE,eACEA,QAASA,EACTG,MAAO,CAAEC,WAAY,IAAKC,OAAQ,WAFpC,SAIGH,GAGN,CAED,SAASI,EAAT,GAA8B,IAATC,EAAQ,EAARA,KACnB,OACE,2BACGA,EAAKV,KAAI,gBAAGT,EAAH,EAAGA,GAAIoB,EAAP,EAAOA,UAAWC,EAAlB,EAAkBA,SAAUC,EAA5B,EAA4BA,QAASC,EAArC,EAAqCA,OAAQC,EAA7C,EAA6CA,SAA7C,OACR,2BACE,wBAAKxB,KACL,wBAAKoB,KACL,wBAAKC,KACL,wBAAKC,KACL,wBAAKC,KACL,wBAAKC,MANExB,EADD,KAYf,CAEI,IAAMyB,EAAb,0IAEI,WACI,OACI,4BACI,gBAAKZ,UAAU,kBAAf,UACI,gBAAKA,UAAU,SAAf,UACI,gBAAKA,UAAU,OAAf,UACI,iBAAKA,UAAU,YAAf,WACI,eAAIA,UAAU,aAAd,mCAGA,SAAC,IAAD,CACIM,KAAMpB,EACN2B,cAAc,KACdC,OAAQ,YAMD,IALHR,EAKE,EALFA,KACAjB,EAIE,EAJFA,UACAC,EAGE,EAHFA,OACAE,EAEE,EAFFA,UACAD,EACE,EADFA,gBAEA,OACA,gBAAKS,UAAU,mBAAf,UACI,mBAAOA,UAAU,QAAjB,WACA,SAACZ,EAAD,CACIC,UAAWA,EACXC,OAAQA,EACRE,UAAWA,EACXD,gBAAiBA,KAErB,SAACc,EAAD,CAAWC,KAAMA,QAIxB,cAOrB,gBAAKN,UAAU,kBAAf,UACI,gBAAKA,UAAU,SAAf,UACI,gBAAKA,UAAU,OAAf,UACI,iBAAKA,UAAU,YAAf,WACI,eAAIA,UAAU,aAAd,mCAGA,SAAC,IAAD,CACIM,KAAMpB,EACN2B,cAAc,KACdC,OAAQ,YAMD,IALHR,EAKE,EALFA,KACAjB,EAIE,EAJFA,UACAC,EAGE,EAHFA,OACAE,EAEE,EAFFA,UACAD,EACE,EADFA,gBAEA,OACA,gBAAKS,UAAU,mBAAf,UACI,mBAAOA,UAAU,sBAAjB,WACA,SAACZ,EAAD,CACIC,UAAWA,EACXC,OAAQA,EACRE,UAAWA,EACXD,gBAAiBA,KAErB,SAACc,EAAD,CAAWC,KAAMA,QAIxB,eAShC,KAjFL,GAAmCS,EAAAA,WAoFnC,W", "sources": ["app/tables/SortableTable.js"], "sourcesContent": ["import React, { Component } from 'react'\nimport Datasort from 'react-data-sort'\n\nconst tableData = [\n    {\n        id: 1,\n        'firstName': '<PERSON>',\n        'lastName': '<PERSON>',\n        'product': 'Photoshop',\n        'amount': '$456.00',\n        'deadline': '12 May 2017'\n    },\n    {\n        id: 2,\n        'firstName': '<PERSON>',\n        'lastName': '<PERSON>',\n        'product': 'Flash',\n        'amount': '$965.00',\n        'deadline': '13 May 2017'\n    },\n    {\n        id: 3,\n        'firstName': '<PERSON>',\n        'lastName': 'Alex',\n        'product': 'Premeire',\n        'amount': '$255.00',\n        'deadline': '14 May 2017'\n    },\n    {\n        id: 4,\n        'firstName': '<PERSON>',\n        'lastName': '<PERSON>',\n        'product': 'After effects',\n        'amount': '$975.00',\n        'deadline': '15 May 2017'\n    },\n    {\n        id: 5,\n        'firstName': 'Messsy max',\n        'lastName': 'Back',\n        'product': 'Ilustrator',\n        'amount': '$298.00',\n        'deadline': '16 May 2017'\n    },\n];\n\nfunction TableHead({ setSortBy, sortBy, direction, toggleDirection }) {\n    const columns = [\n      { key: \"id\", title: \"#\" },\n      { key: \"firstName\", title: \"First Name\" },\n      { key: \"lastName\", title: \"Last Name\" },\n      { key: \"product\", title: \"Product\" },\n      { key: \"amount\", title: \"Amount\" },\n      { key: \"deadline\", title: \"Deadline\" },\n    ];\n    const items = columns.map(({ key, title }) => {\n      const active = key === sortBy;\n      return (\n        <HeadToggle\n          key={key}\n          active={active}\n          onClick={() => {\n            if (active) {\n              toggleDirection();\n            }\n            setSortBy(key);\n          }}\n        >\n          {title} <i className={active ? direction === \"asc\" ? \"ti-angle-down\" : \"ti-angle-down\" : \"ti-angle-down\"}></i>\n        </HeadToggle>\n      );\n    });\n    return (\n      <thead>\n        <tr>{items}</tr>\n      </thead>\n    );\n  }\n  \n  function HeadToggle({ children, active, onClick }) {\n    return (\n      <th\n        onClick={onClick}\n        style={{ fontWeight: 600, cursor: \"pointer\" }}\n      >\n        {children}\n      </th>\n    );\n  }\n  \n  function TableBody({ data }) {\n    return (\n      <tbody>\n        {data.map(({ id, firstName, lastName, product, amount, deadline }) => (\n          <tr key={id}>\n            <td>{id}</td>\n            <td>{firstName}</td>\n            <td>{lastName}</td>\n            <td>{product}</td>\n            <td>{amount}</td>\n            <td>{deadline}</td>\n          </tr>\n        ))}\n      </tbody>\n    );\n  }\n\nexport class SortableTable extends Component {\n    \n    render() {\n        return (\n            <div>\n                <div className=\"row grid-margin\">\n                    <div className=\"col-12\">\n                        <div className=\"card\">\n                            <div className=\"card-body\">\n                                <h4 className=\"card-title\">\n                                Basic Sortable Table\n                                </h4>\n                                <Datasort\n                                    data={tableData}\n                                    defaultSortBy=\"id\"\n                                    render={({\n                                        data,\n                                        setSortBy,\n                                        sortBy,\n                                        direction,\n                                        toggleDirection\n                                    }) => {\n                                        return (\n                                        <div className=\"table-responsive\">\n                                            <table className=\"table\">\n                                            <TableHead\n                                                setSortBy={setSortBy}\n                                                sortBy={sortBy}\n                                                direction={direction}\n                                                toggleDirection={toggleDirection}\n                                            />\n                                            <TableBody data={data} />\n                                            </table>\n                                        </div>\n                                        );\n                                    }}\n                                    />\n\n                            </div>\n                        </div>\n                    </div>\n                </div>\n                <div className=\"row grid-margin\">\n                    <div className=\"col-12\">\n                        <div className=\"card\">\n                            <div className=\"card-body\">\n                                <h4 className=\"card-title\">\n                                Basic Sortable Table\n                                </h4>\n                                <Datasort\n                                    data={tableData}\n                                    defaultSortBy=\"id\"\n                                    render={({\n                                        data,\n                                        setSortBy,\n                                        sortBy,\n                                        direction,\n                                        toggleDirection\n                                    }) => {\n                                        return (\n                                        <div className=\"table-responsive\">\n                                            <table className=\"table table-striped\">\n                                            <TableHead\n                                                setSortBy={setSortBy}\n                                                sortBy={sortBy}\n                                                direction={direction}\n                                                toggleDirection={toggleDirection}\n                                            />\n                                            <TableBody data={data} />\n                                            </table>\n                                        </div>\n                                        );\n                                    }}\n                                    />\n\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        )\n    }\n}\n\nexport default SortableTable\n"], "names": ["tableData", "id", "TableHead", "setSortBy", "sortBy", "toggleDirection", "direction", "items", "key", "title", "map", "active", "HeadToggle", "onClick", "className", "children", "style", "fontWeight", "cursor", "TableBody", "data", "firstName", "lastName", "product", "amount", "deadline", "SortableTable", "defaultSortBy", "render", "Component"], "sourceRoot": ""}