{"version": 3, "file": "static/js/9902.6d942eda.chunk.js", "mappings": "iSAIMA,EAAS,CACf,CACEC,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,MACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,MACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,MACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,GAEV,CACEH,IAAKC,EAAQ,OACbC,MAAO,EACPC,OAAQ,IAIGC,EAAb,0CAEE,WAAYC,GAAQ,IAAD,yBACjB,cAAMA,IACDC,MAAQ,CACXC,aAAc,EACdC,cAAc,GAEhB,EAAKC,aAAe,EAAKA,aAAaC,MAAlB,WACpB,EAAKC,cAAgB,EAAKA,cAAcD,MAAnB,WAPJ,CAQlB,CAVH,2CAYE,SAAaE,EAAb,GAAsC,EAAhBC,MAAiB,IAAVC,EAAS,EAATA,MAC3BC,KAAKC,SAAS,CACZT,aAAcO,EACdN,cAAc,GAEjB,GAjBH,2BAmBE,WACEO,KAAKC,SAAS,CACZT,aAAc,EACdC,cAAc,GAEjB,GAxBH,oBA0BE,WACE,OACE,0BACE,gBAAKS,UAAU,kBAAf,UACE,gBAAKA,UAAU,YAAf,UACE,gBAAKA,UAAU,OAAf,UACE,iBAAKA,UAAU,YAAf,WACE,eAAIA,UAAU,aAAd,4BACA,cAAGA,UAAU,YAAb,+DACA,SAAC,IAAD,CAASlB,OAAQA,EAAQmB,QAASH,KAAKN,gBACrC,SAAC,KAAD,UACGM,KAAKT,MAAME,cACV,SAAC,KAAD,CAAOW,QAASJ,KAAKJ,cAArB,UACE,SAAC,KAAD,CACES,aAAcL,KAAKT,MAAMC,aACzBc,MAAOtB,EAAOuB,KAAI,SAAAC,GAAC,yBACdA,GADc,IAEjBC,OAAQD,EAAEE,OACVC,QAASH,EAAEI,OAHM,QAOrB,iBAQrB,KAxDH,GAAiCC,EAAAA,WA2DjC,W", "sources": ["app/apps/Gallery.js"], "sourcesContent": ["import React, { Component } from 'react';\nimport Gallery from \"react-photo-gallery\";\nimport Carousel, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from \"react-images\";\n\nconst photos = [\n{\n  src: require(\"../../assets/images/samples/1280x768/1.jpg\"),\n  width: 4,\n  height: 3\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/2.jpg\"),\n  width: 1,\n  height: 1\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/3.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/4.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/5.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/6.jpg\"),\n  width: 4,\n  height: 3\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/7.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/8.jpg\"),\n  width: 4,\n  height: 3\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/9.jpg\"),\n  width: 4,\n  height: 3\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/10.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/11.jpg\"),\n  width: 4,\n  height: 3\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/12.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/13.jpg\"),\n  width: 4,\n  height: 3\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/14.jpg\"),\n  width: 3,\n  height: 4\n},\n{\n  src: require(\"../../assets/images/samples/1280x768/15.jpg\"),\n  width: 4,\n  height: 3\n}\n];\n\nexport class GalleryPage extends Component {\n\n  constructor(props) {\n    super(props);\n    this.state = {\n      currentImage: 0,\n      viewerIsOpen: false\n    };\n    this.openLightbox = this.openLightbox.bind(this);\n    this.closeLightbox = this.closeLightbox.bind(this);\n  }\n\n  openLightbox(event, { photo, index }) {\n    this.setState({\n      currentImage: index,\n      viewerIsOpen: true\n    });\n  }\n\n  closeLightbox() {\n    this.setState({\n      currentImage: 0,\n      viewerIsOpen: false\n    });\n  }\n  \n  render() {\n    return (\n      <div>\n        <div className=\"row grid-margin\">\n          <div className=\"col-lg-12\">\n            <div className=\"card\">\n              <div className=\"card-body\">\n                <h4 className=\"card-title\">light Gallery</h4>\n                <p className=\"card-text\"> Click on any image to open in lightbox gallery </p>\n                <Gallery photos={photos} onClick={this.openLightbox} />\n                  <ModalGateway>\n                    {this.state.viewerIsOpen ? (\n                      <Modal onClose={this.closeLightbox}>\n                        <Carousel\n                          currentIndex={this.state.currentImage}\n                          views={photos.map(x => ({\n                            ...x,\n                            srcset: x.srcSet,\n                            caption: x.title\n                          }))}\n                        />\n                      </Modal>\n                    ) : null}\n                  </ModalGateway>\n              </div>\n            </div>\n          </div>\n        </div>\n    </div>\n    )\n  }\n}\n\nexport default GalleryPage\n"], "names": ["photos", "src", "require", "width", "height", "GalleryPage", "props", "state", "currentImage", "viewerIsOpen", "openLightbox", "bind", "closeLightbox", "event", "photo", "index", "this", "setState", "className", "onClick", "onClose", "currentIndex", "views", "map", "x", "srcset", "srcSet", "caption", "title", "Component"], "sourceRoot": ""}