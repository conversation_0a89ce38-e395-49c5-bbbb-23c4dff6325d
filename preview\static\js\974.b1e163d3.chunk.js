/*! For license information please see 974.b1e163d3.chunk.js.LICENSE.txt */
"use strict";(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[974],{31725:function(e){var t=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;function r(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var o={};return"abcdefghijklmnopqrst".split("").forEach((function(e){o[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},o)).join("")}catch(r){return!1}}()?Object.assign:function(e,i){for(var s,u,a=r(e),l=1;l<arguments.length;l++){for(var c in s=Object(arguments[l]))n.call(s,c)&&(a[c]=s[c]);if(t){u=t(s);for(var d=0;d<u.length;d++)o.call(s,u[d])&&(a[u[d]]=s[u[d]])}}return a}},20974:function(e,t,n){n.d(t,{xV:function(){return z},W4:function(){return Q},sN:function(){return N},Wd:function(){return B}});var o=n(72791),r=n(52007),i=n.n(r),s=n(81694),u=n.n(s),a=n(31725),l=n.n(a);function c(e){for(var t=arguments.length,n=Array(t>1?t-1:0),o=1;o<t;o++)n[o-1]=arguments[o];return"function"===typeof e&&e.apply(void 0,n)}function d(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var p="react-contextmenu",f="react-contextmenu--visible",h="react-contextmenu-wrapper",b="react-contextmenu-item",v="react-contextmenu-item--active",m="react-contextmenu-item--disabled",y="react-contextmenu-item--divider",g="react-contextmenu-item--selected",w="react-contextmenu-submenu",M={},O=Boolean("undefined"!==typeof window&&window.document&&window.document.createElement),C="REACT_CONTEXTMENU_SHOW",E="REACT_CONTEXTMENU_HIDE";function k(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:window,o=void 0;"function"===typeof window.CustomEvent?o=new window.CustomEvent(e,{detail:t}):(o=document.createEvent("CustomEvent")).initCustomEvent(e,!1,!0,t),n&&(n.dispatchEvent(o),l()(M,t))}function T(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1];k(C,l()({},e,{type:C}),t)}function j(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments[1];k(E,l()({},e,{type:E}),t)}var S=new function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.handleShowEvent=function(e){for(var n in t.callbacks)d(t.callbacks,n)&&t.callbacks[n].show(e)},this.handleHideEvent=function(e){for(var n in t.callbacks)d(t.callbacks,n)&&t.callbacks[n].hide(e)},this.register=function(e,n){var o=Math.random().toString(36).substring(7);return t.callbacks[o]={show:e,hide:n},o},this.unregister=function(e){e&&t.callbacks[e]&&delete t.callbacks[e]},this.callbacks={},O&&(window.addEventListener(C,this.handleShowEvent),window.addEventListener(E,this.handleHideEvent))},P=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},H=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();function _(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function I(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}var L=function(e){function t(){var e,n,o;x(this,t);for(var r=arguments.length,i=Array(r),s=0;s<r;s++)i[s]=arguments[s];return n=o=I(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i))),o.handleClick=function(e){0!==e.button&&1!==e.button&&e.preventDefault(),o.props.disabled||o.props.divider||(c(o.props.onClick,e,l()({},o.props.data,M.data),M.target),o.props.preventClose||j())},I(o,n)}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),H(t,[{key:"render",value:function(){var e,t=this,n=this.props,r=n.attributes,i=n.children,s=n.className,a=n.disabled,l=n.divider,c=n.selected,d=u()(s,b,r.className,(_(e={},u()(m,r.disabledClassName),a),_(e,u()(y,r.dividerClassName),l),_(e,u()(g,r.selectedClassName),c),e));return o.createElement("div",P({},r,{className:d,role:"menuitem",tabIndex:"-1","aria-disabled":a?"true":"false","aria-orientation":l?"horizontal":null,ref:function(e){t.ref=e},onMouseMove:this.props.onMouseMove,onMouseLeave:this.props.onMouseLeave,onTouchEnd:this.handleClick,onClick:this.handleClick}),l?null:i)}}]),t}(o.Component);L.propTypes={attributes:i().object,children:i().node,className:i().string,data:i().object,disabled:i().bool,divider:i().bool,onClick:i().func,onMouseLeave:i().func,onMouseMove:i().func,preventClose:i().bool,selected:i().bool},L.defaultProps={attributes:{},children:null,className:"",data:{},disabled:!1,divider:!1,onClick:function(){return null},onMouseMove:function(){return null},onMouseLeave:function(){return null},preventClose:!1,selected:!1};var N=L;var R=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return D.call(n),n.seletedItemRef=null,n.state={selectedItem:null,forceSubMenuOpen:!1},n}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),t}(o.Component);R.propTypes={children:i().node.isRequired};var D=function(){var e=this;this.handleKeyNavigation=function(t){if(!1!==e.state.isVisible)switch(t.keyCode){case 37:case 27:t.preventDefault(),e.hideMenu(t);break;case 38:t.preventDefault(),e.selectChildren(!0);break;case 40:t.preventDefault(),e.selectChildren(!1);break;case 39:e.tryToOpenSubMenu(t);break;case 13:t.preventDefault(),e.tryToOpenSubMenu(t);var n=e.seletedItemRef&&e.seletedItemRef.props&&e.seletedItemRef.props.disabled;e.seletedItemRef&&e.seletedItemRef.ref instanceof HTMLElement&&!n?e.seletedItemRef.ref.click():e.hideMenu(t)}},this.handleForceClose=function(){e.setState({forceSubMenuOpen:!1})},this.tryToOpenSubMenu=function(t){e.state.selectedItem&&e.state.selectedItem.type===e.getSubMenuType()&&(t.preventDefault(),e.setState({forceSubMenuOpen:!0}))},this.selectChildren=function(t){var n=e.state.selectedItem,r=[],i=0,s={};if(o.Children.forEach(e.props.children,(function t(n,u){n&&([N,e.getSubMenuType()].indexOf(n.type)<0?o.Children.forEach(n.props.children,t):n.props.divider||(n.props.disabled&&(++i,s[u]=!0),r.push(n)))})),i!==r.length){var u=function(e){var n=e;do{t?--n:++n,n<0?n=r.length-1:n>=r.length&&(n=0)}while(n!==e&&s[n]);return n===e?null:n}(r.indexOf(n));null!==u&&e.setState({selectedItem:r[u],forceSubMenuOpen:!1})}},this.onChildMouseMove=function(t){e.state.selectedItem!==t&&e.setState({selectedItem:t,forceSubMenuOpen:!1})},this.onChildMouseLeave=function(){e.setState({selectedItem:null,forceSubMenuOpen:!1})},this.renderChildren=function(t){return o.Children.map(t,(function(t){var n={};return o.isValidElement(t)?[N,e.getSubMenuType()].indexOf(t.type)<0?(n.children=e.renderChildren(t.props.children),o.cloneElement(t,n)):(n.onMouseLeave=e.onChildMouseLeave.bind(e),t.type===e.getSubMenuType()&&(n.forceOpen=e.state.forceSubMenuOpen&&e.state.selectedItem===t,n.forceClose=e.handleForceClose,n.parentKeyNavigationHandler=e.handleKeyNavigation),t.props.divider||e.state.selectedItem!==t?(n.onMouseMove=function(){return e.onChildMouseMove(t)},o.cloneElement(t,n)):(n.selected=!0,n.ref=function(t){e.seletedItemRef=t},o.cloneElement(t,n))):t}))}},K=R,V=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},q=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();function A(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var U=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.getMenuPosition=function(){var e=window,t=e.innerWidth,o=e.innerHeight,r=n.subMenu.getBoundingClientRect(),i={};return r.bottom>o?i.bottom=0:i.top=0,r.right<t?i.left="100%":i.right="100%",i},n.getRTLMenuPosition=function(){var e=window.innerHeight,t=n.subMenu.getBoundingClientRect(),o={};return t.bottom>e?o.bottom=0:o.top=0,t.left<0?o.left="100%":o.right="100%",o},n.hideSubMenu=function(e){e.detail&&e.detail.id&&n.menu&&e.detail.id!==n.menu.id||(n.props.forceOpen&&n.props.forceClose(),n.setState({visible:!1,selectedItem:null}),n.unregisterHandlers())},n.handleClick=function(e){e.preventDefault(),n.props.disabled||(c(n.props.onClick,e,l()({},n.props.data,M.data),M.target),n.props.onClick&&!n.props.preventCloseOnClick&&j())},n.handleMouseEnter=function(){n.closetimer&&clearTimeout(n.closetimer),n.props.disabled||n.state.visible||(n.opentimer=setTimeout((function(){return n.setState({visible:!0,selectedItem:null})}),n.props.hoverDelay))},n.handleMouseLeave=function(){n.opentimer&&clearTimeout(n.opentimer),n.state.visible&&(n.closetimer=setTimeout((function(){return n.setState({visible:!1,selectedItem:null})}),n.props.hoverDelay))},n.menuRef=function(e){n.menu=e},n.subMenuRef=function(e){n.subMenu=e},n.registerHandlers=function(){document.removeEventListener("keydown",n.props.parentKeyNavigationHandler),document.addEventListener("keydown",n.handleKeyNavigation)},n.unregisterHandlers=function(e){document.removeEventListener("keydown",n.handleKeyNavigation),e||document.addEventListener("keydown",n.props.parentKeyNavigationHandler)},n.state=l()({},n.state,{visible:!1}),n}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),q(t,[{key:"componentDidMount",value:function(){this.listenId=S.register((function(){}),this.hideSubMenu)}},{key:"getSubMenuType",value:function(){return t}},{key:"shouldComponentUpdate",value:function(e,t){return this.isVisibilityChange=(this.state.visible!==t.visible||this.props.forceOpen!==e.forceOpen)&&!(this.state.visible&&e.forceOpen)&&!(this.props.forceOpen&&t.visible),!0}},{key:"componentDidUpdate",value:function(){var e=this;if(this.isVisibilityChange)if(this.props.forceOpen||this.state.visible){(window.requestAnimationFrame||setTimeout)((function(){var t=e.props.rtl?e.getRTLMenuPosition():e.getMenuPosition();e.subMenu.style.removeProperty("top"),e.subMenu.style.removeProperty("bottom"),e.subMenu.style.removeProperty("left"),e.subMenu.style.removeProperty("right"),d(t,"top")&&(e.subMenu.style.top=t.top),d(t,"left")&&(e.subMenu.style.left=t.left),d(t,"bottom")&&(e.subMenu.style.bottom=t.bottom),d(t,"right")&&(e.subMenu.style.right=t.right),e.subMenu.classList.add(f),e.registerHandlers(),e.setState({selectedItem:null})}))}else{this.subMenu.addEventListener("transitionend",(function t(){e.subMenu.removeEventListener("transitionend",t),e.subMenu.style.removeProperty("bottom"),e.subMenu.style.removeProperty("right"),e.subMenu.style.top=0,e.subMenu.style.left="100%",e.unregisterHandlers()})),this.subMenu.classList.remove(f)}}},{key:"componentWillUnmount",value:function(){this.listenId&&S.unregister(this.listenId),this.opentimer&&clearTimeout(this.opentimer),this.closetimer&&clearTimeout(this.closetimer),this.unregisterHandlers(!0)}},{key:"render",value:function(){var e,t=this.props,n=t.children,r=t.attributes,i=t.disabled,s=t.title,a=t.selected,l=this.state.visible,c={ref:this.menuRef,onMouseEnter:this.handleMouseEnter,onMouseLeave:this.handleMouseLeave,className:u()(b,w,r.listClassName),style:{position:"relative"}},d={className:u()(b,r.className,(e={},A(e,u()(m,r.disabledClassName),i),A(e,u()(v,r.visibleClassName),l),A(e,u()(g,r.selectedClassName),a),e)),onMouseMove:this.props.onMouseMove,onMouseOut:this.props.onMouseOut,onClick:this.handleClick},f={ref:this.subMenuRef,style:{position:"absolute",transition:"opacity 1ms",top:0,left:"100%"},className:u()(p,this.props.className)};return o.createElement("nav",V({},c,{role:"menuitem",tabIndex:"-1","aria-haspopup":"true"}),o.createElement("div",V({},r,d),s),o.createElement("nav",V({},f,{role:"menu",tabIndex:"-1"}),this.renderChildren(n)))}}]),t}(K);U.propTypes={children:i().node.isRequired,attributes:i().object,title:i().node.isRequired,className:i().string,disabled:i().bool,hoverDelay:i().number,rtl:i().bool,selected:i().bool,onMouseMove:i().func,onMouseOut:i().func,forceOpen:i().bool,forceClose:i().func,parentKeyNavigationHandler:i().func},U.defaultProps={disabled:!1,hoverDelay:500,attributes:{},className:"",rtl:!1,selected:!1,onMouseMove:function(){return null},onMouseOut:function(){return null},forceOpen:!1,forceClose:function(){return null},parentKeyNavigationHandler:function(){return null}};var B=U,W=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();var X=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.registerHandlers=function(){document.addEventListener("mousedown",n.handleOutsideClick),document.addEventListener("touchstart",n.handleOutsideClick),n.props.preventHideOnScroll||document.addEventListener("scroll",n.handleHide),n.props.preventHideOnContextMenu||document.addEventListener("contextmenu",n.handleHide),document.addEventListener("keydown",n.handleKeyNavigation),n.props.preventHideOnResize||window.addEventListener("resize",n.handleHide)},n.unregisterHandlers=function(){document.removeEventListener("mousedown",n.handleOutsideClick),document.removeEventListener("touchstart",n.handleOutsideClick),document.removeEventListener("scroll",n.handleHide),document.removeEventListener("contextmenu",n.handleHide),document.removeEventListener("keydown",n.handleKeyNavigation),window.removeEventListener("resize",n.handleHide)},n.handleShow=function(e){if(e.detail.id===n.props.id&&!n.state.isVisible){var t=e.detail.position,o=t.x,r=t.y;n.setState({isVisible:!0,x:o,y:r}),n.registerHandlers(),c(n.props.onShow,e)}},n.handleHide=function(e){!n.state.isVisible||e.detail&&e.detail.id&&e.detail.id!==n.props.id||(n.unregisterHandlers(),n.setState({isVisible:!1,selectedItem:null,forceSubMenuOpen:!1}),c(n.props.onHide,e))},n.handleOutsideClick=function(e){n.menu.contains(e.target)||j()},n.handleMouseLeave=function(e){e.preventDefault(),c(n.props.onMouseLeave,e,l()({},n.props.data,M.data),M.target),n.props.hideOnLeave&&j()},n.handleContextMenu=function(e){e.preventDefault(),n.handleHide(e)},n.hideMenu=function(e){27!==e.keyCode&&13!==e.keyCode||j()},n.getMenuPosition=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o={top:t,left:e};if(!n.menu)return o;var r=window,i=r.innerWidth,s=r.innerHeight,u=n.menu.getBoundingClientRect();return t+u.height>s&&(o.top-=u.height),e+u.width>i&&(o.left-=u.width),o.top<0&&(o.top=u.height<s?(s-u.height)/2:0),o.left<0&&(o.left=u.width<i?(i-u.width)/2:0),o},n.getRTLMenuPosition=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o={top:t,left:e};if(!n.menu)return o;var r=window,i=r.innerWidth,s=r.innerHeight,u=n.menu.getBoundingClientRect();return o.left=e-u.width,t+u.height>s&&(o.top-=u.height),o.left<0&&(o.left+=u.width),o.top<0&&(o.top=u.height<s?(s-u.height)/2:0),o.left+u.width>i&&(o.left=u.width<i?(i-u.width)/2:0),o},n.menuRef=function(e){n.menu=e},n.state=l()({},n.state,{x:0,y:0,isVisible:!1}),n}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),W(t,[{key:"getSubMenuType",value:function(){return B}},{key:"componentDidMount",value:function(){this.listenId=S.register(this.handleShow,this.handleHide)}},{key:"componentDidUpdate",value:function(){var e=this,t=window.requestAnimationFrame||setTimeout;this.state.isVisible?t((function(){var n=e.state,o=n.x,r=n.y,i=e.props.rtl?e.getRTLMenuPosition(o,r):e.getMenuPosition(o,r),s=i.top,u=i.left;t((function(){e.menu&&(e.menu.style.top=s+"px",e.menu.style.left=u+"px",e.menu.style.opacity=1,e.menu.style.pointerEvents="auto")}))})):t((function(){e.menu&&(e.menu.style.opacity=0,e.menu.style.pointerEvents="none")}))}},{key:"componentWillUnmount",value:function(){this.listenId&&S.unregister(this.listenId),this.unregisterHandlers()}},{key:"render",value:function(){var e,t,n,r=this.props,i=r.children,s=r.className,a=r.style,c=this.state.isVisible,d=l()({},a,{position:"fixed",opacity:0,pointerEvents:"none"}),h=u()(p,s,(n=c,(t=f)in(e={})?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e));return o.createElement("nav",{role:"menu",tabIndex:"-1",ref:this.menuRef,style:d,className:h,onContextMenu:this.handleContextMenu,onMouseLeave:this.handleMouseLeave},this.renderChildren(i))}}]),t}(K);X.propTypes={id:i().string.isRequired,children:i().node.isRequired,data:i().object,className:i().string,hideOnLeave:i().bool,rtl:i().bool,onHide:i().func,onMouseLeave:i().func,onShow:i().func,preventHideOnContextMenu:i().bool,preventHideOnResize:i().bool,preventHideOnScroll:i().bool,style:i().object},X.defaultProps={className:"",data:{},hideOnLeave:!1,rtl:!1,onHide:function(){return null},onMouseLeave:function(){return null},onShow:function(){return null},preventHideOnContextMenu:!1,preventHideOnResize:!1,preventHideOnScroll:!1,style:{}};var z=X,Y=function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}return function(t,n,o){return n&&e(t.prototype,n),o&&e(t,o),t}}();function F(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function G(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==typeof t&&"function"!==typeof t?e:t}var J=function(e){function t(){var e,n,o;F(this,t);for(var r=arguments.length,i=Array(r),s=0;s<r;s++)i[s]=arguments[s];return n=o=G(this,(e=t.__proto__||Object.getPrototypeOf(t)).call.apply(e,[this].concat(i))),o.touchHandled=!1,o.handleMouseDown=function(e){o.props.holdToDisplay>=0&&0===e.button&&(e.persist(),e.stopPropagation(),o.mouseDownTimeoutId=setTimeout((function(){return o.handleContextClick(e)}),o.props.holdToDisplay)),c(o.props.attributes.onMouseDown,e)},o.handleMouseUp=function(e){0===e.button&&clearTimeout(o.mouseDownTimeoutId),c(o.props.attributes.onMouseUp,e)},o.handleMouseOut=function(e){0===e.button&&clearTimeout(o.mouseDownTimeoutId),c(o.props.attributes.onMouseOut,e)},o.handleTouchstart=function(e){o.touchHandled=!1,o.props.holdToDisplay>=0&&(e.persist(),e.stopPropagation(),o.touchstartTimeoutId=setTimeout((function(){o.handleContextClick(e),o.touchHandled=!0}),o.props.holdToDisplay)),c(o.props.attributes.onTouchStart,e)},o.handleTouchEnd=function(e){o.touchHandled&&e.preventDefault(),clearTimeout(o.touchstartTimeoutId),c(o.props.attributes.onTouchEnd,e)},o.handleContextMenu=function(e){e.button===o.props.mouseButton&&o.handleContextClick(e),c(o.props.attributes.onContextMenu,e)},o.handleMouseClick=function(e){e.button===o.props.mouseButton&&o.handleContextClick(e),c(o.props.attributes.onClick,e)},o.handleContextClick=function(e){if(!o.props.disable&&(!o.props.disableIfShiftIsPressed||!e.shiftKey)){e.preventDefault(),e.stopPropagation();var t=e.clientX||e.touches&&e.touches[0].pageX,n=e.clientY||e.touches&&e.touches[0].pageY;o.props.posX&&(t-=o.props.posX),o.props.posY&&(n-=o.props.posY),j();var r=c(o.props.collect,o.props),i={position:{x:t,y:n},target:o.elem,id:o.props.id};r&&"function"===typeof r.then?r.then((function(t){i.data=l()({},t,{target:e.target}),T(i)})):(i.data=l()({},r,{target:e.target}),T(i))}},o.elemRef=function(e){o.elem=e},G(o,n)}return function(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),Y(t,[{key:"render",value:function(){var e=this.props,t=e.renderTag,n=e.attributes,r=e.children,i=l()({},n,{className:u()(h,n.className),onContextMenu:this.handleContextMenu,onClick:this.handleMouseClick,onMouseDown:this.handleMouseDown,onMouseUp:this.handleMouseUp,onTouchStart:this.handleTouchstart,onTouchEnd:this.handleTouchEnd,onMouseOut:this.handleMouseOut,ref:this.elemRef});return o.createElement(t,i,r)}}]),t}(o.Component);J.propTypes={id:i().string.isRequired,children:i().node.isRequired,attributes:i().object,collect:i().func,disable:i().bool,holdToDisplay:i().number,posX:i().number,posY:i().number,renderTag:i().elementType,mouseButton:i().number,disableIfShiftIsPressed:i().bool},J.defaultProps={attributes:{},collect:function(){return null},disable:!1,holdToDisplay:1e3,renderTag:"div",posX:0,posY:0,mouseButton:2,disableIfShiftIsPressed:!1};var Q=J;Object.assign,function(){function e(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}}();[].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(Object.keys(Q.propTypes)),["children"])}}]);
//# sourceMappingURL=974.b1e163d3.chunk.js.map