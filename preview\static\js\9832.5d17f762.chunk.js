"use strict";(self.webpackChunkstaradmin_react_pro=self.webpackChunkstaradmin_react_pro||[]).push([[9832],{89832:function(e,t,a){a.r(t),a.d(t,{SortableTable:function(){return x}});var r=a(15671),i=a(43144),s=a(60136),n=a(54062),d=a(72791),l=a(52407),c=a(80184),o=[{id:1,firstName:"<PERSON>",lastName:"<PERSON>",product:"Photoshop",amount:"$456.00",deadline:"12 May 2017"},{id:2,firstName:"<PERSON>",lastName:"<PERSON>",product:"<PERSON>",amount:"$965.00",deadline:"13 May 2017"},{id:3,firstName:"<PERSON>",lastName:"<PERSON>",product:"Premeire",amount:"$255.00",deadline:"14 May 2017"},{id:4,firstName:"<PERSON>",lastName:"<PERSON>",product:"After effects",amount:"$975.00",deadline:"15 May 2017"},{id:5,firstName:"Messsy max",lastName:"Back",product:"Ilustrator",amount:"$298.00",deadline:"16 May 2017"}];function m(e){var t=e.setSortBy,a=e.sortBy,r=(e.direction,e.toggleDirection),i=[{key:"id",title:"#"},{key:"firstName",title:"First Name"},{key:"lastName",title:"Last Name"},{key:"product",title:"Product"},{key:"amount",title:"Amount"},{key:"deadline",title:"Deadline"}].map((function(e){var i=e.key,s=e.title,n=i===a;return(0,c.jsxs)(u,{active:n,onClick:function(){n&&r(),t(i)},children:[s," ",(0,c.jsx)("i",{className:"ti-angle-down"})]},i)}));return(0,c.jsx)("thead",{children:(0,c.jsx)("tr",{children:i})})}function u(e){var t=e.children,a=(e.active,e.onClick);return(0,c.jsx)("th",{onClick:a,style:{fontWeight:600,cursor:"pointer"},children:t})}function h(e){var t=e.data;return(0,c.jsx)("tbody",{children:t.map((function(e){var t=e.id,a=e.firstName,r=e.lastName,i=e.product,s=e.amount,n=e.deadline;return(0,c.jsxs)("tr",{children:[(0,c.jsx)("td",{children:t}),(0,c.jsx)("td",{children:a}),(0,c.jsx)("td",{children:r}),(0,c.jsx)("td",{children:i}),(0,c.jsx)("td",{children:s}),(0,c.jsx)("td",{children:n})]},t)}))})}var x=function(e){(0,s.Z)(a,e);var t=(0,n.Z)(a);function a(){return(0,r.Z)(this,a),t.apply(this,arguments)}return(0,i.Z)(a,[{key:"render",value:function(){return(0,c.jsxs)("div",{children:[(0,c.jsx)("div",{className:"row grid-margin",children:(0,c.jsx)("div",{className:"col-12",children:(0,c.jsx)("div",{className:"card",children:(0,c.jsxs)("div",{className:"card-body",children:[(0,c.jsx)("h4",{className:"card-title",children:"Basic Sortable Table"}),(0,c.jsx)(l.Z,{data:o,defaultSortBy:"id",render:function(e){var t=e.data,a=e.setSortBy,r=e.sortBy,i=e.direction,s=e.toggleDirection;return(0,c.jsx)("div",{className:"table-responsive",children:(0,c.jsxs)("table",{className:"table",children:[(0,c.jsx)(m,{setSortBy:a,sortBy:r,direction:i,toggleDirection:s}),(0,c.jsx)(h,{data:t})]})})}})]})})})}),(0,c.jsx)("div",{className:"row grid-margin",children:(0,c.jsx)("div",{className:"col-12",children:(0,c.jsx)("div",{className:"card",children:(0,c.jsxs)("div",{className:"card-body",children:[(0,c.jsx)("h4",{className:"card-title",children:"Basic Sortable Table"}),(0,c.jsx)(l.Z,{data:o,defaultSortBy:"id",render:function(e){var t=e.data,a=e.setSortBy,r=e.sortBy,i=e.direction,s=e.toggleDirection;return(0,c.jsx)("div",{className:"table-responsive",children:(0,c.jsxs)("table",{className:"table table-striped",children:[(0,c.jsx)(m,{setSortBy:a,sortBy:r,direction:i,toggleDirection:s}),(0,c.jsx)(h,{data:t})]})})}})]})})})})]})}}]),a}(d.Component);t.default=x}}]);
//# sourceMappingURL=9832.5d17f762.chunk.js.map